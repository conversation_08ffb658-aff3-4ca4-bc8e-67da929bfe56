# GitLab CI/CD Pipeline for InfoVault Infrastructure - DEV Environment
# Use Case 4: Setup / Manage AWS Infrastructure (DEV)
stages:
  - validate        # Quality Gate 1d - Part 1 (Linting and validation)
  - scan            # Quality Gate 1d - Part 2 (SCA and SAST)
  - test            # Quality Gate 1d - Part 3 (Tests)
  - plan            # Quality Gate 2d (Terraform plan and validation)
  - deploy          # Terraform apply to DEV
  - post-deploy     # Post-deployment validation

workflow:
  rules:
    # Run pipeline for merge requests to develop
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "develop"
      when: always
    # Run quality gate 1d for feature branches
    - if: $CI_COMMIT_BRANCH =~ /^feature\/.*/
      when: always
    # Allow pipeline for direct commits to develop (manual approval will be at job level)
    - if: $CI_COMMIT_BRANCH == "develop"
      when: always
    # Allow pipeline for direct commits to main (for testing and deployment)
    - if: $CI_COMMIT_BRANCH == "main"
      when: always
    # Skip pipeline for all other cases
    - when: never

default:
  tags:
    - infovault
    - local
  before_script:
    - echo "Configuring AWS credentials..."
    # Create .aws directory with proper permissions
    - mkdir -p ~/.aws
    - chmod 700 ~/.aws
    # Export AWS credentials as environment variables
    - export AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
    - export AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
    - export AWS_SESSION_TOKEN=${AWS_SESSION_TOKEN}
    - export AWS_DEFAULT_REGION=${AWS_DEFAULT_REGION}
    # Verify AWS credentials
    - aws sts get-caller-identity || (echo "Failed to authenticate with AWS" && exit 1)

variables:
  TF_ROOT: ${CI_PROJECT_DIR}/environments/dev
  AWS_DEFAULT_REGION: ap-southeast-1
  INFOVAULT_VPC: vpc-05e0e8104b3321c40
  TF_VAR_environment: "dev"
  TF_VAR_backend_bucket: "infovault-terraform-state-dev"
  TF_VAR_backend_key: "infovault/terraform.tfstate"
  TF_VAR_backend_region: ${AWS_DEFAULT_REGION}
  # Terraform variables for S3 bucket configuration
  TF_VAR_bucket_name: "shriyatftryouts"
  TF_VAR_versioning_enabled: "false"
  TF_VAR_encryption_algorithm: "AES256"
  TF_VAR_block_public_acls: "true"
  TF_VAR_block_public_policy: "true"
  TF_VAR_ignore_public_acls: "true"
  TF_VAR_restrict_public_buckets: "true"
  TF_VAR_common_tags: '{"Project":"InfoVault","Environment":"dev","ManagedBy":"Terraform","Owner":"<EMAIL>"}'

cache:
  key: ${CI_COMMIT_REF_SLUG}-${CI_PROJECT_ID}
  paths:
    - ${TF_ROOT}/.terraform
    - ${TF_ROOT}/.terraform.lock.hcl
  policy: pull-push

terraform_lint:
  stage: validate
  script:
    - cd ${TF_ROOT}
    - echo "🔍 Running Terraform format check..."
    - terraform init -backend=false
    - terraform fmt -check -recursive
    - echo "✅ Terraform formatting is correct."
    - echo "🔍 Running Terraform validation..."
    - terraform validate
    - echo "✅ Terraform validation passed."
    - echo "🔍 Installing and running TFLint..."
    # Install TFLint manually to avoid sudo requirements
    - mkdir -p $HOME/bin
    - export PATH="$HOME/bin:$PATH"
    - TFLINT_VERSION=$(curl -s https://api.github.com/repos/terraform-linters/tflint/releases/latest | grep '"tag_name"' | cut -d'"' -f4)
    - curl -L "https://github.com/terraform-linters/tflint/releases/download/${TFLINT_VERSION}/tflint_linux_amd64.zip" -o /tmp/tflint.zip
    - unzip -q /tmp/tflint.zip -d /tmp/
    - mv /tmp/tflint $HOME/bin/
    - chmod +x $HOME/bin/tflint
    - tflint --version
    # Initialize TFLint with default ruleset
    - tflint --init
    # Run TFLint with comprehensive checks
    - tflint --recursive --format=compact --no-color
    - echo "✅ Terraform linting completed successfully."
  allow_failure: false
  # Quality Gate 1d - runs on feature branches, main, and MRs to develop
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "develop"
    - if: $CI_COMMIT_BRANCH =~ /^feature\/.*/
    - if: $CI_COMMIT_BRANCH == "main"

security_scan:
  stage: scan
  script:
    - echo "🛡️ Running infrastructure-as-code security scanning..."
    - tfsec ${TF_ROOT} --no-color || echo "⚠️ Security scan completed with warnings"
    - echo "✅ Security scan completed."
  allow_failure: true
  # Quality Gate 1d - runs on feature branches, main, and MRs to develop
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "develop"
    - if: $CI_COMMIT_BRANCH =~ /^feature\/.*/
    - if: $CI_COMMIT_BRANCH == "main"

dependency_scan:
  stage: scan
  script:
    - echo "📦 Scanning dependencies for vulnerabilities..."
    # Install Trivy manually to avoid sudo requirements
    - mkdir -p $HOME/bin
    - export PATH="$HOME/bin:$PATH"
    - TRIVY_VERSION=$(curl -s https://api.github.com/repos/aquasecurity/trivy/releases/latest | grep '"tag_name"' | cut -d'"' -f4)
    - curl -L "https://github.com/aquasecurity/trivy/releases/download/${TRIVY_VERSION}/trivy_${TRIVY_VERSION#v}_Linux-64bit.tar.gz" -o /tmp/trivy.tar.gz
    - tar -xzf /tmp/trivy.tar.gz -C /tmp/
    - mv /tmp/trivy $HOME/bin/
    - chmod +x $HOME/bin/trivy
    - trivy --version
    # Use updated scanners flag instead of deprecated security-checks
    - trivy fs --scanners vuln,config --skip-policy-update ${TF_ROOT} || echo "⚠️ Dependency scan completed with warnings"
    - echo "✅ Dependency scan completed."
  allow_failure: true
  # Quality Gate 1d - runs on feature branches, main, and MRs to develop
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "develop"
    - if: $CI_COMMIT_BRANCH =~ /^feature\/.*/
    - if: $CI_COMMIT_BRANCH == "main"


terraform_plan:
  stage: plan
  script:
    - cd ${TF_ROOT}
    - echo "🔮 Generating Terraform plan..."
    - terraform init -backend-config="bucket=${TF_VAR_backend_bucket}" -backend-config="key=${TF_VAR_backend_key}" -backend-config="region=${TF_VAR_backend_region}"
    # Create plan with detailed exit code
    - terraform plan -detailed-exitcode -out=tfplan || export TF_EXIT_CODE=$?
    - |
      if [ "${TF_EXIT_CODE}" = "1" ]; then
        echo "❌ Terraform plan failed"
        exit 1
      elif [ "${TF_EXIT_CODE}" = "2" ]; then
        echo "⚠️ Terraform plan has changes"
      else
        echo "✅ Terraform plan has no changes"
      fi
    - terraform show -json tfplan > tfplan.json
    - echo "💾 Plan saved and ready for apply stage"
  artifacts:
    paths:
      - ${TF_ROOT}/tfplan
      - ${TF_ROOT}/tfplan.json
    reports:
      terraform: ${TF_ROOT}/tfplan.json
    expire_in: 1 week
  # Quality Gate 2d - runs on MRs to develop and main branch
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "develop"
    - if: $CI_COMMIT_BRANCH == "main"
  resource_group: ${CI_COMMIT_REF_SLUG}

terraform_apply:
  stage: deploy
  script:
    - cd ${TF_ROOT}
    - echo "🚀 Applying Terraform plan..."
    - terraform init -backend-config="bucket=${TF_VAR_backend_bucket}" -backend-config="key=${TF_VAR_backend_key}" -backend-config="region=${TF_VAR_backend_region}"
    - |
      if [ ! -f tfplan ]; then
        echo "❌ Plan file not found. Ensure terraform_plan job completed successfully."
        exit 1
      fi
    - terraform apply -auto-approve tfplan
    - terraform output -json > terraform_outputs.json
    - echo "✅ Infrastructure deployed successfully"
  artifacts:
    paths:
      - ${TF_ROOT}/terraform_outputs.json
    expire_in: 1 week
  # Deploy only after successful MR to develop or on main branch, with manual approval
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "develop"
      when: manual
      allow_failure: false
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual
      allow_failure: false
  needs:
    - job: terraform_plan
      artifacts: true
  dependencies:
    - terraform_plan
  environment:
    name: dev
    url: https://console.aws.amazon.com/console/home?region=${AWS_DEFAULT_REGION}
  resource_group: ${CI_COMMIT_REF_SLUG}

infrastructure_validation:
  stage: post-deploy
  script:
    - cd ${TF_ROOT}
    - echo "🧪 Validating deployed infrastructure..."
    - |
      if [ ! -f terraform_outputs.json ]; then
        echo "❌ Terraform outputs not found"
        exit 1
      fi
    # Validate S3 bucket deployment
    - |
      BUCKET_ID=$(cat terraform_outputs.json | jq -r '.bucket_id.value // empty')
      if [ -n "$BUCKET_ID" ] && [ "$BUCKET_ID" != "null" ]; then
        echo "🔍 Validating S3 bucket: $BUCKET_ID"
        # Try head-bucket operation with better error handling
        if aws s3api head-bucket --bucket "$BUCKET_ID" 2>/dev/null; then
          echo "✅ S3 bucket validated successfully"
        else
          echo "⚠️ Direct bucket access validation failed (may be due to permissions)"
          # Alternative validation: try to list bucket (less privileged operation)
          if aws s3 ls "s3://$BUCKET_ID" --max-items 1 2>/dev/null >/dev/null; then
            echo "✅ S3 bucket accessible via alternative validation"
          else
            echo "⚠️ S3 bucket validation inconclusive - bucket may exist but access is restricted"
            echo "ℹ️ This is common in secure environments and doesn't indicate deployment failure"
          fi
        fi
      else
        echo "⚠️ No bucket_id found in terraform outputs"
      fi
    - echo "✅ Infrastructure validation complete"
    - echo "📣 Deployment complete - ready for manual functional validation"
  # Run validation only after successful deployment
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "develop"
    - if: $CI_COMMIT_BRANCH == "main"
  needs:
    - job: terraform_apply
      artifacts: true
  dependencies:
    - terraform_apply
  environment:
    name: dev
    url: https://console.aws.amazon.com/console/home?region=${AWS_DEFAULT_REGION}
