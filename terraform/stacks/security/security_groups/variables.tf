# Security Groups Stack Variables
# InfoVault Internet Facing Security Groups Stack

variable "vpc_id" {
  description = "VPC ID where security groups will be created"
  type        = string
}

variable "environment" {
  description = "Environment name (dev, staging, prod)"
  type        = string
}

variable "internet_facing_security_groups" {
  description = "Map of security groups to create for Internet Facing VPC"
  type = map(object({
    name        = string
    description = string
  }))
  default = {}
}

variable "internet_facing_sg_ingress_rules" {
  description = "Map of ingress rules for Internet Facing security groups"
  type = map(object({
    security_group_key       = string
    from_port                = number
    to_port                  = number
    protocol                 = string
    cidr_blocks              = optional(list(string))
    source_security_group_id = optional(string)
    description              = string
  }))
  default = {}
}

variable "internet_facing_sg_egress_rules" {
  description = "Map of egress rules for Internet Facing security groups"
  type = map(object({
    security_group_key            = string
    from_port                     = number
    to_port                       = number
    protocol                      = string
    cidr_blocks                   = optional(list(string))
    destination_security_group_id = optional(string)
    description                   = string
  }))
  default = {}
}

variable "common_tags" {
  description = "Common tags for all resources"
  type        = map(string)
  default     = {}
}
