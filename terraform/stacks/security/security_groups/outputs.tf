# Security Groups Stack Outputs
# InfoVault Internet Facing Security Groups Stack Outputs

output "security_group_ids" {
  description = "Map of security group names to their IDs"
  value       = module.internet_facing_security_groups.security_group_ids
}

output "security_group_arns" {
  description = "Map of security group names to their ARNs"
  value       = module.internet_facing_security_groups.security_group_arns
}

output "security_group_names" {
  description = "Map of security group keys to their names"
  value       = module.internet_facing_security_groups.security_group_names
}

output "proxy_egress_sg_id" {
  description = "Proxy egress security group ID"
  value       = module.internet_facing_security_groups.security_group_ids["proxy_egress"]
}

output "nfw_egress_sg_id" {
  description = "Network firewall egress security group ID"
  value       = module.internet_facing_security_groups.security_group_ids["nfw_egress"]
}
