# Security Groups Stack
# InfoVault Internet Facing Security Groups Stack Implementation

# Call the security groups module
module "internet_facing_security_groups" {
  source = "../../../modules/security/security_groups"

  vpc_id                       = var.vpc_id
  environment                  = var.environment
  security_groups              = var.internet_facing_security_groups
  security_group_ingress_rules = var.internet_facing_sg_ingress_rules
  security_group_egress_rules  = var.internet_facing_sg_egress_rules
  common_tags                  = var.common_tags
}
