terraform {
  required_version = ">= 1.3.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

# EC2 Instances
module "infovault_dev_gitlab_runner" {
  source = "../../../modules/compute/ec2"

  instance_name             = var.infovault_dev_gitlab_runner_name
  ami_id                    = var.infovault_dev_gitlab_runner_ami_id
  instance_type             = var.infovault_dev_gitlab_runner_instance_type
  key_name                  = var.infovault_dev_gitlab_runner_key_name
  security_group_ids        = var.infovault_dev_gitlab_runner_security_group_ids
  subnet_id                 = var.infovault_dev_gitlab_runner_subnet_id
  iam_instance_profile_name = var.infovault_dev_gitlab_runner_iam_instance_profile_name
  availability_zone         = var.infovault_dev_gitlab_runner_availability_zone
  monitoring_enabled        = var.infovault_dev_gitlab_runner_monitoring_enabled
  ebs_optimized             = var.infovault_dev_gitlab_runner_ebs_optimized
  source_dest_check         = var.infovault_dev_gitlab_runner_source_dest_check
  private_ip_address        = var.infovault_dev_gitlab_runner_private_ip_address
  root_block_device         = var.infovault_dev_gitlab_runner_root_block_device
  ebs_block_devices         = var.infovault_dev_gitlab_runner_ebs_block_devices
  user_data                 = var.infovault_dev_gitlab_runner_user_data

  common_tags = var.common_tags
}
module "infovault_dev_linux_tooling_server" {
  source = "../../../modules/compute/ec2"

  instance_name             = var.infovault_dev_linux_tooling_server_name
  ami_id                    = var.infovault_dev_linux_tooling_server_ami_id
  instance_type             = var.infovault_dev_linux_tooling_server_instance_type
  key_name                  = var.infovault_dev_linux_tooling_server_key_name
  security_group_ids        = var.infovault_dev_linux_tooling_server_security_group_ids
  subnet_id                 = var.infovault_dev_linux_tooling_server_subnet_id
  iam_instance_profile_name = var.infovault_dev_linux_tooling_server_iam_instance_profile_name
  availability_zone         = var.infovault_dev_linux_tooling_server_availability_zone
  monitoring_enabled        = var.infovault_dev_linux_tooling_server_monitoring_enabled
  ebs_optimized             = var.infovault_dev_linux_tooling_server_ebs_optimized
  source_dest_check         = var.infovault_dev_linux_tooling_server_source_dest_check
  private_ip_address        = var.infovault_dev_linux_tooling_server_private_ip_address
  root_block_device         = var.infovault_dev_linux_tooling_server_root_block_device
  ebs_block_devices         = var.infovault_dev_linux_tooling_server_ebs_block_devices
  user_data                 = var.infovault_dev_linux_tooling_server_user_data

  common_tags = var.common_tags
}
module "mgmt_newgenadm_win_tooling_01" {
  source = "../../../modules/compute/ec2"

  instance_name             = var.mgmt_newgenadm_win_tooling_01_name
  ami_id                    = var.mgmt_newgenadm_win_tooling_01_ami_id
  instance_type             = var.mgmt_newgenadm_win_tooling_01_instance_type
  key_name                  = var.mgmt_newgenadm_win_tooling_01_key_name
  security_group_ids        = var.mgmt_newgenadm_win_tooling_01_security_group_ids
  subnet_id                 = var.mgmt_newgenadm_win_tooling_01_subnet_id
  iam_instance_profile_name = var.mgmt_newgenadm_win_tooling_01_iam_instance_profile_name
  availability_zone         = var.mgmt_newgenadm_win_tooling_01_availability_zone
  monitoring_enabled        = var.mgmt_newgenadm_win_tooling_01_monitoring_enabled
  ebs_optimized             = var.mgmt_newgenadm_win_tooling_01_ebs_optimized
  source_dest_check         = var.mgmt_newgenadm_win_tooling_01_source_dest_check
  private_ip_address        = var.mgmt_newgenadm_win_tooling_01_private_ip_address
  root_block_device         = var.mgmt_newgenadm_win_tooling_01_root_block_device
  ebs_block_devices         = var.mgmt_newgenadm_win_tooling_01_ebs_block_devices
  user_data                 = var.mgmt_newgenadm_win_tooling_01_user_data

  common_tags = var.common_tags
}
module "dev_management_server_avm150_new" {
  source = "../../../modules/compute/ec2"

  instance_name             = var.dev_management_server_avm150_new_name
  ami_id                    = var.dev_management_server_avm150_new_ami_id
  instance_type             = var.dev_management_server_avm150_new_instance_type
  key_name                  = var.dev_management_server_avm150_new_key_name
  security_group_ids        = var.dev_management_server_avm150_new_security_group_ids
  subnet_id                 = var.dev_management_server_avm150_new_subnet_id
  iam_instance_profile_name = var.dev_management_server_avm150_new_iam_instance_profile_name
  availability_zone         = var.dev_management_server_avm150_new_availability_zone
  monitoring_enabled        = var.dev_management_server_avm150_new_monitoring_enabled
  ebs_optimized             = var.dev_management_server_avm150_new_ebs_optimized
  source_dest_check         = var.dev_management_server_avm150_new_source_dest_check
  private_ip_address        = var.dev_management_server_avm150_new_private_ip_address
  root_block_device         = var.dev_management_server_avm150_new_root_block_device
  ebs_block_devices         = var.dev_management_server_avm150_new_ebs_block_devices
  user_data                 = var.dev_management_server_avm150_new_user_data

  common_tags = var.common_tags
}
module "dev_dra_admin_server_new" {
  source = "../../../modules/compute/ec2"

  instance_name             = var.dev_dra_admin_server_new_name
  ami_id                    = var.dev_dra_admin_server_new_ami_id
  instance_type             = var.dev_dra_admin_server_new_instance_type
  key_name                  = var.dev_dra_admin_server_new_key_name
  security_group_ids        = var.dev_dra_admin_server_new_security_group_ids
  subnet_id                 = var.dev_dra_admin_server_new_subnet_id
  iam_instance_profile_name = var.dev_dra_admin_server_new_iam_instance_profile_name
  availability_zone         = var.dev_dra_admin_server_new_availability_zone
  monitoring_enabled        = var.dev_dra_admin_server_new_monitoring_enabled
  ebs_optimized             = var.dev_dra_admin_server_new_ebs_optimized
  source_dest_check         = var.dev_dra_admin_server_new_source_dest_check
  private_ip_address        = var.dev_dra_admin_server_new_private_ip_address
  root_block_device         = var.dev_dra_admin_server_new_root_block_device
  ebs_block_devices         = var.dev_dra_admin_server_new_ebs_block_devices
  user_data                 = var.dev_dra_admin_server_new_user_data

  common_tags = var.common_tags
}
module "dev_dra_analytics_server_new" {
  source = "../../../modules/compute/ec2"

  instance_name             = var.dev_dra_analytics_server_new_name
  ami_id                    = var.dev_dra_analytics_server_new_ami_id
  instance_type             = var.dev_dra_analytics_server_new_instance_type
  key_name                  = var.dev_dra_analytics_server_new_key_name
  security_group_ids        = var.dev_dra_analytics_server_new_security_group_ids
  subnet_id                 = var.dev_dra_analytics_server_new_subnet_id
  iam_instance_profile_name = var.dev_dra_analytics_server_new_iam_instance_profile_name
  availability_zone         = var.dev_dra_analytics_server_new_availability_zone
  monitoring_enabled        = var.dev_dra_analytics_server_new_monitoring_enabled
  ebs_optimized             = var.dev_dra_analytics_server_new_ebs_optimized
  source_dest_check         = var.dev_dra_analytics_server_new_source_dest_check
  private_ip_address        = var.dev_dra_analytics_server_new_private_ip_address
  root_block_device         = var.dev_dra_analytics_server_new_root_block_device
  ebs_block_devices         = var.dev_dra_analytics_server_new_ebs_block_devices
  user_data                 = var.dev_dra_analytics_server_new_user_data

  common_tags = var.common_tags
}
module "dev_management_server_avm150_imperva" {
  source = "../../../modules/compute/ec2"

  instance_name             = var.dev_management_server_avm150_imperva_name
  ami_id                    = var.dev_management_server_avm150_imperva_ami_id
  instance_type             = var.dev_management_server_avm150_imperva_instance_type
  key_name                  = var.dev_management_server_avm150_imperva_key_name
  security_group_ids        = var.dev_management_server_avm150_imperva_security_group_ids
  subnet_id                 = var.dev_management_server_avm150_imperva_subnet_id
  iam_instance_profile_name = var.dev_management_server_avm150_imperva_iam_instance_profile_name
  availability_zone         = var.dev_management_server_avm150_imperva_availability_zone
  monitoring_enabled        = var.dev_management_server_avm150_imperva_monitoring_enabled
  ebs_optimized             = var.dev_management_server_avm150_imperva_ebs_optimized
  source_dest_check         = var.dev_management_server_avm150_imperva_source_dest_check
  private_ip_address        = var.dev_management_server_avm150_imperva_private_ip_address
  root_block_device         = var.dev_management_server_avm150_imperva_root_block_device
  ebs_block_devices         = var.dev_management_server_avm150_imperva_ebs_block_devices
  user_data                 = var.dev_management_server_avm150_imperva_user_data

  common_tags = var.common_tags
}
module "ad_tooling_windows_02" {
  source = "../../../modules/compute/ec2"

  instance_name             = var.ad_tooling_windows_02_name
  ami_id                    = var.ad_tooling_windows_02_ami_id
  instance_type             = var.ad_tooling_windows_02_instance_type
  key_name                  = var.ad_tooling_windows_02_key_name
  security_group_ids        = var.ad_tooling_windows_02_security_group_ids
  subnet_id                 = var.ad_tooling_windows_02_subnet_id
  iam_instance_profile_name = var.ad_tooling_windows_02_iam_instance_profile_name
  availability_zone         = var.ad_tooling_windows_02_availability_zone
  monitoring_enabled        = var.ad_tooling_windows_02_monitoring_enabled
  ebs_optimized             = var.ad_tooling_windows_02_ebs_optimized
  source_dest_check         = var.ad_tooling_windows_02_source_dest_check
  private_ip_address        = var.ad_tooling_windows_02_private_ip_address
  root_block_device         = var.ad_tooling_windows_02_root_block_device
  ebs_block_devices         = var.ad_tooling_windows_02_ebs_block_devices
  user_data                 = var.ad_tooling_windows_02_user_data

  common_tags = var.common_tags
}

module "infovault_dev_marvin_ai_02" {
  source = "../../../modules/compute/ec2"

  instance_name             = var.infovault_dev_marvin_ai_02_name
  ami_id                    = var.infovault_dev_marvin_ai_02_ami_id
  instance_type             = var.infovault_dev_marvin_ai_02_instance_type
  key_name                  = var.infovault_dev_marvin_ai_02_key_name
  security_group_ids        = var.infovault_dev_marvin_ai_02_security_group_ids
  subnet_id                 = var.infovault_dev_marvin_ai_02_subnet_id
  iam_instance_profile_name = var.infovault_dev_marvin_ai_02_iam_instance_profile_name
  availability_zone         = var.infovault_dev_marvin_ai_02_availability_zone
  monitoring_enabled        = var.infovault_dev_marvin_ai_02_monitoring_enabled
  ebs_optimized             = var.infovault_dev_marvin_ai_02_ebs_optimized
  source_dest_check         = var.infovault_dev_marvin_ai_02_source_dest_check
  private_ip_address        = var.infovault_dev_marvin_ai_02_private_ip_address
  root_block_device         = var.infovault_dev_marvin_ai_02_root_block_device
  ebs_block_devices         = var.infovault_dev_marvin_ai_02_ebs_block_devices
  user_data                 = var.infovault_dev_marvin_ai_02_user_data

  common_tags = var.common_tags
}
