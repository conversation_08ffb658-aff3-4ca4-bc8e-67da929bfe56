variable "common_tags" {
  description = "Common tags for all resources"
  type        = map(string)
  default     = {}
}

# Variables for infovault-dev-gitlab-runner
variable "infovault_dev_gitlab_runner_name" {
  description = "Name of the infovault-dev-gitlab-runner instance"
  type        = string
}

variable "infovault_dev_gitlab_runner_ami_id" {
  description = "AMI ID for infovault-dev-gitlab-runner"
  type        = string
}

variable "infovault_dev_gitlab_runner_instance_type" {
  description = "Instance type for infovault-dev-gitlab-runner"
  type        = string
}

variable "infovault_dev_gitlab_runner_key_name" {
  description = "Key pair name for infovault-dev-gitlab-runner"
  type        = string
  default     = null
}

variable "infovault_dev_gitlab_runner_security_group_ids" {
  description = "Security group IDs for infovault-dev-gitlab-runner"
  type        = list(string)
}

variable "infovault_dev_gitlab_runner_subnet_id" {
  description = "Subnet ID for infovault-dev-gitlab-runner"
  type        = string
}

variable "infovault_dev_gitlab_runner_iam_instance_profile_name" {
  description = "IAM instance profile name for infovault-dev-gitlab-runner"
  type        = string
  default     = null
}

variable "infovault_dev_gitlab_runner_availability_zone" {
  description = "Availability zone for infovault-dev-gitlab-runner"
  type        = string
}

variable "infovault_dev_gitlab_runner_monitoring_enabled" {
  description = "Enable monitoring for infovault-dev-gitlab-runner"
  type        = bool
  default     = false
}

variable "infovault_dev_gitlab_runner_ebs_optimized" {
  description = "Enable EBS optimization for infovault-dev-gitlab-runner"
  type        = bool
  default     = false
}

variable "infovault_dev_gitlab_runner_source_dest_check" {
  description = "Enable source destination check for infovault-dev-gitlab-runner"
  type        = bool
  default     = true
}

variable "infovault_dev_gitlab_runner_private_ip_address" {
  description = "Private IP address for infovault-dev-gitlab-runner"
  type        = string
  default     = null
}

variable "infovault_dev_gitlab_runner_root_block_device" {
  description = "Root block device configuration for infovault-dev-gitlab-runner"
  type = object({
    volume_type           = string
    volume_size           = number
    delete_on_termination = bool
    encrypted             = bool
  })
  default = null
}

variable "infovault_dev_gitlab_runner_ebs_block_devices" {
  description = "Additional EBS block devices for infovault-dev-gitlab-runner"
  type = list(object({
    device_name           = string
    volume_type           = string
    volume_size           = number
    delete_on_termination = bool
    encrypted             = bool
  }))
  default = []
}

variable "infovault_dev_gitlab_runner_user_data" {
  description = "User data script for infovault-dev-gitlab-runner"
  type        = string
  default     = null
}
# Variables for infovault-dev-linux-tooling-server
variable "infovault_dev_linux_tooling_server_name" {
  description = "Name of the infovault-dev-linux-tooling-server instance"
  type        = string
}

variable "infovault_dev_linux_tooling_server_ami_id" {
  description = "AMI ID for infovault-dev-linux-tooling-server"
  type        = string
}

variable "infovault_dev_linux_tooling_server_instance_type" {
  description = "Instance type for infovault-dev-linux-tooling-server"
  type        = string
}

variable "infovault_dev_linux_tooling_server_key_name" {
  description = "Key pair name for infovault-dev-linux-tooling-server"
  type        = string
  default     = null
}

variable "infovault_dev_linux_tooling_server_security_group_ids" {
  description = "Security group IDs for infovault-dev-linux-tooling-server"
  type        = list(string)
}

variable "infovault_dev_linux_tooling_server_subnet_id" {
  description = "Subnet ID for infovault-dev-linux-tooling-server"
  type        = string
}

variable "infovault_dev_linux_tooling_server_iam_instance_profile_name" {
  description = "IAM instance profile name for infovault-dev-linux-tooling-server"
  type        = string
  default     = null
}

variable "infovault_dev_linux_tooling_server_availability_zone" {
  description = "Availability zone for infovault-dev-linux-tooling-server"
  type        = string
}

variable "infovault_dev_linux_tooling_server_monitoring_enabled" {
  description = "Enable monitoring for infovault-dev-linux-tooling-server"
  type        = bool
  default     = false
}

variable "infovault_dev_linux_tooling_server_ebs_optimized" {
  description = "Enable EBS optimization for infovault-dev-linux-tooling-server"
  type        = bool
  default     = false
}

variable "infovault_dev_linux_tooling_server_source_dest_check" {
  description = "Enable source destination check for infovault-dev-linux-tooling-server"
  type        = bool
  default     = true
}

variable "infovault_dev_linux_tooling_server_private_ip_address" {
  description = "Private IP address for infovault-dev-linux-tooling-server"
  type        = string
  default     = null
}

variable "infovault_dev_linux_tooling_server_root_block_device" {
  description = "Root block device configuration for infovault-dev-linux-tooling-server"
  type = object({
    volume_type           = string
    volume_size           = number
    delete_on_termination = bool
    encrypted             = bool
  })
  default = null
}

variable "infovault_dev_linux_tooling_server_ebs_block_devices" {
  description = "Additional EBS block devices for infovault-dev-linux-tooling-server"
  type = list(object({
    device_name           = string
    volume_type           = string
    volume_size           = number
    delete_on_termination = bool
    encrypted             = bool
  }))
  default = []
}

variable "infovault_dev_linux_tooling_server_user_data" {
  description = "User data script for infovault-dev-linux-tooling-server"
  type        = string
  default     = null
}

# Variables for mgmt-newgenadm-win-tooling-01
variable "mgmt_newgenadm_win_tooling_01_name" {
  description = "Name of the mgmt-newgenadm-win-tooling-01 instance"
  type        = string
}

variable "mgmt_newgenadm_win_tooling_01_ami_id" {
  description = "AMI ID for mgmt-newgenadm-win-tooling-01"
  type        = string
}

variable "mgmt_newgenadm_win_tooling_01_instance_type" {
  description = "Instance type for mgmt-newgenadm-win-tooling-01"
  type        = string
}

variable "mgmt_newgenadm_win_tooling_01_key_name" {
  description = "Key pair name for mgmt-newgenadm-win-tooling-01"
  type        = string
  default     = null
}

variable "mgmt_newgenadm_win_tooling_01_security_group_ids" {
  description = "Security group IDs for mgmt-newgenadm-win-tooling-01"
  type        = list(string)
}

variable "mgmt_newgenadm_win_tooling_01_subnet_id" {
  description = "Subnet ID for mgmt-newgenadm-win-tooling-01"
  type        = string
}

variable "mgmt_newgenadm_win_tooling_01_iam_instance_profile_name" {
  description = "IAM instance profile name for mgmt-newgenadm-win-tooling-01"
  type        = string
  default     = null
}

variable "mgmt_newgenadm_win_tooling_01_availability_zone" {
  description = "Availability zone for mgmt-newgenadm-win-tooling-01"
  type        = string
}

variable "mgmt_newgenadm_win_tooling_01_monitoring_enabled" {
  description = "Enable monitoring for mgmt-newgenadm-win-tooling-01"
  type        = bool
  default     = false
}

variable "mgmt_newgenadm_win_tooling_01_ebs_optimized" {
  description = "Enable EBS optimization for mgmt-newgenadm-win-tooling-01"
  type        = bool
  default     = false
}

variable "mgmt_newgenadm_win_tooling_01_source_dest_check" {
  description = "Enable source destination check for mgmt-newgenadm-win-tooling-01"
  type        = bool
  default     = true
}

variable "mgmt_newgenadm_win_tooling_01_private_ip_address" {
  description = "Private IP address for mgmt-newgenadm-win-tooling-01"
  type        = string
  default     = null
}

variable "mgmt_newgenadm_win_tooling_01_root_block_device" {
  description = "Root block device configuration for mgmt-newgenadm-win-tooling-01"
  type = object({
    volume_type           = string
    volume_size           = number
    delete_on_termination = bool
    encrypted             = bool
  })
  default = null
}

variable "mgmt_newgenadm_win_tooling_01_ebs_block_devices" {
  description = "Additional EBS block devices for mgmt-newgenadm-win-tooling-01"
  type = list(object({
    device_name           = string
    volume_type           = string
    volume_size           = number
    delete_on_termination = bool
    encrypted             = bool
  }))
  default = []
}

variable "mgmt_newgenadm_win_tooling_01_user_data" {
  description = "User data script for mgmt-newgenadm-win-tooling-01"
  type        = string
  default     = null
}

# Variables for dev-management-server-avm150-new
variable "dev_management_server_avm150_new_name" {
  description = "Name of the dev-management-server-avm150-new instance"
  type        = string
}

variable "dev_management_server_avm150_new_ami_id" {
  description = "AMI ID for dev-management-server-avm150-new"
  type        = string
}

variable "dev_management_server_avm150_new_instance_type" {
  description = "Instance type for dev-management-server-avm150-new"
  type        = string
}

variable "dev_management_server_avm150_new_key_name" {
  description = "Key pair name for dev-management-server-avm150-new"
  type        = string
  default     = null
}

variable "dev_management_server_avm150_new_security_group_ids" {
  description = "Security group IDs for dev-management-server-avm150-new"
  type        = list(string)
}

variable "dev_management_server_avm150_new_subnet_id" {
  description = "Subnet ID for dev-management-server-avm150-new"
  type        = string
}

variable "dev_management_server_avm150_new_iam_instance_profile_name" {
  description = "IAM instance profile name for dev-management-server-avm150-new"
  type        = string
  default     = null
}

variable "dev_management_server_avm150_new_availability_zone" {
  description = "Availability zone for dev-management-server-avm150-new"
  type        = string
}

variable "dev_management_server_avm150_new_monitoring_enabled" {
  description = "Enable monitoring for dev-management-server-avm150-new"
  type        = bool
  default     = false
}

variable "dev_management_server_avm150_new_ebs_optimized" {
  description = "Enable EBS optimization for dev-management-server-avm150-new"
  type        = bool
  default     = false
}

variable "dev_management_server_avm150_new_source_dest_check" {
  description = "Enable source destination check for dev-management-server-avm150-new"
  type        = bool
  default     = true
}

variable "dev_management_server_avm150_new_private_ip_address" {
  description = "Private IP address for dev-management-server-avm150-new"
  type        = string
  default     = null
}

variable "dev_management_server_avm150_new_root_block_device" {
  description = "Root block device configuration for dev-management-server-avm150-new"
  type = object({
    volume_type           = string
    volume_size           = number
    delete_on_termination = bool
    encrypted             = bool
  })
  default = null
}

variable "dev_management_server_avm150_new_ebs_block_devices" {
  description = "Additional EBS block devices for dev-management-server-avm150-new"
  type = list(object({
    device_name           = string
    volume_type           = string
    volume_size           = number
    delete_on_termination = bool
    encrypted             = bool
  }))
  default = []
}

variable "dev_management_server_avm150_new_user_data" {
  description = "User data script for dev-management-server-avm150-new"
  type        = string
  default     = null
}

# Variables for dev-dra-admin-server-new
variable "dev_dra_admin_server_new_name" {
  description = "Name of the dev-dra-admin-server-new instance"
  type        = string
}

variable "dev_dra_admin_server_new_ami_id" {
  description = "AMI ID for dev-dra-admin-server-new"
  type        = string
}

variable "dev_dra_admin_server_new_instance_type" {
  description = "Instance type for dev-dra-admin-server-new"
  type        = string
}

variable "dev_dra_admin_server_new_key_name" {
  description = "Key pair name for dev-dra-admin-server-new"
  type        = string
  default     = null
}

variable "dev_dra_admin_server_new_security_group_ids" {
  description = "Security group IDs for dev-dra-admin-server-new"
  type        = list(string)
}

variable "dev_dra_admin_server_new_subnet_id" {
  description = "Subnet ID for dev-dra-admin-server-new"
  type        = string
}

variable "dev_dra_admin_server_new_iam_instance_profile_name" {
  description = "IAM instance profile name for dev-dra-admin-server-new"
  type        = string
  default     = null
}

variable "dev_dra_admin_server_new_availability_zone" {
  description = "Availability zone for dev-dra-admin-server-new"
  type        = string
}

variable "dev_dra_admin_server_new_monitoring_enabled" {
  description = "Enable monitoring for dev-dra-admin-server-new"
  type        = bool
  default     = false
}

variable "dev_dra_admin_server_new_ebs_optimized" {
  description = "Enable EBS optimization for dev-dra-admin-server-new"
  type        = bool
  default     = false
}

variable "dev_dra_admin_server_new_source_dest_check" {
  description = "Enable source destination check for dev-dra-admin-server-new"
  type        = bool
  default     = true
}

variable "dev_dra_admin_server_new_private_ip_address" {
  description = "Private IP address for dev-dra-admin-server-new"
  type        = string
  default     = null
}

variable "dev_dra_admin_server_new_root_block_device" {
  description = "Root block device configuration for dev-dra-admin-server-new"
  type = object({
    volume_type           = string
    volume_size           = number
    delete_on_termination = bool
    encrypted             = bool
  })
  default = null
}

variable "dev_dra_admin_server_new_ebs_block_devices" {
  description = "Additional EBS block devices for dev-dra-admin-server-new"
  type = list(object({
    device_name           = string
    volume_type           = string
    volume_size           = number
    delete_on_termination = bool
    encrypted             = bool
  }))
  default = []
}

variable "dev_dra_admin_server_new_user_data" {
  description = "User data script for dev-dra-admin-server-new"
  type        = string
  default     = null
}

# Variables for dev-dra-analytics-server-new
variable "dev_dra_analytics_server_new_name" {
  description = "Name of the dev-dra-analytics-server-new instance"
  type        = string
}

variable "dev_dra_analytics_server_new_ami_id" {
  description = "AMI ID for dev-dra-analytics-server-new"
  type        = string
}

variable "dev_dra_analytics_server_new_instance_type" {
  description = "Instance type for dev-dra-analytics-server-new"
  type        = string
}

variable "dev_dra_analytics_server_new_key_name" {
  description = "Key pair name for dev-dra-analytics-server-new"
  type        = string
  default     = null
}

variable "dev_dra_analytics_server_new_security_group_ids" {
  description = "Security group IDs for dev-dra-analytics-server-new"
  type        = list(string)
}

variable "dev_dra_analytics_server_new_subnet_id" {
  description = "Subnet ID for dev-dra-analytics-server-new"
  type        = string
}

variable "dev_dra_analytics_server_new_iam_instance_profile_name" {
  description = "IAM instance profile name for dev-dra-analytics-server-new"
  type        = string
  default     = null
}

variable "dev_dra_analytics_server_new_availability_zone" {
  description = "Availability zone for dev-dra-analytics-server-new"
  type        = string
}

variable "dev_dra_analytics_server_new_monitoring_enabled" {
  description = "Enable monitoring for dev-dra-analytics-server-new"
  type        = bool
  default     = false
}

variable "dev_dra_analytics_server_new_ebs_optimized" {
  description = "Enable EBS optimization for dev-dra-analytics-server-new"
  type        = bool
  default     = false
}

variable "dev_dra_analytics_server_new_source_dest_check" {
  description = "Enable source destination check for dev-dra-analytics-server-new"
  type        = bool
  default     = true
}

variable "dev_dra_analytics_server_new_private_ip_address" {
  description = "Private IP address for dev-dra-analytics-server-new"
  type        = string
  default     = null
}

variable "dev_dra_analytics_server_new_root_block_device" {
  description = "Root block device configuration for dev-dra-analytics-server-new"
  type = object({
    volume_type           = string
    volume_size           = number
    delete_on_termination = bool
    encrypted             = bool
  })
  default = null
}

variable "dev_dra_analytics_server_new_ebs_block_devices" {
  description = "Additional EBS block devices for dev-dra-analytics-server-new"
  type = list(object({
    device_name           = string
    volume_type           = string
    volume_size           = number
    delete_on_termination = bool
    encrypted             = bool
  }))
  default = []
}

variable "dev_dra_analytics_server_new_user_data" {
  description = "User data script for dev-dra-analytics-server-new"
  type        = string
  default     = null
}

# Variables for dev-management-server-avm150-imperva
variable "dev_management_server_avm150_imperva_name" {
  description = "Name of the dev-management-server-avm150-imperva instance"
  type        = string
}

variable "dev_management_server_avm150_imperva_ami_id" {
  description = "AMI ID for dev-management-server-avm150-imperva"
  type        = string
}

variable "dev_management_server_avm150_imperva_instance_type" {
  description = "Instance type for dev-management-server-avm150-imperva"
  type        = string
}

variable "dev_management_server_avm150_imperva_key_name" {
  description = "Key pair name for dev-management-server-avm150-imperva"
  type        = string
  default     = null
}

variable "dev_management_server_avm150_imperva_security_group_ids" {
  description = "Security group IDs for dev-management-server-avm150-imperva"
  type        = list(string)
}

variable "dev_management_server_avm150_imperva_subnet_id" {
  description = "Subnet ID for dev-management-server-avm150-imperva"
  type        = string
}

variable "dev_management_server_avm150_imperva_iam_instance_profile_name" {
  description = "IAM instance profile name for dev-management-server-avm150-imperva"
  type        = string
  default     = null
}

variable "dev_management_server_avm150_imperva_availability_zone" {
  description = "Availability zone for dev-management-server-avm150-imperva"
  type        = string
}

variable "dev_management_server_avm150_imperva_monitoring_enabled" {
  description = "Enable monitoring for dev-management-server-avm150-imperva"
  type        = bool
  default     = false
}

variable "dev_management_server_avm150_imperva_ebs_optimized" {
  description = "Enable EBS optimization for dev-management-server-avm150-imperva"
  type        = bool
  default     = false
}

variable "dev_management_server_avm150_imperva_source_dest_check" {
  description = "Enable source destination check for dev-management-server-avm150-imperva"
  type        = bool
  default     = true
}

variable "dev_management_server_avm150_imperva_private_ip_address" {
  description = "Private IP address for dev-management-server-avm150-imperva"
  type        = string
  default     = null
}

variable "dev_management_server_avm150_imperva_root_block_device" {
  description = "Root block device configuration for dev-management-server-avm150-imperva"
  type = object({
    volume_type           = string
    volume_size           = number
    delete_on_termination = bool
    encrypted             = bool
  })
  default = null
}

variable "dev_management_server_avm150_imperva_ebs_block_devices" {
  description = "Additional EBS block devices for dev-management-server-avm150-imperva"
  type = list(object({
    device_name           = string
    volume_type           = string
    volume_size           = number
    delete_on_termination = bool
    encrypted             = bool
  }))
  default = []
}

variable "dev_management_server_avm150_imperva_user_data" {
  description = "User data script for dev-management-server-avm150-imperva"
  type        = string
  default     = null
}

# Variables for ad-tooling-windows-02
variable "ad_tooling_windows_02_name" {
  description = "Name of the ad-tooling-windows-02 instance"
  type        = string
}

variable "ad_tooling_windows_02_ami_id" {
  description = "AMI ID for ad-tooling-windows-02"
  type        = string
}

variable "ad_tooling_windows_02_instance_type" {
  description = "Instance type for ad-tooling-windows-02"
  type        = string
}

variable "ad_tooling_windows_02_key_name" {
  description = "Key pair name for ad-tooling-windows-02"
  type        = string
  default     = null
}

variable "ad_tooling_windows_02_security_group_ids" {
  description = "Security group IDs for ad-tooling-windows-02"
  type        = list(string)
}

variable "ad_tooling_windows_02_subnet_id" {
  description = "Subnet ID for ad-tooling-windows-02"
  type        = string
}

variable "ad_tooling_windows_02_iam_instance_profile_name" {
  description = "IAM instance profile name for ad-tooling-windows-02"
  type        = string
  default     = null
}

variable "ad_tooling_windows_02_availability_zone" {
  description = "Availability zone for ad-tooling-windows-02"
  type        = string
}

variable "ad_tooling_windows_02_monitoring_enabled" {
  description = "Enable monitoring for ad-tooling-windows-02"
  type        = bool
  default     = false
}

variable "ad_tooling_windows_02_ebs_optimized" {
  description = "Enable EBS optimization for ad-tooling-windows-02"
  type        = bool
  default     = false
}

variable "ad_tooling_windows_02_source_dest_check" {
  description = "Enable source destination check for ad-tooling-windows-02"
  type        = bool
  default     = true
}

variable "ad_tooling_windows_02_private_ip_address" {
  description = "Private IP address for ad-tooling-windows-02"
  type        = string
  default     = null
}

variable "ad_tooling_windows_02_root_block_device" {
  description = "Root block device configuration for ad-tooling-windows-02"
  type = object({
    volume_type           = string
    volume_size           = number
    delete_on_termination = bool
    encrypted             = bool
  })
  default = null
}

variable "ad_tooling_windows_02_ebs_block_devices" {
  description = "Additional EBS block devices for ad-tooling-windows-02"
  type = list(object({
    device_name           = string
    volume_type           = string
    volume_size           = number
    delete_on_termination = bool
    encrypted             = bool
  }))
  default = []
}

variable "ad_tooling_windows_02_user_data" {
  description = "User data script for ad-tooling-windows-02"
  type        = string
  default     = null
}
