output "infovault_dev_gitlab_runner_instance_id" {
  description = "ID of the infovault-dev-gitlab-runner instance"
  value       = module.infovault_dev_gitlab_runner.instance_id
}

output "infovault_dev_gitlab_runner_private_ip" {
  description = "Private IP of the infovault-dev-gitlab-runner instance"
  value       = module.infovault_dev_gitlab_runner.private_ip
}

output "infovault_dev_linux_tooling_server_instance_id" {
  description = "ID of the infovault-dev-linux-tooling-server instance"
  value       = module.infovault_dev_linux_tooling_server.instance_id
}

output "infovault_dev_linux_tooling_server_private_ip" {
  description = "Private IP of the infovault-dev-linux-tooling-server instance"
  value       = module.infovault_dev_linux_tooling_server.private_ip
}

output "mgmt_newgenadm_win_tooling_01_instance_id" {
  description = "ID of the mgmt-newgenadm-win-tooling-01 instance"
  value       = module.mgmt_newgenadm_win_tooling_01.instance_id
}

output "mgmt_newgenadm_win_tooling_01_private_ip" {
  description = "Private IP of the mgmt-newgenadm-win-tooling-01 instance"
  value       = module.mgmt_newgenadm_win_tooling_01.private_ip
}

output "dev_management_server_avm150_new_instance_id" {
  description = "ID of the dev-management-server-avm150-new instance"
  value       = module.dev_management_server_avm150_new.instance_id
}

output "dev_management_server_avm150_new_private_ip" {
  description = "Private IP of the dev-management-server-avm150-new instance"
  value       = module.dev_management_server_avm150_new.private_ip
}

output "dev_dra_admin_server_new_instance_id" {
  description = "ID of the dev-dra-admin-server-new instance"
  value       = module.dev_dra_admin_server_new.instance_id
}

output "dev_dra_admin_server_new_private_ip" {
  description = "Private IP of the dev-dra-admin-server-new instance"
  value       = module.dev_dra_admin_server_new.private_ip
}

output "dev_dra_analytics_server_new_instance_id" {
  description = "ID of the dev-dra-analytics-server-new instance"
  value       = module.dev_dra_analytics_server_new.instance_id
}

output "dev_dra_analytics_server_new_private_ip" {
  description = "Private IP of the dev-dra-analytics-server-new instance"
  value       = module.dev_dra_analytics_server_new.private_ip
}

output "dev_management_server_avm150_imperva_instance_id" {
  description = "ID of the dev-management-server-avm150-imperva instance"
  value       = module.dev_management_server_avm150_imperva.instance_id
}

output "dev_management_server_avm150_imperva_private_ip" {
  description = "Private IP of the dev-management-server-avm150-imperva instance"
  value       = module.dev_management_server_avm150_imperva.private_ip
}

output "ad_tooling_windows_02_instance_id" {
  description = "ID of the ad-tooling-windows-02 instance"
  value       = module.ad_tooling_windows_02.instance_id
}

output "ad_tooling_windows_02_private_ip" {
  description = "Private IP of the ad-tooling-windows-02 instance"
  value       = module.ad_tooling_windows_02.private_ip
}