# EKS Stack - Combines EKS cluster, node groups, and related resources

module "eks_cluster" {
  source = "../../../modules/compute/eks"

  environment = var.environment
  region      = var.region
  common_tags = var.common_tags

  # EKS Cluster Configuration
  cluster_name               = var.cluster_name
  cluster_version            = var.cluster_version
  cluster_role_name          = var.cluster_role_name
  node_group_role_name       = var.node_group_role_name
  subnet_ids                 = var.subnet_ids
  cluster_security_group_ids = var.cluster_security_group_ids
  endpoint_private_access    = var.endpoint_private_access
  endpoint_public_access     = var.endpoint_public_access
  public_access_cidrs        = var.public_access_cidrs
  cluster_log_types          = var.cluster_log_types
  kms_key_arn                = var.kms_key_arn

  # Node Group Configuration
  node_group_name           = var.node_group_name
  node_group_subnet_ids     = var.node_group_subnet_ids
  node_group_instance_types = var.node_group_instance_types
  node_group_ami_type       = var.node_group_ami_type
  node_group_capacity_type  = var.node_group_capacity_type
  node_group_disk_size      = var.node_group_disk_size
  node_group_desired_size   = var.node_group_desired_size
  node_group_max_size       = var.node_group_max_size
  node_group_min_size       = var.node_group_min_size
  node_group_labels         = var.node_group_labels

  # EKS Addon Versions
  addon_amazon_cloudwatch_observability_version = var.addon_amazon_cloudwatch_observability_version
  addon_aws_ebs_csi_driver_version              = var.addon_aws_ebs_csi_driver_version
  addon_aws_guardduty_agent_version             = var.addon_aws_guardduty_agent_version
  addon_coredns_version                         = var.addon_coredns_version
  addon_eks_pod_identity_agent_version          = var.addon_eks_pod_identity_agent_version
  addon_kube_proxy_version                      = var.addon_kube_proxy_version
  addon_vpc_cni_version                         = var.addon_vpc_cni_version
}
