# Stack Variables
# Generated by terraformer_vpc_fetch.py

variable "environment" {
  description = "Environment name"
  type        = string
}

variable "common_tags" {
  description = "Common tags for all resources"
  type        = map(string)
  default     = {}
}

variable "internet_facing" {
  description = "Configuration for Internet Facing VPC compartment"
  type = object({
    vpc_cidr                             = string
    vpc_name                             = string
    enable_dns_hostnames                 = bool
    enable_dns_support                   = bool
    enable_network_address_usage_metrics = bool
    instance_tenancy                     = string
    create_internet_gateway              = bool
    public_subnets = map(object({
      cidr_block        = string
      availability_zone = string
      name              = string
    }))
  })
}
