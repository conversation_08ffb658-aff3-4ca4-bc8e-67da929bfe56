# Stack Outputs
# Internet Facing Compartment Stack Outputs

output "vpc_id" {
  description = "ID of the Internet Facing VPC"
  value       = module.Internet-facing-compartment.vpc_id
}

output "vpc_arn" {
  description = "ARN of the Internet Facing VPC"
  value       = module.Internet-facing-compartment.vpc_arn
}

output "vpc_cidr_block" {
  description = "CIDR block of the Internet Facing VPC"
  value       = module.Internet-facing-compartment.vpc_cidr_block
}

output "vpc_name" {
  description = "Name of the Internet Facing VPC"
  value       = module.Internet-facing-compartment.vpc_name
}

# Internet Gateway Outputs
output "internet_gateway_id" {
  description = "ID of the Internet Gateway"
  value       = module.Internet-facing-compartment.internet_gateway_id
}

output "internet_gateway_arn" {
  description = "ARN of the Internet Gateway"
  value       = module.Internet-facing-compartment.internet_gateway_arn
}

# Public Subnet Outputs
output "public_subnet_ids" {
  description = "Map of public subnet names to their IDs"
  value       = module.Internet-facing-compartment.public_subnet_ids
}

output "public_subnet_arns" {
  description = "Map of public subnet names to their ARNs"
  value       = module.Internet-facing-compartment.public_subnet_arns
}

output "public_subnet_cidr_blocks" {
  description = "Map of public subnet names to their CIDR blocks"
  value       = module.Internet-facing-compartment.public_subnet_cidr_blocks
}

output "public_subnet_availability_zones" {
  description = "Map of public subnet names to their availability zones"
  value       = module.Internet-facing-compartment.public_subnet_availability_zones
}

# Route Table Outputs
output "public_route_table_id" {
  description = "ID of the public route table"
  value       = module.Internet-facing-compartment.public_route_table_id
}

output "public_route_table_arn" {
  description = "ARN of the public route table"
  value       = module.Internet-facing-compartment.public_route_table_arn
}
