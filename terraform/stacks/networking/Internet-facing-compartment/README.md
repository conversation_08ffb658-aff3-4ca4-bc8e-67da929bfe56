# infovault-dev-Internet-facing-compartment Stack

Generated by terraformer_vpc_fetch.py on 2025-06-09 13:17:22

## Overview

This stack combines the Internet-facing-compartment networking module with additional stack-specific resources.

## Usage

```hcl
module "networking_Internet-facing-compartment" {
  source = "../../stacks/networking/Internet-facing-compartment"
  
  environment = var.environment
  common_tags = var.common_tags
}
```

## Components

- **Module**: `modules/networking/Internet-facing-compartment`
- **Resources**: VPC, subnets, security groups, etc.

## Validation

Run the validation script:
```bash
./terraform/scripts/validation/Internet-facing-compartment/validate_Internet-facing-compartment.sh
```
