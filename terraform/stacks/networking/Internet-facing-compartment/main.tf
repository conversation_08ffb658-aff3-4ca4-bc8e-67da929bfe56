# infovault-dev-Internet-facing-compartment Stack
# Generated by terraformer_vpc_fetch.py

module "Internet-facing-compartment" {
  source = "../../../modules/networking/Internet-facing-compartment"

  environment                          = var.environment
  common_tags                          = var.common_tags
  vpc_cidr                             = var.internet_facing.vpc_cidr
  vpc_name                             = var.internet_facing.vpc_name
  enable_dns_hostnames                 = var.internet_facing.enable_dns_hostnames
  enable_dns_support                   = var.internet_facing.enable_dns_support
  enable_network_address_usage_metrics = var.internet_facing.enable_network_address_usage_metrics
  instance_tenancy                     = var.internet_facing.instance_tenancy
  public_subnets                       = var.internet_facing.public_subnets
  create_internet_gateway              = var.internet_facing.create_internet_gateway
  internet_gateway_name                = "infovault-${var.environment}-igw"
  public_route_table_name              = "infovault-${var.environment}-public-rt"
}
