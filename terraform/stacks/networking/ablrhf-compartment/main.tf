# infovault-dev-ablrhf-compartment Stack
# Generated by terraformer_vpc_fetch.py

module "ablrhf_vpc" {
  source = "../../../modules/networking/vpc_compartment"

  # VPC Configuration
  vpc_cidr                             = var.ablrhf.vpc_cidr
  vpc_name                             = var.ablrhf.vpc_name
  enable_dns_hostnames                 = var.ablrhf.enable_dns_hostnames
  enable_dns_support                   = var.ablrhf.enable_dns_support
  enable_network_address_usage_metrics = var.ablrhf.enable_network_address_usage_metrics
  instance_tenancy                     = var.ablrhf.instance_tenancy
  create_internet_gateway              = var.ablrhf.create_internet_gateway

  # Subnets
  public_subnets  = var.ablrhf.public_subnets
  private_subnets = var.ablrhf.private_subnets

  # NAT Gateways
  nat_gateways = var.ablrhf.nat_gateways

  # Route Tables
  public_route_tables  = var.ablrhf.public_route_tables
  private_route_tables = var.ablrhf.private_route_tables

  # Route Table Associations
  public_subnet_route_associations  = var.ablrhf.public_subnet_route_associations
  private_subnet_route_associations = var.ablrhf.private_subnet_route_associations

  # Routes
  private_nat_routes = var.ablrhf.private_nat_routes
  vpc_peering_routes = var.ablrhf.vpc_peering_routes

  # Security Groups
  security_groups              = var.ablrhf.security_groups
  security_group_ingress_rules = var.ablrhf.security_group_ingress_rules
  security_group_egress_rules  = var.ablrhf.security_group_egress_rules

  # Network ACLs
  network_acls              = var.ablrhf.network_acls
  network_acl_ingress_rules = var.ablrhf.network_acl_ingress_rules
  network_acl_egress_rules  = var.ablrhf.network_acl_egress_rules

  # VPC Endpoints
  vpc_endpoints = var.ablrhf.vpc_endpoints

  # Tags
  common_tags = var.common_tags
}
