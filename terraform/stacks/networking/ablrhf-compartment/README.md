# infovault-dev-ablrhf-compartment Stack

Generated by terraformer_vpc_fetch.py on 2025-06-09 13:17:16

## Overview

This stack combines the ablrhf-compartment networking module with additional stack-specific resources.

## Usage

```hcl
module "networking_ablrhf-compartment" {
  source = "../../stacks/networking/ablrhf-compartment"
  
  environment = var.environment
  common_tags = var.common_tags
}
```

## Components

- **Module**: `modules/networking/ablrhf-compartment`
- **Resources**: VPC, subnets, security groups, etc.

## Validation

Run the validation script:
```bash
./terraform/scripts/validation/ablrhf-compartment/validate_ablrhf-compartment.sh
```
