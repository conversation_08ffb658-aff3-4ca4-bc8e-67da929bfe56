# infovault-dev-Patching-Compartment Stack

Generated by terraformer_vpc_fetch.py on 2025-06-09 13:16:58

## Overview

This stack combines the Patching-Compartment networking module with additional stack-specific resources.

## Usage

```hcl
module "networking_Patching-Compartment" {
  source = "../../stacks/networking/Patching-Compartment"
  
  environment = var.environment
  common_tags = var.common_tags
}
```

## Components

- **Module**: `modules/networking/Patching-Compartment`
- **Resources**: VPC, subnets, security groups, etc.

## Validation

Run the validation script:
```bash
./terraform/scripts/validation/Patching-Compartment/validate_Patching-Compartment.sh
```
