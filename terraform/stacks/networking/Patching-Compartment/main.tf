# Patching Compartment VPC Stack

module "patching_vpc" {
  source = "../../../modules/networking/vpc_compartment"

  # VPC Configuration
  vpc_cidr                             = var.patching.vpc_cidr
  vpc_name                             = var.patching.vpc_name
  enable_dns_hostnames                 = var.patching.enable_dns_hostnames
  enable_dns_support                   = var.patching.enable_dns_support
  enable_network_address_usage_metrics = var.patching.enable_network_address_usage_metrics
  instance_tenancy                     = var.patching.instance_tenancy
  create_internet_gateway              = var.patching.create_internet_gateway

  # Subnets
  public_subnets  = var.patching.public_subnets
  private_subnets = var.patching.private_subnets

  # NAT Gateways
  nat_gateways = var.patching.nat_gateways

  # Route Tables
  public_route_tables  = var.patching.public_route_tables
  private_route_tables = var.patching.private_route_tables

  # Route Table Associations
  public_subnet_route_associations  = var.patching.public_subnet_route_associations
  private_subnet_route_associations = var.patching.private_subnet_route_associations

  # Routes
  private_nat_routes = var.patching.private_nat_routes
  vpc_peering_routes = var.patching.vpc_peering_routes

  # Security Groups
  security_groups              = var.patching.security_groups
  security_group_ingress_rules = var.patching.security_group_ingress_rules
  security_group_egress_rules  = var.patching.security_group_egress_rules

  # Network ACLs
  network_acls              = var.patching.network_acls
  network_acl_ingress_rules = var.patching.network_acl_ingress_rules
  network_acl_egress_rules  = var.patching.network_acl_egress_rules

  # VPC Endpoints
  vpc_endpoints = var.patching.vpc_endpoints

  # Common Tags
  common_tags = var.common_tags
}
