# Stack Variables
# Generated by terraformer_vpc_fetch.py

variable "environment" {
  description = "Environment name"
  type        = string
}

variable "common_tags" {
  description = "Common tags for all resources"
  type        = map(string)
  default     = {}
}

variable "patching" {
  description = "Configuration for Patching VPC compartment"
  type = object({
    # VPC Configuration
    vpc_cidr                             = string
    vpc_name                             = string
    enable_dns_hostnames                 = bool
    enable_dns_support                   = bool
    enable_network_address_usage_metrics = bool
    instance_tenancy                     = string
    create_internet_gateway              = bool

    # Subnets
    public_subnets = map(object({
      cidr_block                      = string
      availability_zone               = string
      map_public_ip_on_launch         = bool
      assign_ipv6_address_on_creation = bool
      name                            = string
    }))

    private_subnets = map(object({
      cidr_block                      = string
      availability_zone               = string
      map_public_ip_on_launch         = bool
      assign_ipv6_address_on_creation = bool
      name                            = string
    }))

    # NAT Gateways
    nat_gateways = map(object({
      name       = string
      subnet_key = string
    }))

    # Route Tables
    public_route_tables = map(object({
      name = string
    }))

    private_route_tables = map(object({
      name = string
    }))

    # Route Table Associations
    public_subnet_route_associations = map(object({
      subnet_key      = string
      route_table_key = string
    }))

    private_subnet_route_associations = map(object({
      subnet_key      = string
      route_table_key = string
    }))

    # Routes
    private_nat_routes = map(object({
      route_table_key        = string
      destination_cidr_block = string
      nat_gateway_key        = string
    }))

    vpc_peering_routes = map(object({
      route_table_key           = string
      route_table_type          = string
      destination_cidr_block    = string
      vpc_peering_connection_id = string
    }))

    # Security Groups
    security_groups = map(object({
      name_prefix = string
      description = string
      name        = string
    }))

    security_group_ingress_rules = map(object({
      security_group_key       = string
      from_port                = number
      to_port                  = number
      protocol                 = string
      cidr_blocks              = list(string)
      source_security_group_id = string
      description              = string
    }))

    security_group_egress_rules = map(object({
      security_group_key       = string
      from_port                = number
      to_port                  = number
      protocol                 = string
      cidr_blocks              = list(string)
      source_security_group_id = string
      description              = string
    }))

    # Network ACLs
    network_acls = map(object({
      name        = string
      subnet_keys = list(string)
    }))

    network_acl_ingress_rules = map(object({
      network_acl_key = string
      rule_number     = number
      protocol        = string
      rule_action     = string
      cidr_block      = string
      from_port       = number
      to_port         = number
    }))

    network_acl_egress_rules = map(object({
      network_acl_key = string
      rule_number     = number
      protocol        = string
      rule_action     = string
      cidr_block      = string
      from_port       = number
      to_port         = number
    }))

    # VPC Endpoints
    vpc_endpoints = map(object({
      name                = string
      service_name        = string
      vpc_endpoint_type   = string
      route_table_ids     = list(string)
      subnet_ids          = list(string)
      security_group_ids  = list(string)
      private_dns_enabled = bool
    }))
  })
}
