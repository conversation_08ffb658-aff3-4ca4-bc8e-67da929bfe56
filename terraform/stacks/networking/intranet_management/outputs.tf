# Intranet Management Compartment Stack Outputs

# VPC Outputs
output "vpc_id" {
  description = "ID of the Intranet Management VPC"
  value       = module.intranet_management_vpc.vpc_id
}

output "vpc_cidr_block" {
  description = "CIDR block of the Intranet Management VPC"
  value       = module.intranet_management_vpc.vpc_cidr_block
}

output "vpc_arn" {
  description = "ARN of the Intranet Management VPC"
  value       = module.intranet_management_vpc.vpc_arn
}

# Internet Gateway Outputs
output "internet_gateway_id" {
  description = "ID of the internet gateway"
  value       = module.intranet_management_vpc.internet_gateway_id
}

output "internet_gateway_arn" {
  description = "ARN of the internet gateway"
  value       = module.intranet_management_vpc.internet_gateway_arn
}

# Subnet Outputs
output "public_subnet_ids" {
  description = "IDs of the public subnets"
  value       = module.intranet_management_vpc.public_subnet_ids
}

output "private_subnet_ids" {
  description = "IDs of the private subnets"
  value       = module.intranet_management_vpc.private_subnet_ids
}

output "public_subnet_arns" {
  description = "ARNs of the public subnets"
  value       = module.intranet_management_vpc.public_subnet_arns
}

output "private_subnet_arns" {
  description = "ARNs of the private subnets"
  value       = module.intranet_management_vpc.private_subnet_arns
}

output "public_subnet_cidr_blocks" {
  description = "CIDR blocks of the public subnets"
  value       = module.intranet_management_vpc.public_subnet_cidr_blocks
}

output "private_subnet_cidr_blocks" {
  description = "CIDR blocks of the private subnets"
  value       = module.intranet_management_vpc.private_subnet_cidr_blocks
}

# NAT Gateway Outputs
output "nat_gateway_ids" {
  description = "IDs of the NAT gateways"
  value       = module.intranet_management_vpc.nat_gateway_ids
}

output "nat_gateway_public_ips" {
  description = "Public IPs of the NAT gateways"
  value       = module.intranet_management_vpc.nat_gateway_public_ips
}

output "elastic_ip_ids" {
  description = "IDs of the Elastic IPs for NAT gateways"
  value       = module.intranet_management_vpc.elastic_ip_ids
}

output "elastic_ip_public_ips" {
  description = "Public IPs of the Elastic IPs"
  value       = module.intranet_management_vpc.elastic_ip_public_ips
}

# Route Table Outputs
output "public_route_table_ids" {
  description = "IDs of the public route tables"
  value       = module.intranet_management_vpc.public_route_table_ids
}

output "private_route_table_ids" {
  description = "IDs of the private route tables"
  value       = module.intranet_management_vpc.private_route_table_ids
}

# Security Group Outputs
output "security_group_ids" {
  description = "IDs of the security groups"
  value       = module.intranet_management_vpc.security_group_ids
}

output "security_group_arns" {
  description = "ARNs of the security groups"
  value       = module.intranet_management_vpc.security_group_arns
}

# Network ACL Outputs
output "network_acl_ids" {
  description = "IDs of the network ACLs"
  value       = module.intranet_management_vpc.network_acl_ids
}

# VPC Endpoint Outputs
output "vpc_endpoint_ids" {
  description = "IDs of the VPC endpoints"
  value       = module.intranet_management_vpc.vpc_endpoint_ids
}

output "vpc_endpoint_dns_entries" {
  description = "DNS entries of the VPC endpoints"
  value       = module.intranet_management_vpc.vpc_endpoint_dns_entries
}

