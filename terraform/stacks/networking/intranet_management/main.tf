# Intranet Management Compartment Stack
# This stack creates the Intranet Management VPC compartment using the generic VPC module

terraform {
  required_version = ">= 1.3.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

module "intranet_management_vpc" {
  source = "../../../modules/networking/vpc_compartment"

  # VPC Configuration
  vpc_cidr                             = var.vpc_cidr
  vpc_name                             = var.vpc_name
  enable_dns_hostnames                 = var.enable_dns_hostnames
  enable_dns_support                   = var.enable_dns_support
  enable_network_address_usage_metrics = var.enable_network_address_usage_metrics
  instance_tenancy                     = var.instance_tenancy
  create_internet_gateway              = var.create_internet_gateway

  # Subnets
  public_subnets  = var.public_subnets
  private_subnets = var.private_subnets

  # NAT Gateways
  nat_gateways = var.nat_gateways

  # Route Tables
  public_route_tables  = var.public_route_tables
  private_route_tables = var.private_route_tables

  # Route Table Associations
  public_subnet_route_associations  = var.public_subnet_route_associations
  private_subnet_route_associations = var.private_subnet_route_associations

  # Routes
  private_nat_routes = var.private_nat_routes
  vpc_peering_routes = var.vpc_peering_routes

  # Security Groups
  security_groups              = var.security_groups
  security_group_ingress_rules = var.security_group_ingress_rules
  security_group_egress_rules  = var.security_group_egress_rules

  # Network ACLs
  network_acls              = var.network_acls
  network_acl_ingress_rules = var.network_acl_ingress_rules
  network_acl_egress_rules  = var.network_acl_egress_rules

  # VPC Endpoints
  vpc_endpoints = var.vpc_endpoints

  # Common Tags
  common_tags = var.common_tags
}

