# infovault-dev-gen-facing-compartment Stack

Generated by terraformer_vpc_fetch.py on 2025-06-09 13:17:18

## Overview

This stack combines the gen-facing-compartment networking module with additional stack-specific resources.

## Usage

```hcl
module "networking_gen-facing-compartment" {
  source = "../../stacks/networking/gen-facing-compartment"
  
  environment = var.environment
  common_tags = var.common_tags
}
```

## Components

- **Module**: `modules/networking/gen-facing-compartment`
- **Resources**: VPC, subnets, security groups, etc.

## Validation

Run the validation script:
```bash
./terraform/scripts/validation/gen-facing-compartment/validate_gen-facing-compartment.sh
```
