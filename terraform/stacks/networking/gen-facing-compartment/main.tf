# Gen Facing Compartment VPC Stack

module "gen_facing_vpc" {
  source = "../../../modules/networking/vpc_compartment"

  # VPC Configuration
  vpc_cidr                             = var.gen_facing.vpc_cidr
  vpc_name                             = var.gen_facing.vpc_name
  enable_dns_hostnames                 = var.gen_facing.enable_dns_hostnames
  enable_dns_support                   = var.gen_facing.enable_dns_support
  enable_network_address_usage_metrics = var.gen_facing.enable_network_address_usage_metrics
  instance_tenancy                     = var.gen_facing.instance_tenancy
  create_internet_gateway              = var.gen_facing.create_internet_gateway
  secondary_cidr_blocks                = var.gen_facing.secondary_cidr_blocks

  # Subnets
  public_subnets  = var.gen_facing.public_subnets
  private_subnets = var.gen_facing.private_subnets

  # Route Tables
  public_route_tables  = var.gen_facing.public_route_tables
  private_route_tables = var.gen_facing.private_route_tables

  # Route Table Associations
  public_subnet_route_associations  = var.gen_facing.public_subnet_route_associations
  private_subnet_route_associations = var.gen_facing.private_subnet_route_associations

  # NAT Gateways
  nat_gateways       = var.gen_facing.nat_gateways
  private_nat_routes = var.gen_facing.private_nat_routes

  # VPC Peering Routes
  vpc_peering_routes = var.gen_facing.vpc_peering_routes

  # Security Groups
  security_groups              = var.gen_facing.security_groups
  security_group_ingress_rules = var.gen_facing.security_group_ingress_rules
  security_group_egress_rules  = var.gen_facing.security_group_egress_rules

  # Network ACLs
  network_acls              = var.gen_facing.network_acls
  network_acl_ingress_rules = var.gen_facing.network_acl_ingress_rules
  network_acl_egress_rules  = var.gen_facing.network_acl_egress_rules

  # VPC Endpoints
  vpc_endpoints = var.gen_facing.vpc_endpoints

  # Tags
  common_tags = var.common_tags
}
