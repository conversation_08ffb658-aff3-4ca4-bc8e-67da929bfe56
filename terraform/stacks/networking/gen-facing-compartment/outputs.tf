# Gen Facing Compartment Stack Outputs

output "vpc_id" {
  description = "ID of the VPC"
  value       = module.gen_facing_vpc.vpc_id
}

output "vpc_cidr_block" {
  description = "CIDR block of the VPC"
  value       = module.gen_facing_vpc.vpc_cidr_block
}

output "public_subnet_ids" {
  description = "IDs of the public subnets"
  value       = module.gen_facing_vpc.public_subnet_ids
}

output "private_subnet_ids" {
  description = "IDs of the private subnets"
  value       = module.gen_facing_vpc.private_subnet_ids
}

output "public_route_table_ids" {
  description = "IDs of the public route tables"
  value       = module.gen_facing_vpc.public_route_table_ids
}

output "private_route_table_ids" {
  description = "IDs of the private route tables"
  value       = module.gen_facing_vpc.private_route_table_ids
}

output "internet_gateway_id" {
  description = "ID of the Internet Gateway"
  value       = module.gen_facing_vpc.internet_gateway_id
}
