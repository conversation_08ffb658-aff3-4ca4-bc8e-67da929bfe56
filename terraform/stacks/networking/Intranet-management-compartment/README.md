# infovalut-dev-Intranet-management-compartment Stack

Generated by terraformer_vpc_fetch.py on 2025-06-09 13:15:58

## Overview

This stack combines the Intranet-management-compartment networking module with additional stack-specific resources.

## Usage

```hcl
module "networking_Intranet-management-compartment" {
  source = "../../stacks/networking/Intranet-management-compartment"
  
  environment = var.environment
  common_tags = var.common_tags
}
```

## Components

- **Module**: `modules/networking/Intranet-management-compartment`
- **Resources**: VPC, subnets, security groups, etc.

## Validation

Run the validation script:
```bash
./terraform/scripts/validation/Intranet-management-compartment/validate_Intranet-management-compartment.sh
```
