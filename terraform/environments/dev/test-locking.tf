# S3 Native Locking Test Resources
# These resources help verify S3 native locking functionality

resource "random_id" "locking_test" {
  byte_length = 4

  keepers = {
    test_purpose = "s3-native-locking-verification"
    timestamp = timestamp()
  }
}

resource "random_string" "lock_test_string" {
  length = 16
  special = false

  keepers = {
    random_id = random_id.locking_test.hex
  }
}

# Test resource with a longer apply time to better test locking
resource "time_sleep" "lock_duration_test" {
  create_duration = "10s"

  triggers = {
    random_id = random_id.locking_test.hex
  }
}

output "locking_test_id" {
  value = random_id.locking_test.hex
  description = "Random ID to test S3 native locking functionality"
}

output "lock_test_string" {
  value = random_string.lock_test_string.result
  description = "Random string for extended locking test"
}

output "lock_test_timestamp" {
  value = random_id.locking_test.keepers.timestamp
  description = "Timestamp when the lock test was created"
}
