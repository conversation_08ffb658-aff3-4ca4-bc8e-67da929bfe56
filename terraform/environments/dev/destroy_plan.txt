Acquiring state lock. This may take a few moments...
[0m[1mmodule.patching.module.patching_vpc.aws_vpc.main: Refreshing state... [id=vpc-0b1e6f41c37fe6624][0m
[0m[1mmodule.gen_facing.module.gen_facing_vpc.aws_vpc.main: Refreshing state... [id=vpc-02bb90f92fd70e6f6][0m
[0m[1mmodule.internet_facing.module.Internet-facing-compartment.aws_vpc.main: Refreshing state... [id=vpc-08695098ddd82b831][0m
[0m[1mmodule.linux_tooling_server.aws_instance.this: Refreshing state... [id=i-0094f5748e0e6e7c8][0m
[0m[1mmodule.gitlab_runner.aws_instance.this: Refreshing state... [id=i-0e010c82071792dd1][0m
[0m[1mmodule.windows_tooling_server.aws_instance.this: Refreshing state... [id=i-025d0495b9029255b][0m
[0m[1mmodule.dra_analytics_server.aws_instance.this: Refreshing state... [id=i-01e16873776b18c93][0m
[0m[1mmodule.dra_admin_server.aws_instance.this: Refreshing state... [id=i-0868d91b82fc958b8][0m
[0m[1mmodule.intranet_management.module.intranet_management_vpc.aws_vpc.main: Refreshing state... [id=vpc-05f38ae43b723b6e9][0m
[0m[1mmodule.intranet_management.module.intranet_management_vpc.aws_route_table.private["mgmt_main_rt"]: Refreshing state... [id=rtb-0ec4caf429261cbfc][0m
[0m[1mmodule.intranet_management.module.intranet_management_vpc.aws_subnet.private["natvpce_az1"]: Refreshing state... [id=subnet-09a1df115fcbf7e71][0m
[0m[1mmodule.intranet_management.module.intranet_management_vpc.aws_subnet.private["natvpce_az2"]: Refreshing state... [id=subnet-008509e3c7a226a65][0m
[0m[1mmodule.intranet_management.module.intranet_management_vpc.aws_subnet.private["natad"]: Refreshing state... [id=subnet-0fc9b7dea2dfb2f5f][0m
[0m[1mmodule.intranet_management.module.intranet_management_vpc.aws_subnet.private["natmgt"]: Refreshing state... [id=subnet-06742da032d3c33ed][0m
[0m[1mmodule.patching.module.patching_vpc.aws_route_table.private["patching_rt"]: Refreshing state... [id=rtb-09532958ed81f7a74][0m
[0m[1mmodule.patching.module.patching_vpc.aws_subnet.private["patching_az1"]: Refreshing state... [id=subnet-0f08b6104f4626204][0m
[0m[1mmodule.internet_facing.module.Internet-facing-compartment.aws_subnet.public["public_az1"]: Refreshing state... [id=subnet-0ab44f0ae503eec07][0m
[0m[1mmodule.internet_facing.module.Internet-facing-compartment.aws_subnet.public["public_az2"]: Refreshing state... [id=subnet-027ac562019ecdca7][0m
[0m[1mmodule.internet_facing.module.Internet-facing-compartment.aws_internet_gateway.main[0]: Refreshing state... [id=igw-0a54195907b5b5079][0m
[0m[1mmodule.internet_facing.module.Internet-facing-compartment.aws_route_table.public[0]: Refreshing state... [id=rtb-0c1eadd6394a31b84][0m
[0m[1mmodule.gen_facing.module.gen_facing_vpc.aws_route_table.public["gen_public_rt"]: Refreshing state... [id=rtb-08755f425d9cedaf6][0m
[0m[1mmodule.gen_facing.module.gen_facing_vpc.aws_vpc_ipv4_cidr_block_association.secondary["**********/22"]: Refreshing state... [id=vpc-cidr-assoc-0f35f196d7c6c44b6][0m
[0m[1mmodule.gen_facing.module.gen_facing_vpc.aws_route_table.private["gen_private_rt"]: Refreshing state... [id=rtb-096f1decba5710de5][0m
[0m[1mmodule.gen_facing.module.gen_facing_vpc.aws_internet_gateway.main[0]: Refreshing state... [id=igw-0e53990e6d46ee42c][0m
[0m[1mmodule.intranet_management.module.intranet_management_vpc.aws_route_table_association.private["natvpce_az2_assoc"]: Refreshing state... [id=rtbassoc-0bd3c05c9e34dd9ca][0m
[0m[1mmodule.intranet_management.module.intranet_management_vpc.aws_route_table_association.private["natad_assoc"]: Refreshing state... [id=rtbassoc-01963043ae3694069][0m
[0m[1mmodule.intranet_management.module.intranet_management_vpc.aws_route_table_association.private["natmgt_assoc"]: Refreshing state... [id=rtbassoc-0a5cc3e2cf9d830c6][0m
[0m[1mmodule.intranet_management.module.intranet_management_vpc.aws_route_table_association.private["natvpce_az1_assoc"]: Refreshing state... [id=rtbassoc-0bd686d34af03f5dd][0m
[0m[1mmodule.patching.module.patching_vpc.aws_route_table_association.private["patching_assoc"]: Refreshing state... [id=rtbassoc-06810216b99dbd053][0m
[0m[1mmodule.internet_facing.module.Internet-facing-compartment.aws_route_table_association.public["public_az1"]: Refreshing state... [id=rtbassoc-08740208b5b96da46][0m
[0m[1mmodule.internet_facing.module.Internet-facing-compartment.aws_route_table_association.public["public_az2"]: Refreshing state... [id=rtbassoc-0c2ee32a10c0818e3][0m
[0m[1mmodule.internet_facing.module.Internet-facing-compartment.aws_route.public_internet[0]: Refreshing state... [id=r-rtb-0c1eadd6394a31b841080289494][0m
[0m[1mmodule.gen_facing.module.gen_facing_vpc.aws_subnet.public["alb_subnet_az1"]: Refreshing state... [id=subnet-0b93f0452a90bb94b][0m
[0m[1mmodule.gen_facing.module.gen_facing_vpc.aws_subnet.private["smtp_subnet_dlz1"]: Refreshing state... [id=subnet-048d2c6ad6104bce1][0m
[0m[1mmodule.gen_facing.module.gen_facing_vpc.aws_subnet.private["gen_ingress_az1"]: Refreshing state... [id=subnet-0d670456ac150e9b7][0m
[0m[1mmodule.gen_facing.module.gen_facing_vpc.aws_subnet.private["app_subnet_az2"]: Refreshing state... [id=subnet-00a5a802525196bba][0m
[0m[1mmodule.gen_facing.module.gen_facing_vpc.aws_subnet.private["gen_migration_az1"]: Refreshing state... [id=subnet-06ef006f5416a0e77][0m
[0m[1mmodule.gen_facing.module.gen_facing_vpc.aws_subnet.private["db_subnet_az1"]: Refreshing state... [id=subnet-01ae797696c34b263][0m
[0m[1mmodule.gen_facing.module.gen_facing_vpc.aws_subnet.private["gen_egress_az1"]: Refreshing state... [id=subnet-02163189325121054][0m
[0m[1mmodule.gen_facing.module.gen_facing_vpc.aws_subnet.private["app_subnet_az1"]: Refreshing state... [id=subnet-087a114ef79afa85f][0m
[0m[1mmodule.gen_facing.module.gen_facing_vpc.aws_subnet.private["db_subnet_az2"]: Refreshing state... [id=subnet-0b749c2f19e463feb][0m
[0m[1mmodule.gen_facing.module.gen_facing_vpc.aws_route.public_internet["gen_public_rt"]: Refreshing state... [id=r-rtb-08755f425d9cedaf61080289494][0m
[0m[1mmodule.internet_facing_security_groups.module.internet_facing_security_groups.aws_security_group.this["proxy_egress"]: Refreshing state... [id=sg-0ad42ef8fbb37202c][0m
[0m[1mmodule.internet_facing_security_groups.module.internet_facing_security_groups.aws_security_group.this["nfw_egress"]: Refreshing state... [id=sg-00054adbcabec6df1][0m
[0m[1mmodule.gen_facing.module.gen_facing_vpc.aws_route_table_association.public["alb_subnet_az1_assoc"]: Refreshing state... [id=rtbassoc-05b2897e83d8f4dae][0m
[0m[1mmodule.gen_facing.module.gen_facing_vpc.aws_route_table_association.private["gen_egress_assoc"]: Refreshing state... [id=rtbassoc-0de2892917acaee0c][0m
[0m[1mmodule.gen_facing.module.gen_facing_vpc.aws_route_table_association.private["gen_migration_assoc"]: Refreshing state... [id=rtbassoc-003bfe0c4415a10ed][0m
[0m[1mmodule.gen_facing.module.gen_facing_vpc.aws_route_table_association.private["smtp_subnet_dlz1_assoc"]: Refreshing state... [id=rtbassoc-0394faea4b2e89be1][0m
[0m[1mmodule.gen_facing.module.gen_facing_vpc.aws_route_table_association.private["db_subnet_az2_assoc"]: Refreshing state... [id=rtbassoc-01d2c7645ff9a7ac3][0m
[0m[1mmodule.gen_facing.module.gen_facing_vpc.aws_route_table_association.private["db_subnet_az1_assoc"]: Refreshing state... [id=rtbassoc-07c5a7158739f34a1][0m
[0m[1mmodule.gen_facing.module.gen_facing_vpc.aws_route_table_association.private["gen_ingress_assoc"]: Refreshing state... [id=rtbassoc-004dac3395ab894e9][0m
[0m[1mmodule.gen_facing.module.gen_facing_vpc.aws_route_table_association.private["app_subnet_az2_assoc"]: Refreshing state... [id=rtbassoc-098db8c0990416e4a][0m
[0m[1mmodule.gen_facing.module.gen_facing_vpc.aws_route_table_association.private["app_subnet_az1_assoc"]: Refreshing state... [id=rtbassoc-0f38bb5f20a981d74][0m
[0m[1mmodule.internet_facing_security_groups.module.internet_facing_security_groups.aws_vpc_security_group_ingress_rule.this["proxy_egress_https"]: Refreshing state... [id=sgr-0777bd895eca786bc][0m
[0m[1mmodule.internet_facing_security_groups.module.internet_facing_security_groups.aws_vpc_security_group_ingress_rule.this["nfw_egress_internal_10"]: Refreshing state... [id=sgr-0abd9aa6ff5912ff6][0m
[0m[1mmodule.internet_facing_security_groups.module.internet_facing_security_groups.aws_vpc_security_group_ingress_rule.this["proxy_egress_http"]: Refreshing state... [id=sgr-0af7a07b8ed88020f][0m
[0m[1mmodule.internet_facing_security_groups.module.internet_facing_security_groups.aws_vpc_security_group_egress_rule.this["nfw_egress_all"]: Refreshing state... [id=sgr-0358f116dbc669418][0m
[0m[1mmodule.internet_facing_security_groups.module.internet_facing_security_groups.aws_vpc_security_group_ingress_rule.this["nfw_egress_internal_100"]: Refreshing state... [id=sgr-0308580bc23def426][0m
[0m[1mmodule.internet_facing_security_groups.module.internet_facing_security_groups.aws_vpc_security_group_egress_rule.this["proxy_egress_all"]: Refreshing state... [id=sgr-0e7c5ce05837952bc][0m
[0m[1mmodule.eks.module.eks_cluster.aws_iam_role.cluster: Refreshing state... [id=eks-cluster-role-infovault-dev-eks-cluster-v130][0m
[0m[1mmodule.eks.module.eks_cluster.aws_iam_role.node_group: Refreshing state... [id=eks-nodegroup-role-infovault-dev-eks-cluster-v130][0m
[0m[1mmodule.eks.module.eks_cluster.aws_iam_role_policy_attachment.node_group_AmazonEC2ContainerRegistryReadOnly: Refreshing state... [id=eks-nodegroup-role-infovault-dev-eks-cluster-v130-20250610041035813300000004][0m
[0m[1mmodule.eks.module.eks_cluster.aws_iam_role_policy_attachment.node_group_AmazonEKSWorkerNodePolicy: Refreshing state... [id=eks-nodegroup-role-infovault-dev-eks-cluster-v130-20250610041035456200000001][0m
[0m[1mmodule.eks.module.eks_cluster.aws_iam_role_policy_attachment.cluster_AmazonEKSClusterPolicy: Refreshing state... [id=eks-cluster-role-infovault-dev-eks-cluster-v130-20250610041035767200000003][0m
[0m[1mmodule.eks.module.eks_cluster.aws_iam_role_policy_attachment.node_group_AmazonEKS_CNI_Policy: Refreshing state... [id=eks-nodegroup-role-infovault-dev-eks-cluster-v130-20250610041035480000000002][0m
[0m[1mmodule.eks.module.eks_cluster.aws_eks_cluster.this: Refreshing state... [id=infovault-dev-eks-cluster-v130][0m
[0m[1mmodule.eks.module.eks_cluster.aws_eks_addon.eks_pod_identity_agent: Refreshing state... [id=infovault-dev-eks-cluster-v130:eks-pod-identity-agent][0m
[0m[1mmodule.eks.module.eks_cluster.aws_eks_addon.aws_guardduty_agent: Refreshing state... [id=infovault-dev-eks-cluster-v130:aws-guardduty-agent][0m
[0m[1mmodule.eks.module.eks_cluster.aws_eks_addon.vpc_cni: Refreshing state... [id=infovault-dev-eks-cluster-v130:vpc-cni][0m
[0m[1mmodule.eks.module.eks_cluster.aws_eks_addon.coredns: Refreshing state... [id=infovault-dev-eks-cluster-v130:coredns][0m
[0m[1mmodule.eks.module.eks_cluster.aws_eks_addon.kube_proxy: Refreshing state... [id=infovault-dev-eks-cluster-v130:kube-proxy][0m
[0m[1mmodule.eks.module.eks_cluster.aws_eks_addon.amazon_cloudwatch_observability: Refreshing state... [id=infovault-dev-eks-cluster-v130:amazon-cloudwatch-observability][0m
[0m[1mmodule.eks.module.eks_cluster.aws_eks_addon.aws_ebs_csi_driver: Refreshing state... [id=infovault-dev-eks-cluster-v130:aws-ebs-csi-driver][0m
[0m[1mmodule.eks.module.eks_cluster.aws_eks_node_group.main: Refreshing state... [id=infovault-dev-eks-cluster-v130:v130-node-group-3][0m

Terraform used the selected providers to generate the following execution
plan. Resource actions are indicated with the following symbols:
  [31m-[0m destroy[0m

Terraform will perform the following actions:

[1m  # module.dra_admin_server.aws_instance.this[0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_instance" "this" {
      [31m-[0m[0m ami                                  = "ami-05f0fc85de381bd44" [90m-> null[0m[0m
      [31m-[0m[0m arn                                  = "arn:aws:ec2:us-east-1:046276255144:instance/i-0868d91b82fc958b8" [90m-> null[0m[0m
      [31m-[0m[0m associate_public_ip_address          = false [90m-> null[0m[0m
      [31m-[0m[0m availability_zone                    = "us-east-1a" [90m-> null[0m[0m
      [31m-[0m[0m cpu_core_count                       = 2 [90m-> null[0m[0m
      [31m-[0m[0m cpu_threads_per_core                 = 2 [90m-> null[0m[0m
      [31m-[0m[0m disable_api_stop                     = false [90m-> null[0m[0m
      [31m-[0m[0m disable_api_termination              = false [90m-> null[0m[0m
      [31m-[0m[0m ebs_optimized                        = false [90m-> null[0m[0m
      [31m-[0m[0m get_password_data                    = false [90m-> null[0m[0m
      [31m-[0m[0m hibernation                          = false [90m-> null[0m[0m
      [31m-[0m[0m iam_instance_profile                 = "infovault-dev-ec2-profile" [90m-> null[0m[0m
      [31m-[0m[0m id                                   = "i-0868d91b82fc958b8" [90m-> null[0m[0m
      [31m-[0m[0m instance_initiated_shutdown_behavior = "stop" [90m-> null[0m[0m
      [31m-[0m[0m instance_state                       = "running" [90m-> null[0m[0m
      [31m-[0m[0m instance_type                        = "m5.xlarge" [90m-> null[0m[0m
      [31m-[0m[0m ipv6_address_count                   = 0 [90m-> null[0m[0m
      [31m-[0m[0m ipv6_addresses                       = [] [90m-> null[0m[0m
      [31m-[0m[0m monitoring                           = false [90m-> null[0m[0m
      [31m-[0m[0m placement_partition_number           = 0 [90m-> null[0m[0m
      [31m-[0m[0m primary_network_interface_id         = "eni-0dbf08b83bf03d965" [90m-> null[0m[0m
      [31m-[0m[0m private_dns                          = "ip-100-16-0-5.ec2.internal" [90m-> null[0m[0m
      [31m-[0m[0m private_ip                           = "**********" [90m-> null[0m[0m
      [31m-[0m[0m secondary_private_ips                = [] [90m-> null[0m[0m
      [31m-[0m[0m security_groups                      = [] [90m-> null[0m[0m
      [31m-[0m[0m source_dest_check                    = true [90m-> null[0m[0m
      [31m-[0m[0m subnet_id                            = "subnet-06742da032d3c33ed" [90m-> null[0m[0m
      [31m-[0m[0m tags                                 = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-draadmin-server"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m tags_all                             = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-draadmin-server"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m tenancy                              = "default" [90m-> null[0m[0m
      [31m-[0m[0m user_data                            = "fe81e072b1cb7e992be5c2ce843384cea7dbabc8" [90m-> null[0m[0m
      [31m-[0m[0m user_data_replace_on_change          = false [90m-> null[0m[0m
      [31m-[0m[0m vpc_security_group_ids               = [
          [31m-[0m[0m "sg-04d4b09b1d60de68f",
        ] [90m-> null[0m[0m

      [31m-[0m[0m capacity_reservation_specification {
          [31m-[0m[0m capacity_reservation_preference = "open" [90m-> null[0m[0m
        }

      [31m-[0m[0m cpu_options {
          [31m-[0m[0m core_count       = 2 [90m-> null[0m[0m
          [31m-[0m[0m threads_per_core = 2 [90m-> null[0m[0m
        }

      [31m-[0m[0m enclave_options {
          [31m-[0m[0m enabled = false [90m-> null[0m[0m
        }

      [31m-[0m[0m maintenance_options {
          [31m-[0m[0m auto_recovery = "default" [90m-> null[0m[0m
        }

      [31m-[0m[0m metadata_options {
          [31m-[0m[0m http_endpoint               = "enabled" [90m-> null[0m[0m
          [31m-[0m[0m http_protocol_ipv6          = "disabled" [90m-> null[0m[0m
          [31m-[0m[0m http_put_response_hop_limit = 1 [90m-> null[0m[0m
          [31m-[0m[0m http_tokens                 = "optional" [90m-> null[0m[0m
          [31m-[0m[0m instance_metadata_tags      = "disabled" [90m-> null[0m[0m
        }

      [31m-[0m[0m private_dns_name_options {
          [31m-[0m[0m enable_resource_name_dns_a_record    = false [90m-> null[0m[0m
          [31m-[0m[0m enable_resource_name_dns_aaaa_record = false [90m-> null[0m[0m
          [31m-[0m[0m hostname_type                        = "ip-name" [90m-> null[0m[0m
        }

      [31m-[0m[0m root_block_device {
          [31m-[0m[0m delete_on_termination = true [90m-> null[0m[0m
          [31m-[0m[0m device_name           = "/dev/xvda" [90m-> null[0m[0m
          [31m-[0m[0m encrypted             = false [90m-> null[0m[0m
          [31m-[0m[0m iops                  = 100 [90m-> null[0m[0m
          [31m-[0m[0m tags                  = {} [90m-> null[0m[0m
          [31m-[0m[0m tags_all              = {} [90m-> null[0m[0m
          [31m-[0m[0m throughput            = 0 [90m-> null[0m[0m
          [31m-[0m[0m volume_id             = "vol-03fcc9a8bdaebc820" [90m-> null[0m[0m
          [31m-[0m[0m volume_size           = 8 [90m-> null[0m[0m
          [31m-[0m[0m volume_type           = "gp2" [90m-> null[0m[0m
        }
    }

[1m  # module.dra_analytics_server.aws_instance.this[0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_instance" "this" {
      [31m-[0m[0m ami                                  = "ami-05f0fc85de381bd44" [90m-> null[0m[0m
      [31m-[0m[0m arn                                  = "arn:aws:ec2:us-east-1:046276255144:instance/i-01e16873776b18c93" [90m-> null[0m[0m
      [31m-[0m[0m associate_public_ip_address          = false [90m-> null[0m[0m
      [31m-[0m[0m availability_zone                    = "us-east-1a" [90m-> null[0m[0m
      [31m-[0m[0m cpu_core_count                       = 2 [90m-> null[0m[0m
      [31m-[0m[0m cpu_threads_per_core                 = 2 [90m-> null[0m[0m
      [31m-[0m[0m disable_api_stop                     = false [90m-> null[0m[0m
      [31m-[0m[0m disable_api_termination              = false [90m-> null[0m[0m
      [31m-[0m[0m ebs_optimized                        = false [90m-> null[0m[0m
      [31m-[0m[0m get_password_data                    = false [90m-> null[0m[0m
      [31m-[0m[0m hibernation                          = false [90m-> null[0m[0m
      [31m-[0m[0m iam_instance_profile                 = "infovault-dev-ec2-profile" [90m-> null[0m[0m
      [31m-[0m[0m id                                   = "i-01e16873776b18c93" [90m-> null[0m[0m
      [31m-[0m[0m instance_initiated_shutdown_behavior = "stop" [90m-> null[0m[0m
      [31m-[0m[0m instance_state                       = "running" [90m-> null[0m[0m
      [31m-[0m[0m instance_type                        = "m5.xlarge" [90m-> null[0m[0m
      [31m-[0m[0m ipv6_address_count                   = 0 [90m-> null[0m[0m
      [31m-[0m[0m ipv6_addresses                       = [] [90m-> null[0m[0m
      [31m-[0m[0m monitoring                           = false [90m-> null[0m[0m
      [31m-[0m[0m placement_partition_number           = 0 [90m-> null[0m[0m
      [31m-[0m[0m primary_network_interface_id         = "eni-0f3a1aaeb4fb2c1bb" [90m-> null[0m[0m
      [31m-[0m[0m private_dns                          = "ip-100-16-0-11.ec2.internal" [90m-> null[0m[0m
      [31m-[0m[0m private_ip                           = "***********" [90m-> null[0m[0m
      [31m-[0m[0m secondary_private_ips                = [] [90m-> null[0m[0m
      [31m-[0m[0m security_groups                      = [] [90m-> null[0m[0m
      [31m-[0m[0m source_dest_check                    = true [90m-> null[0m[0m
      [31m-[0m[0m subnet_id                            = "subnet-06742da032d3c33ed" [90m-> null[0m[0m
      [31m-[0m[0m tags                                 = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-draanalytics-server"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m tags_all                             = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-draanalytics-server"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m tenancy                              = "default" [90m-> null[0m[0m
      [31m-[0m[0m user_data                            = "fe81e072b1cb7e992be5c2ce843384cea7dbabc8" [90m-> null[0m[0m
      [31m-[0m[0m user_data_replace_on_change          = false [90m-> null[0m[0m
      [31m-[0m[0m vpc_security_group_ids               = [
          [31m-[0m[0m "sg-04d4b09b1d60de68f",
        ] [90m-> null[0m[0m

      [31m-[0m[0m capacity_reservation_specification {
          [31m-[0m[0m capacity_reservation_preference = "open" [90m-> null[0m[0m
        }

      [31m-[0m[0m cpu_options {
          [31m-[0m[0m core_count       = 2 [90m-> null[0m[0m
          [31m-[0m[0m threads_per_core = 2 [90m-> null[0m[0m
        }

      [31m-[0m[0m enclave_options {
          [31m-[0m[0m enabled = false [90m-> null[0m[0m
        }

      [31m-[0m[0m maintenance_options {
          [31m-[0m[0m auto_recovery = "default" [90m-> null[0m[0m
        }

      [31m-[0m[0m metadata_options {
          [31m-[0m[0m http_endpoint               = "enabled" [90m-> null[0m[0m
          [31m-[0m[0m http_protocol_ipv6          = "disabled" [90m-> null[0m[0m
          [31m-[0m[0m http_put_response_hop_limit = 1 [90m-> null[0m[0m
          [31m-[0m[0m http_tokens                 = "optional" [90m-> null[0m[0m
          [31m-[0m[0m instance_metadata_tags      = "disabled" [90m-> null[0m[0m
        }

      [31m-[0m[0m private_dns_name_options {
          [31m-[0m[0m enable_resource_name_dns_a_record    = false [90m-> null[0m[0m
          [31m-[0m[0m enable_resource_name_dns_aaaa_record = false [90m-> null[0m[0m
          [31m-[0m[0m hostname_type                        = "ip-name" [90m-> null[0m[0m
        }

      [31m-[0m[0m root_block_device {
          [31m-[0m[0m delete_on_termination = true [90m-> null[0m[0m
          [31m-[0m[0m device_name           = "/dev/xvda" [90m-> null[0m[0m
          [31m-[0m[0m encrypted             = false [90m-> null[0m[0m
          [31m-[0m[0m iops                  = 100 [90m-> null[0m[0m
          [31m-[0m[0m tags                  = {} [90m-> null[0m[0m
          [31m-[0m[0m tags_all              = {} [90m-> null[0m[0m
          [31m-[0m[0m throughput            = 0 [90m-> null[0m[0m
          [31m-[0m[0m volume_id             = "vol-0f99ffe0dffd12d91" [90m-> null[0m[0m
          [31m-[0m[0m volume_size           = 8 [90m-> null[0m[0m
          [31m-[0m[0m volume_type           = "gp2" [90m-> null[0m[0m
        }
    }

[1m  # module.gitlab_runner.aws_instance.this[0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_instance" "this" {
      [31m-[0m[0m ami                                  = "ami-05f0fc85de381bd44" [90m-> null[0m[0m
      [31m-[0m[0m arn                                  = "arn:aws:ec2:us-east-1:046276255144:instance/i-0e010c82071792dd1" [90m-> null[0m[0m
      [31m-[0m[0m associate_public_ip_address          = false [90m-> null[0m[0m
      [31m-[0m[0m availability_zone                    = "us-east-1a" [90m-> null[0m[0m
      [31m-[0m[0m cpu_core_count                       = 2 [90m-> null[0m[0m
      [31m-[0m[0m cpu_threads_per_core                 = 2 [90m-> null[0m[0m
      [31m-[0m[0m disable_api_stop                     = false [90m-> null[0m[0m
      [31m-[0m[0m disable_api_termination              = false [90m-> null[0m[0m
      [31m-[0m[0m ebs_optimized                        = false [90m-> null[0m[0m
      [31m-[0m[0m get_password_data                    = false [90m-> null[0m[0m
      [31m-[0m[0m hibernation                          = false [90m-> null[0m[0m
      [31m-[0m[0m iam_instance_profile                 = "infovault-dev-ec2-profile" [90m-> null[0m[0m
      [31m-[0m[0m id                                   = "i-0e010c82071792dd1" [90m-> null[0m[0m
      [31m-[0m[0m instance_initiated_shutdown_behavior = "stop" [90m-> null[0m[0m
      [31m-[0m[0m instance_state                       = "running" [90m-> null[0m[0m
      [31m-[0m[0m instance_type                        = "m5.xlarge" [90m-> null[0m[0m
      [31m-[0m[0m ipv6_address_count                   = 0 [90m-> null[0m[0m
      [31m-[0m[0m ipv6_addresses                       = [] [90m-> null[0m[0m
      [31m-[0m[0m monitoring                           = false [90m-> null[0m[0m
      [31m-[0m[0m placement_partition_number           = 0 [90m-> null[0m[0m
      [31m-[0m[0m primary_network_interface_id         = "eni-05140e51b7f7265bb" [90m-> null[0m[0m
      [31m-[0m[0m private_dns                          = "ip-100-16-0-9.ec2.internal" [90m-> null[0m[0m
      [31m-[0m[0m private_ip                           = "**********" [90m-> null[0m[0m
      [31m-[0m[0m secondary_private_ips                = [] [90m-> null[0m[0m
      [31m-[0m[0m security_groups                      = [] [90m-> null[0m[0m
      [31m-[0m[0m source_dest_check                    = true [90m-> null[0m[0m
      [31m-[0m[0m subnet_id                            = "subnet-06742da032d3c33ed" [90m-> null[0m[0m
      [31m-[0m[0m tags                                 = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-gitlab-runner"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m tags_all                             = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-gitlab-runner"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m tenancy                              = "default" [90m-> null[0m[0m
      [31m-[0m[0m user_data                            = "fe81e072b1cb7e992be5c2ce843384cea7dbabc8" [90m-> null[0m[0m
      [31m-[0m[0m user_data_replace_on_change          = false [90m-> null[0m[0m
      [31m-[0m[0m vpc_security_group_ids               = [
          [31m-[0m[0m "sg-04d4b09b1d60de68f",
        ] [90m-> null[0m[0m

      [31m-[0m[0m capacity_reservation_specification {
          [31m-[0m[0m capacity_reservation_preference = "open" [90m-> null[0m[0m
        }

      [31m-[0m[0m cpu_options {
          [31m-[0m[0m core_count       = 2 [90m-> null[0m[0m
          [31m-[0m[0m threads_per_core = 2 [90m-> null[0m[0m
        }

      [31m-[0m[0m enclave_options {
          [31m-[0m[0m enabled = false [90m-> null[0m[0m
        }

      [31m-[0m[0m maintenance_options {
          [31m-[0m[0m auto_recovery = "default" [90m-> null[0m[0m
        }

      [31m-[0m[0m metadata_options {
          [31m-[0m[0m http_endpoint               = "enabled" [90m-> null[0m[0m
          [31m-[0m[0m http_protocol_ipv6          = "disabled" [90m-> null[0m[0m
          [31m-[0m[0m http_put_response_hop_limit = 1 [90m-> null[0m[0m
          [31m-[0m[0m http_tokens                 = "optional" [90m-> null[0m[0m
          [31m-[0m[0m instance_metadata_tags      = "disabled" [90m-> null[0m[0m
        }

      [31m-[0m[0m private_dns_name_options {
          [31m-[0m[0m enable_resource_name_dns_a_record    = false [90m-> null[0m[0m
          [31m-[0m[0m enable_resource_name_dns_aaaa_record = false [90m-> null[0m[0m
          [31m-[0m[0m hostname_type                        = "ip-name" [90m-> null[0m[0m
        }

      [31m-[0m[0m root_block_device {
          [31m-[0m[0m delete_on_termination = true [90m-> null[0m[0m
          [31m-[0m[0m device_name           = "/dev/xvda" [90m-> null[0m[0m
          [31m-[0m[0m encrypted             = false [90m-> null[0m[0m
          [31m-[0m[0m iops                  = 100 [90m-> null[0m[0m
          [31m-[0m[0m tags                  = {} [90m-> null[0m[0m
          [31m-[0m[0m tags_all              = {} [90m-> null[0m[0m
          [31m-[0m[0m throughput            = 0 [90m-> null[0m[0m
          [31m-[0m[0m volume_id             = "vol-0fb2e9cbfb26b606b" [90m-> null[0m[0m
          [31m-[0m[0m volume_size           = 8 [90m-> null[0m[0m
          [31m-[0m[0m volume_type           = "gp2" [90m-> null[0m[0m
        }
    }

[1m  # module.linux_tooling_server.aws_instance.this[0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_instance" "this" {
      [31m-[0m[0m ami                                  = "ami-05f0fc85de381bd44" [90m-> null[0m[0m
      [31m-[0m[0m arn                                  = "arn:aws:ec2:us-east-1:046276255144:instance/i-0094f5748e0e6e7c8" [90m-> null[0m[0m
      [31m-[0m[0m associate_public_ip_address          = false [90m-> null[0m[0m
      [31m-[0m[0m availability_zone                    = "us-east-1a" [90m-> null[0m[0m
      [31m-[0m[0m cpu_core_count                       = 1 [90m-> null[0m[0m
      [31m-[0m[0m cpu_threads_per_core                 = 2 [90m-> null[0m[0m
      [31m-[0m[0m disable_api_stop                     = false [90m-> null[0m[0m
      [31m-[0m[0m disable_api_termination              = false [90m-> null[0m[0m
      [31m-[0m[0m ebs_optimized                        = false [90m-> null[0m[0m
      [31m-[0m[0m get_password_data                    = false [90m-> null[0m[0m
      [31m-[0m[0m hibernation                          = false [90m-> null[0m[0m
      [31m-[0m[0m iam_instance_profile                 = "infovault-dev-ec2-profile" [90m-> null[0m[0m
      [31m-[0m[0m id                                   = "i-0094f5748e0e6e7c8" [90m-> null[0m[0m
      [31m-[0m[0m instance_initiated_shutdown_behavior = "stop" [90m-> null[0m[0m
      [31m-[0m[0m instance_state                       = "running" [90m-> null[0m[0m
      [31m-[0m[0m instance_type                        = "t3.medium" [90m-> null[0m[0m
      [31m-[0m[0m ipv6_address_count                   = 0 [90m-> null[0m[0m
      [31m-[0m[0m ipv6_addresses                       = [] [90m-> null[0m[0m
      [31m-[0m[0m monitoring                           = false [90m-> null[0m[0m
      [31m-[0m[0m placement_partition_number           = 0 [90m-> null[0m[0m
      [31m-[0m[0m primary_network_interface_id         = "eni-0cc56583b2c7bc182" [90m-> null[0m[0m
      [31m-[0m[0m private_dns                          = "ip-100-16-0-8.ec2.internal" [90m-> null[0m[0m
      [31m-[0m[0m private_ip                           = "**********" [90m-> null[0m[0m
      [31m-[0m[0m secondary_private_ips                = [] [90m-> null[0m[0m
      [31m-[0m[0m security_groups                      = [] [90m-> null[0m[0m
      [31m-[0m[0m source_dest_check                    = true [90m-> null[0m[0m
      [31m-[0m[0m subnet_id                            = "subnet-06742da032d3c33ed" [90m-> null[0m[0m
      [31m-[0m[0m tags                                 = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-linux-tooling-server"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m tags_all                             = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-linux-tooling-server"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m tenancy                              = "default" [90m-> null[0m[0m
      [31m-[0m[0m user_data                            = "fe81e072b1cb7e992be5c2ce843384cea7dbabc8" [90m-> null[0m[0m
      [31m-[0m[0m user_data_replace_on_change          = false [90m-> null[0m[0m
      [31m-[0m[0m vpc_security_group_ids               = [
          [31m-[0m[0m "sg-04d4b09b1d60de68f",
        ] [90m-> null[0m[0m

      [31m-[0m[0m capacity_reservation_specification {
          [31m-[0m[0m capacity_reservation_preference = "open" [90m-> null[0m[0m
        }

      [31m-[0m[0m cpu_options {
          [31m-[0m[0m core_count       = 1 [90m-> null[0m[0m
          [31m-[0m[0m threads_per_core = 2 [90m-> null[0m[0m
        }

      [31m-[0m[0m credit_specification {
          [31m-[0m[0m cpu_credits = "unlimited" [90m-> null[0m[0m
        }

      [31m-[0m[0m enclave_options {
          [31m-[0m[0m enabled = false [90m-> null[0m[0m
        }

      [31m-[0m[0m maintenance_options {
          [31m-[0m[0m auto_recovery = "default" [90m-> null[0m[0m
        }

      [31m-[0m[0m metadata_options {
          [31m-[0m[0m http_endpoint               = "enabled" [90m-> null[0m[0m
          [31m-[0m[0m http_protocol_ipv6          = "disabled" [90m-> null[0m[0m
          [31m-[0m[0m http_put_response_hop_limit = 1 [90m-> null[0m[0m
          [31m-[0m[0m http_tokens                 = "optional" [90m-> null[0m[0m
          [31m-[0m[0m instance_metadata_tags      = "disabled" [90m-> null[0m[0m
        }

      [31m-[0m[0m private_dns_name_options {
          [31m-[0m[0m enable_resource_name_dns_a_record    = false [90m-> null[0m[0m
          [31m-[0m[0m enable_resource_name_dns_aaaa_record = false [90m-> null[0m[0m
          [31m-[0m[0m hostname_type                        = "ip-name" [90m-> null[0m[0m
        }

      [31m-[0m[0m root_block_device {
          [31m-[0m[0m delete_on_termination = true [90m-> null[0m[0m
          [31m-[0m[0m device_name           = "/dev/xvda" [90m-> null[0m[0m
          [31m-[0m[0m encrypted             = false [90m-> null[0m[0m
          [31m-[0m[0m iops                  = 100 [90m-> null[0m[0m
          [31m-[0m[0m tags                  = {} [90m-> null[0m[0m
          [31m-[0m[0m tags_all              = {} [90m-> null[0m[0m
          [31m-[0m[0m throughput            = 0 [90m-> null[0m[0m
          [31m-[0m[0m volume_id             = "vol-0d6a57a6835016f9d" [90m-> null[0m[0m
          [31m-[0m[0m volume_size           = 8 [90m-> null[0m[0m
          [31m-[0m[0m volume_type           = "gp2" [90m-> null[0m[0m
        }
    }

[1m  # module.windows_tooling_server.aws_instance.this[0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_instance" "this" {
      [31m-[0m[0m ami                                  = "ami-0db3480be03d8d01c" [90m-> null[0m[0m
      [31m-[0m[0m arn                                  = "arn:aws:ec2:us-east-1:046276255144:instance/i-025d0495b9029255b" [90m-> null[0m[0m
      [31m-[0m[0m associate_public_ip_address          = false [90m-> null[0m[0m
      [31m-[0m[0m availability_zone                    = "us-east-1a" [90m-> null[0m[0m
      [31m-[0m[0m cpu_core_count                       = 2 [90m-> null[0m[0m
      [31m-[0m[0m cpu_threads_per_core                 = 2 [90m-> null[0m[0m
      [31m-[0m[0m disable_api_stop                     = false [90m-> null[0m[0m
      [31m-[0m[0m disable_api_termination              = false [90m-> null[0m[0m
      [31m-[0m[0m ebs_optimized                        = false [90m-> null[0m[0m
      [31m-[0m[0m get_password_data                    = false [90m-> null[0m[0m
      [31m-[0m[0m hibernation                          = false [90m-> null[0m[0m
      [31m-[0m[0m iam_instance_profile                 = "infovault-dev-ec2-profile" [90m-> null[0m[0m
      [31m-[0m[0m id                                   = "i-025d0495b9029255b" [90m-> null[0m[0m
      [31m-[0m[0m instance_initiated_shutdown_behavior = "stop" [90m-> null[0m[0m
      [31m-[0m[0m instance_state                       = "running" [90m-> null[0m[0m
      [31m-[0m[0m instance_type                        = "t3.xlarge" [90m-> null[0m[0m
      [31m-[0m[0m ipv6_address_count                   = 0 [90m-> null[0m[0m
      [31m-[0m[0m ipv6_addresses                       = [] [90m-> null[0m[0m
      [31m-[0m[0m monitoring                           = false [90m-> null[0m[0m
      [31m-[0m[0m placement_partition_number           = 0 [90m-> null[0m[0m
      [31m-[0m[0m primary_network_interface_id         = "eni-0d16536dab72049e8" [90m-> null[0m[0m
      [31m-[0m[0m private_dns                          = "ip-100-16-0-7.ec2.internal" [90m-> null[0m[0m
      [31m-[0m[0m private_ip                           = "**********" [90m-> null[0m[0m
      [31m-[0m[0m secondary_private_ips                = [] [90m-> null[0m[0m
      [31m-[0m[0m security_groups                      = [] [90m-> null[0m[0m
      [31m-[0m[0m source_dest_check                    = true [90m-> null[0m[0m
      [31m-[0m[0m subnet_id                            = "subnet-06742da032d3c33ed" [90m-> null[0m[0m
      [31m-[0m[0m tags                                 = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-win-tooling-server"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m tags_all                             = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-win-tooling-server"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m tenancy                              = "default" [90m-> null[0m[0m
      [31m-[0m[0m user_data                            = "5538895354a5916e80d060add45036053b61ac74" [90m-> null[0m[0m
      [31m-[0m[0m user_data_replace_on_change          = false [90m-> null[0m[0m
      [31m-[0m[0m vpc_security_group_ids               = [
          [31m-[0m[0m "sg-04d4b09b1d60de68f",
        ] [90m-> null[0m[0m

      [31m-[0m[0m capacity_reservation_specification {
          [31m-[0m[0m capacity_reservation_preference = "open" [90m-> null[0m[0m
        }

      [31m-[0m[0m cpu_options {
          [31m-[0m[0m core_count       = 2 [90m-> null[0m[0m
          [31m-[0m[0m threads_per_core = 2 [90m-> null[0m[0m
        }

      [31m-[0m[0m credit_specification {
          [31m-[0m[0m cpu_credits = "unlimited" [90m-> null[0m[0m
        }

      [31m-[0m[0m enclave_options {
          [31m-[0m[0m enabled = false [90m-> null[0m[0m
        }

      [31m-[0m[0m maintenance_options {
          [31m-[0m[0m auto_recovery = "default" [90m-> null[0m[0m
        }

      [31m-[0m[0m metadata_options {
          [31m-[0m[0m http_endpoint               = "enabled" [90m-> null[0m[0m
          [31m-[0m[0m http_protocol_ipv6          = "disabled" [90m-> null[0m[0m
          [31m-[0m[0m http_put_response_hop_limit = 1 [90m-> null[0m[0m
          [31m-[0m[0m http_tokens                 = "optional" [90m-> null[0m[0m
          [31m-[0m[0m instance_metadata_tags      = "disabled" [90m-> null[0m[0m
        }

      [31m-[0m[0m private_dns_name_options {
          [31m-[0m[0m enable_resource_name_dns_a_record    = false [90m-> null[0m[0m
          [31m-[0m[0m enable_resource_name_dns_aaaa_record = false [90m-> null[0m[0m
          [31m-[0m[0m hostname_type                        = "ip-name" [90m-> null[0m[0m
        }

      [31m-[0m[0m root_block_device {
          [31m-[0m[0m delete_on_termination = true [90m-> null[0m[0m
          [31m-[0m[0m device_name           = "/dev/sda1" [90m-> null[0m[0m
          [31m-[0m[0m encrypted             = false [90m-> null[0m[0m
          [31m-[0m[0m iops                  = 100 [90m-> null[0m[0m
          [31m-[0m[0m tags                  = {} [90m-> null[0m[0m
          [31m-[0m[0m tags_all              = {} [90m-> null[0m[0m
          [31m-[0m[0m throughput            = 0 [90m-> null[0m[0m
          [31m-[0m[0m volume_id             = "vol-0bfcefd555a52a83c" [90m-> null[0m[0m
          [31m-[0m[0m volume_size           = 30 [90m-> null[0m[0m
          [31m-[0m[0m volume_type           = "gp2" [90m-> null[0m[0m
        }
    }

[1m  # module.eks.module.eks_cluster.aws_eks_addon.amazon_cloudwatch_observability[0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_eks_addon" "amazon_cloudwatch_observability" {
      [31m-[0m[0m addon_name        = "amazon-cloudwatch-observability" [90m-> null[0m[0m
      [31m-[0m[0m addon_version     = "v4.1.0-eksbuild.1" [90m-> null[0m[0m
      [31m-[0m[0m arn               = "arn:aws:eks:us-east-1:046276255144:addon/infovault-dev-eks-cluster-v130/amazon-cloudwatch-observability/40cbac09-96a3-4794-7c7d-58170edb81aa" [90m-> null[0m[0m
      [31m-[0m[0m cluster_name      = "infovault-dev-eks-cluster-v130" [90m-> null[0m[0m
      [31m-[0m[0m created_at        = "2025-06-10T04:22:21Z" [90m-> null[0m[0m
      [31m-[0m[0m id                = "infovault-dev-eks-cluster-v130:amazon-cloudwatch-observability" [90m-> null[0m[0m
      [31m-[0m[0m modified_at       = "2025-06-10T04:22:50Z" [90m-> null[0m[0m
      [31m-[0m[0m resolve_conflicts = "OVERWRITE" [90m-> null[0m[0m
      [31m-[0m[0m tags              = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m tags_all          = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
    }

[1m  # module.eks.module.eks_cluster.aws_eks_addon.aws_ebs_csi_driver[0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_eks_addon" "aws_ebs_csi_driver" {
      [31m-[0m[0m addon_name        = "aws-ebs-csi-driver" [90m-> null[0m[0m
      [31m-[0m[0m addon_version     = "v1.44.0-eksbuild.1" [90m-> null[0m[0m
      [31m-[0m[0m arn               = "arn:aws:eks:us-east-1:046276255144:addon/infovault-dev-eks-cluster-v130/aws-ebs-csi-driver/3acbac09-969e-d1bc-3898-3fe05e0e933d" [90m-> null[0m[0m
      [31m-[0m[0m cluster_name      = "infovault-dev-eks-cluster-v130" [90m-> null[0m[0m
      [31m-[0m[0m created_at        = "2025-06-10T04:22:21Z" [90m-> null[0m[0m
      [31m-[0m[0m id                = "infovault-dev-eks-cluster-v130:aws-ebs-csi-driver" [90m-> null[0m[0m
      [31m-[0m[0m modified_at       = "2025-06-10T04:22:38Z" [90m-> null[0m[0m
      [31m-[0m[0m resolve_conflicts = "OVERWRITE" [90m-> null[0m[0m
      [31m-[0m[0m tags              = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m tags_all          = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
    }

[1m  # module.eks.module.eks_cluster.aws_eks_addon.aws_guardduty_agent[0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_eks_addon" "aws_guardduty_agent" {
      [31m-[0m[0m addon_name        = "aws-guardduty-agent" [90m-> null[0m[0m
      [31m-[0m[0m addon_version     = "v1.10.0-eksbuild.2" [90m-> null[0m[0m
      [31m-[0m[0m arn               = "arn:aws:eks:us-east-1:046276255144:addon/infovault-dev-eks-cluster-v130/aws-guardduty-agent/d2cbac09-969e-1053-3e03-11c6b30efaba" [90m-> null[0m[0m
      [31m-[0m[0m cluster_name      = "infovault-dev-eks-cluster-v130" [90m-> null[0m[0m
      [31m-[0m[0m created_at        = "2025-06-10T04:22:21Z" [90m-> null[0m[0m
      [31m-[0m[0m id                = "infovault-dev-eks-cluster-v130:aws-guardduty-agent" [90m-> null[0m[0m
      [31m-[0m[0m modified_at       = "2025-06-10T04:22:28Z" [90m-> null[0m[0m
      [31m-[0m[0m resolve_conflicts = "OVERWRITE" [90m-> null[0m[0m
      [31m-[0m[0m tags              = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m tags_all          = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
    }

[1m  # module.eks.module.eks_cluster.aws_eks_addon.coredns[0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_eks_addon" "coredns" {
      [31m-[0m[0m addon_name        = "coredns" [90m-> null[0m[0m
      [31m-[0m[0m addon_version     = "v1.11.4-eksbuild.14" [90m-> null[0m[0m
      [31m-[0m[0m arn               = "arn:aws:eks:us-east-1:046276255144:addon/infovault-dev-eks-cluster-v130/coredns/42cbac09-9692-d9d1-5b42-cf1db597ad0c" [90m-> null[0m[0m
      [31m-[0m[0m cluster_name      = "infovault-dev-eks-cluster-v130" [90m-> null[0m[0m
      [31m-[0m[0m created_at        = "2025-06-10T04:22:21Z" [90m-> null[0m[0m
      [31m-[0m[0m id                = "infovault-dev-eks-cluster-v130:coredns" [90m-> null[0m[0m
      [31m-[0m[0m modified_at       = "2025-06-10T04:22:33Z" [90m-> null[0m[0m
      [31m-[0m[0m resolve_conflicts = "OVERWRITE" [90m-> null[0m[0m
      [31m-[0m[0m tags              = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m tags_all          = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
    }

[1m  # module.eks.module.eks_cluster.aws_eks_addon.eks_pod_identity_agent[0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_eks_addon" "eks_pod_identity_agent" {
      [31m-[0m[0m addon_name        = "eks-pod-identity-agent" [90m-> null[0m[0m
      [31m-[0m[0m addon_version     = "v1.3.7-eksbuild.2" [90m-> null[0m[0m
      [31m-[0m[0m arn               = "arn:aws:eks:us-east-1:046276255144:addon/infovault-dev-eks-cluster-v130/eks-pod-identity-agent/f6cbac09-9695-7937-5e53-6cf8527b79b7" [90m-> null[0m[0m
      [31m-[0m[0m cluster_name      = "infovault-dev-eks-cluster-v130" [90m-> null[0m[0m
      [31m-[0m[0m created_at        = "2025-06-10T04:22:21Z" [90m-> null[0m[0m
      [31m-[0m[0m id                = "infovault-dev-eks-cluster-v130:eks-pod-identity-agent" [90m-> null[0m[0m
      [31m-[0m[0m modified_at       = "2025-06-10T04:22:27Z" [90m-> null[0m[0m
      [31m-[0m[0m resolve_conflicts = "OVERWRITE" [90m-> null[0m[0m
      [31m-[0m[0m tags              = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m tags_all          = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
    }

[1m  # module.eks.module.eks_cluster.aws_eks_addon.kube_proxy[0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_eks_addon" "kube_proxy" {
      [31m-[0m[0m addon_name        = "kube-proxy" [90m-> null[0m[0m
      [31m-[0m[0m addon_version     = "v1.32.0-eksbuild.2" [90m-> null[0m[0m
      [31m-[0m[0m arn               = "arn:aws:eks:us-east-1:046276255144:addon/infovault-dev-eks-cluster-v130/kube-proxy/d8cbac09-969c-8a4d-8bca-5c9ff226d0e7" [90m-> null[0m[0m
      [31m-[0m[0m cluster_name      = "infovault-dev-eks-cluster-v130" [90m-> null[0m[0m
      [31m-[0m[0m created_at        = "2025-06-10T04:22:21Z" [90m-> null[0m[0m
      [31m-[0m[0m id                = "infovault-dev-eks-cluster-v130:kube-proxy" [90m-> null[0m[0m
      [31m-[0m[0m modified_at       = "2025-06-10T04:22:28Z" [90m-> null[0m[0m
      [31m-[0m[0m resolve_conflicts = "OVERWRITE" [90m-> null[0m[0m
      [31m-[0m[0m tags              = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m tags_all          = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
    }

[1m  # module.eks.module.eks_cluster.aws_eks_addon.vpc_cni[0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_eks_addon" "vpc_cni" {
      [31m-[0m[0m addon_name        = "vpc-cni" [90m-> null[0m[0m
      [31m-[0m[0m addon_version     = "v1.19.5-eksbuild.3" [90m-> null[0m[0m
      [31m-[0m[0m arn               = "arn:aws:eks:us-east-1:046276255144:addon/infovault-dev-eks-cluster-v130/vpc-cni/d2cbac09-9694-4694-bac4-6dbcbef3fd23" [90m-> null[0m[0m
      [31m-[0m[0m cluster_name      = "infovault-dev-eks-cluster-v130" [90m-> null[0m[0m
      [31m-[0m[0m created_at        = "2025-06-10T04:22:21Z" [90m-> null[0m[0m
      [31m-[0m[0m id                = "infovault-dev-eks-cluster-v130:vpc-cni" [90m-> null[0m[0m
      [31m-[0m[0m modified_at       = "2025-06-10T04:22:28Z" [90m-> null[0m[0m
      [31m-[0m[0m resolve_conflicts = "OVERWRITE" [90m-> null[0m[0m
      [31m-[0m[0m tags              = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m tags_all          = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
    }

[1m  # module.eks.module.eks_cluster.aws_eks_cluster.this[0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_eks_cluster" "this" {
      [31m-[0m[0m arn                           = "arn:aws:eks:us-east-1:046276255144:cluster/infovault-dev-eks-cluster-v130" [90m-> null[0m[0m
      [31m-[0m[0m bootstrap_self_managed_addons = true [90m-> null[0m[0m
      [31m-[0m[0m certificate_authority         = [
          [31m-[0m[0m {
              [31m-[0m[0m data = "LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURCVENDQWUyZ0F3SUJBZ0lJR3I5ei95ZHZYZ3N3RFFZSktvWklodmNOQVFFTEJRQXdGVEVUTUJFR0ExVUUKQXhNS2EzVmlaWEp1WlhSbGN6QWVGdzB5TlRBMk1UQXdOREV3TVRaYUZ3MHpOVEEyTURnd05ERTFNVFphTUJVeApFekFSQmdOVkJBTVRDbXQxWW1WeWJtVjBaWE13Z2dFaU1BMEdDU3FHU0liM0RRRUJBUVVBQTRJQkR3QXdnZ0VLCkFvSUJBUURVai9WeDY2bUhWNXowRlR3RnNXdXUxYm1RWDQvT2lhTE9Rd1pTeU5SUVBtWGEwSUd1eXUwUjRlN3YKRWMwNVFEQ2FWdG5BTm5BYVhuZ3o3ZGpRZ0IxeHVCSW8wNzhwcFA2S0RyMTVWTk1RdzFmSG1GTVN3T2ZhS3NZdQpnZHhOSTVybkpoa1lZeW1ncE1XYVlVeFJjbm5WWVFHaEQ3Slh3TE1LYXJ5U2VJUkt1U0RKUHBhbEtRNFNyZEpYClppS0labWEvR0xOaUtRc1NsbXdncHJBWVhZcFhqU2I0R3R0eXo1c1FraTlOekdmRVVOUms3QzR5UVc3cGtnMmcKM1FMY2VveTE4UEtFRkJHZ2kwcEdONVZ0MmsrMkUwR3YyU2c3MnZVQ1JaT01ZVHJkcm5ReUkyRlJybU5MbWNlbgp3RTRmaFV0S21PdHpubUxBc3dTT2tiQkFydXJkQWdNQkFBR2pXVEJYTUE0R0ExVWREd0VCL3dRRUF3SUNwREFQCkJnTlZIUk1CQWY4RUJUQURBUUgvTUIwR0ExVWREZ1FXQkJTQm1lamhKdk9UeExueUxuMGNIb0wyWmNUdUV6QVYKQmdOVkhSRUVEakFNZ2dwcmRXSmxjbTVsZEdWek1BMEdDU3FHU0liM0RRRUJDd1VBQTRJQkFRQnBZc3V6MVpxbwp2SkthZCtOdmxSUlZjRjFBc0J3cHNEbkI2WkhHYkZWeENSbUdYdlIwOG55cVRqNEI5OFpNVnlOSG94ZjdBY1gvCjJMTGowWGdSSDBNOGNXL3M4ZS8rREVVOTM3MVc0Y1VNLzVWcXE4WDVST0hLRTJrZHFTY1NYR0t3Zis1N3NvQ1QKR1JrWFB2UCtoWFdEbCtaMkNtNE9CMGFmZ2p3b2FDdFZGV1lsUGU1dGZpUmUvRXMrZ2c5d0ZxZDdQWWhySThocQpWMy84KzVEYkMyU1pGQmNHMjg4VVIvUlJBVjE0eEJseFllWVJJVTg3VkQ4VzlIa0lET3BvRmtMQXlnSXB1L0hrCndLdHhmUm82OFgyTS8wMTRyYUlFVFBUMk1lU0g2MmpQdDZzWWFGeGwydFVaelRiSzg4WmZYc3BXWkMzQzlQNEsKM21tTVFRVzAvcHVOCi0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K"
            },
        ] [90m-> null[0m[0m
      [31m-[0m[0m created_at                    = "2025-06-10T04:10:38Z" [90m-> null[0m[0m
      [31m-[0m[0m enabled_cluster_log_types     = [
          [31m-[0m[0m "api",
          [31m-[0m[0m "audit",
          [31m-[0m[0m "authenticator",
          [31m-[0m[0m "controllerManager",
          [31m-[0m[0m "scheduler",
        ] [90m-> null[0m[0m
      [31m-[0m[0m endpoint                      = "https://DF6BE70E448A43C57BB0D1E67B1C1A5F.gr7.us-east-1.eks.amazonaws.com" [90m-> null[0m[0m
      [31m-[0m[0m id                            = "infovault-dev-eks-cluster-v130" [90m-> null[0m[0m
      [31m-[0m[0m identity                      = [
          [31m-[0m[0m {
              [31m-[0m[0m oidc = [
                  [31m-[0m[0m {
                      [31m-[0m[0m issuer = "https://oidc.eks.us-east-1.amazonaws.com/id/DF6BE70E448A43C57BB0D1E67B1C1A5F"
                    },
                ]
            },
        ] [90m-> null[0m[0m
      [31m-[0m[0m name                          = "infovault-dev-eks-cluster-v130" [90m-> null[0m[0m
      [31m-[0m[0m platform_version              = "eks.11" [90m-> null[0m[0m
      [31m-[0m[0m role_arn                      = "arn:aws:iam::046276255144:role/eks-cluster-role-infovault-dev-eks-cluster-v130" [90m-> null[0m[0m
      [31m-[0m[0m status                        = "ACTIVE" [90m-> null[0m[0m
      [31m-[0m[0m tags                          = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m tags_all                      = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m version                       = "1.32" [90m-> null[0m[0m

      [31m-[0m[0m access_config {
          [31m-[0m[0m authentication_mode                         = "CONFIG_MAP" [90m-> null[0m[0m
          [31m-[0m[0m bootstrap_cluster_creator_admin_permissions = true [90m-> null[0m[0m
        }

      [31m-[0m[0m kubernetes_network_config {
          [31m-[0m[0m ip_family         = "ipv4" [90m-> null[0m[0m
          [31m-[0m[0m service_ipv4_cidr = "**********/16" [90m-> null[0m[0m

          [31m-[0m[0m elastic_load_balancing {
              [31m-[0m[0m enabled = false [90m-> null[0m[0m
            }
        }

      [31m-[0m[0m upgrade_policy {
          [31m-[0m[0m support_type = "EXTENDED" [90m-> null[0m[0m
        }

      [31m-[0m[0m vpc_config {
          [31m-[0m[0m cluster_security_group_id = "sg-085f797931174828b" [90m-> null[0m[0m
          [31m-[0m[0m endpoint_private_access   = true [90m-> null[0m[0m
          [31m-[0m[0m endpoint_public_access    = true [90m-> null[0m[0m
          [31m-[0m[0m public_access_cidrs       = [
              [31m-[0m[0m "0.0.0.0/0",
            ] [90m-> null[0m[0m
          [31m-[0m[0m security_group_ids        = [] [90m-> null[0m[0m
          [31m-[0m[0m subnet_ids                = [
              [31m-[0m[0m "subnet-00a5a802525196bba",
              [31m-[0m[0m "subnet-087a114ef79afa85f",
            ] [90m-> null[0m[0m
          [31m-[0m[0m vpc_id                    = "vpc-02bb90f92fd70e6f6" [90m-> null[0m[0m
        }
    }

[1m  # module.eks.module.eks_cluster.aws_eks_node_group.main[0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_eks_node_group" "main" {
      [31m-[0m[0m ami_type        = "AL2_x86_64" [90m-> null[0m[0m
      [31m-[0m[0m arn             = "arn:aws:eks:us-east-1:046276255144:nodegroup/infovault-dev-eks-cluster-v130/v130-node-group-3/e6cbac09-9696-0a4f-ef72-5f25c74e2dac" [90m-> null[0m[0m
      [31m-[0m[0m capacity_type   = "ON_DEMAND" [90m-> null[0m[0m
      [31m-[0m[0m cluster_name    = "infovault-dev-eks-cluster-v130" [90m-> null[0m[0m
      [31m-[0m[0m disk_size       = 20 [90m-> null[0m[0m
      [31m-[0m[0m id              = "infovault-dev-eks-cluster-v130:v130-node-group-3" [90m-> null[0m[0m
      [31m-[0m[0m instance_types  = [
          [31m-[0m[0m "t3.medium",
        ] [90m-> null[0m[0m
      [31m-[0m[0m labels          = {
          [31m-[0m[0m "environment" = "dev"
          [31m-[0m[0m "role"        = "application"
        } [90m-> null[0m[0m
      [31m-[0m[0m node_group_name = "v130-node-group-3" [90m-> null[0m[0m
      [31m-[0m[0m node_role_arn   = "arn:aws:iam::046276255144:role/eks-nodegroup-role-infovault-dev-eks-cluster-v130" [90m-> null[0m[0m
      [31m-[0m[0m release_version = "1.32.3-20250519" [90m-> null[0m[0m
      [31m-[0m[0m resources       = [
          [31m-[0m[0m {
              [31m-[0m[0m autoscaling_groups              = [
                  [31m-[0m[0m {
                      [31m-[0m[0m name = "eks-v130-node-group-3-e6cbac09-9696-0a4f-ef72-5f25c74e2dac"
                    },
                ]
              [31m-[0m[0m remote_access_security_group_id = ""
            },
        ] [90m-> null[0m[0m
      [31m-[0m[0m status          = "CREATE_FAILED" [90m-> null[0m[0m
      [31m-[0m[0m subnet_ids      = [
          [31m-[0m[0m "subnet-00a5a802525196bba",
          [31m-[0m[0m "subnet-087a114ef79afa85f",
        ] [90m-> null[0m[0m
      [31m-[0m[0m tags            = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m tags_all        = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m version         = "1.32" [90m-> null[0m[0m

      [31m-[0m[0m scaling_config {
          [31m-[0m[0m desired_size = 2 [90m-> null[0m[0m
          [31m-[0m[0m max_size     = 4 [90m-> null[0m[0m
          [31m-[0m[0m min_size     = 1 [90m-> null[0m[0m
        }

      [31m-[0m[0m update_config {
          [31m-[0m[0m max_unavailable            = 1 [90m-> null[0m[0m
          [31m-[0m[0m max_unavailable_percentage = 0 [90m-> null[0m[0m
        }
    }

[1m  # module.eks.module.eks_cluster.aws_iam_role.cluster[0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_iam_role" "cluster" {
      [31m-[0m[0m arn                   = "arn:aws:iam::046276255144:role/eks-cluster-role-infovault-dev-eks-cluster-v130" [90m-> null[0m[0m
      [31m-[0m[0m assume_role_policy    = jsonencode(
            {
              [31m-[0m[0m Statement = [
                  [31m-[0m[0m {
                      [31m-[0m[0m Action    = "sts:AssumeRole"
                      [31m-[0m[0m Effect    = "Allow"
                      [31m-[0m[0m Principal = {
                          [31m-[0m[0m Service = "eks.amazonaws.com"
                        }
                    },
                ]
              [31m-[0m[0m Version   = "2012-10-17"
            }
        ) [90m-> null[0m[0m
      [31m-[0m[0m create_date           = "2025-06-10T04:10:34Z" [90m-> null[0m[0m
      [31m-[0m[0m force_detach_policies = false [90m-> null[0m[0m
      [31m-[0m[0m id                    = "eks-cluster-role-infovault-dev-eks-cluster-v130" [90m-> null[0m[0m
      [31m-[0m[0m managed_policy_arns   = [
          [31m-[0m[0m "arn:aws:iam::aws:policy/AmazonEKSClusterPolicy",
        ] [90m-> null[0m[0m
      [31m-[0m[0m max_session_duration  = 3600 [90m-> null[0m[0m
      [31m-[0m[0m name                  = "eks-cluster-role-infovault-dev-eks-cluster-v130" [90m-> null[0m[0m
      [31m-[0m[0m path                  = "/" [90m-> null[0m[0m
      [31m-[0m[0m tags                  = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m tags_all              = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m unique_id             = "AROAQVRSHTWUL2PKALZJ4" [90m-> null[0m[0m
    }

[1m  # module.eks.module.eks_cluster.aws_iam_role.node_group[0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_iam_role" "node_group" {
      [31m-[0m[0m arn                   = "arn:aws:iam::046276255144:role/eks-nodegroup-role-infovault-dev-eks-cluster-v130" [90m-> null[0m[0m
      [31m-[0m[0m assume_role_policy    = jsonencode(
            {
              [31m-[0m[0m Statement = [
                  [31m-[0m[0m {
                      [31m-[0m[0m Action    = "sts:AssumeRole"
                      [31m-[0m[0m Effect    = "Allow"
                      [31m-[0m[0m Principal = {
                          [31m-[0m[0m Service = "ec2.amazonaws.com"
                        }
                    },
                ]
              [31m-[0m[0m Version   = "2012-10-17"
            }
        ) [90m-> null[0m[0m
      [31m-[0m[0m create_date           = "2025-06-10T04:10:34Z" [90m-> null[0m[0m
      [31m-[0m[0m force_detach_policies = false [90m-> null[0m[0m
      [31m-[0m[0m id                    = "eks-nodegroup-role-infovault-dev-eks-cluster-v130" [90m-> null[0m[0m
      [31m-[0m[0m managed_policy_arns   = [
          [31m-[0m[0m "arn:aws:iam::aws:policy/AmazonEC2ContainerRegistryReadOnly",
          [31m-[0m[0m "arn:aws:iam::aws:policy/AmazonEKSWorkerNodePolicy",
          [31m-[0m[0m "arn:aws:iam::aws:policy/AmazonEKS_CNI_Policy",
        ] [90m-> null[0m[0m
      [31m-[0m[0m max_session_duration  = 3600 [90m-> null[0m[0m
      [31m-[0m[0m name                  = "eks-nodegroup-role-infovault-dev-eks-cluster-v130" [90m-> null[0m[0m
      [31m-[0m[0m path                  = "/" [90m-> null[0m[0m
      [31m-[0m[0m tags                  = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m tags_all              = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m unique_id             = "AROAQVRSHTWUPVWI6UZCD" [90m-> null[0m[0m
    }

[1m  # module.eks.module.eks_cluster.aws_iam_role_policy_attachment.cluster_AmazonEKSClusterPolicy[0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_iam_role_policy_attachment" "cluster_AmazonEKSClusterPolicy" {
      [31m-[0m[0m id         = "eks-cluster-role-infovault-dev-eks-cluster-v130-20250610041035767200000003" [90m-> null[0m[0m
      [31m-[0m[0m policy_arn = "arn:aws:iam::aws:policy/AmazonEKSClusterPolicy" [90m-> null[0m[0m
      [31m-[0m[0m role       = "eks-cluster-role-infovault-dev-eks-cluster-v130" [90m-> null[0m[0m
    }

[1m  # module.eks.module.eks_cluster.aws_iam_role_policy_attachment.node_group_AmazonEC2ContainerRegistryReadOnly[0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_iam_role_policy_attachment" "node_group_AmazonEC2ContainerRegistryReadOnly" {
      [31m-[0m[0m id         = "eks-nodegroup-role-infovault-dev-eks-cluster-v130-20250610041035813300000004" [90m-> null[0m[0m
      [31m-[0m[0m policy_arn = "arn:aws:iam::aws:policy/AmazonEC2ContainerRegistryReadOnly" [90m-> null[0m[0m
      [31m-[0m[0m role       = "eks-nodegroup-role-infovault-dev-eks-cluster-v130" [90m-> null[0m[0m
    }

[1m  # module.eks.module.eks_cluster.aws_iam_role_policy_attachment.node_group_AmazonEKSWorkerNodePolicy[0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_iam_role_policy_attachment" "node_group_AmazonEKSWorkerNodePolicy" {
      [31m-[0m[0m id         = "eks-nodegroup-role-infovault-dev-eks-cluster-v130-20250610041035456200000001" [90m-> null[0m[0m
      [31m-[0m[0m policy_arn = "arn:aws:iam::aws:policy/AmazonEKSWorkerNodePolicy" [90m-> null[0m[0m
      [31m-[0m[0m role       = "eks-nodegroup-role-infovault-dev-eks-cluster-v130" [90m-> null[0m[0m
    }

[1m  # module.eks.module.eks_cluster.aws_iam_role_policy_attachment.node_group_AmazonEKS_CNI_Policy[0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_iam_role_policy_attachment" "node_group_AmazonEKS_CNI_Policy" {
      [31m-[0m[0m id         = "eks-nodegroup-role-infovault-dev-eks-cluster-v130-20250610041035480000000002" [90m-> null[0m[0m
      [31m-[0m[0m policy_arn = "arn:aws:iam::aws:policy/AmazonEKS_CNI_Policy" [90m-> null[0m[0m
      [31m-[0m[0m role       = "eks-nodegroup-role-infovault-dev-eks-cluster-v130" [90m-> null[0m[0m
    }

[1m  # module.gen_facing.module.gen_facing_vpc.aws_internet_gateway.main[0][0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_internet_gateway" "main" {
      [31m-[0m[0m arn      = "arn:aws:ec2:us-east-1:046276255144:internet-gateway/igw-0e53990e6d46ee42c" [90m-> null[0m[0m
      [31m-[0m[0m id       = "igw-0e53990e6d46ee42c" [90m-> null[0m[0m
      [31m-[0m[0m owner_id = "046276255144" [90m-> null[0m[0m
      [31m-[0m[0m tags     = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-gen-facing-compartment-igw"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m tags_all = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-gen-facing-compartment-igw"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m vpc_id   = "vpc-02bb90f92fd70e6f6" [90m-> null[0m[0m
    }

[1m  # module.gen_facing.module.gen_facing_vpc.aws_route.public_internet["gen_public_rt"][0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_route" "public_internet" {
      [31m-[0m[0m destination_cidr_block = "0.0.0.0/0" [90m-> null[0m[0m
      [31m-[0m[0m gateway_id             = "igw-0e53990e6d46ee42c" [90m-> null[0m[0m
      [31m-[0m[0m id                     = "r-rtb-08755f425d9cedaf61080289494" [90m-> null[0m[0m
      [31m-[0m[0m origin                 = "CreateRoute" [90m-> null[0m[0m
      [31m-[0m[0m route_table_id         = "rtb-08755f425d9cedaf6" [90m-> null[0m[0m
      [31m-[0m[0m state                  = "active" [90m-> null[0m[0m
    }

[1m  # module.gen_facing.module.gen_facing_vpc.aws_route_table.private["gen_private_rt"][0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_route_table" "private" {
      [31m-[0m[0m arn              = "arn:aws:ec2:us-east-1:046276255144:route-table/rtb-096f1decba5710de5" [90m-> null[0m[0m
      [31m-[0m[0m id               = "rtb-096f1decba5710de5" [90m-> null[0m[0m
      [31m-[0m[0m owner_id         = "046276255144" [90m-> null[0m[0m
      [31m-[0m[0m propagating_vgws = [] [90m-> null[0m[0m
      [31m-[0m[0m route            = [] [90m-> null[0m[0m
      [31m-[0m[0m tags             = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-gen-private-route-table"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "Type"        = "Private"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m tags_all         = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-gen-private-route-table"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "Type"        = "Private"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m vpc_id           = "vpc-02bb90f92fd70e6f6" [90m-> null[0m[0m
    }

[1m  # module.gen_facing.module.gen_facing_vpc.aws_route_table.public["gen_public_rt"][0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_route_table" "public" {
      [31m-[0m[0m arn              = "arn:aws:ec2:us-east-1:046276255144:route-table/rtb-08755f425d9cedaf6" [90m-> null[0m[0m
      [31m-[0m[0m id               = "rtb-08755f425d9cedaf6" [90m-> null[0m[0m
      [31m-[0m[0m owner_id         = "046276255144" [90m-> null[0m[0m
      [31m-[0m[0m propagating_vgws = [] [90m-> null[0m[0m
      [31m-[0m[0m route            = [
          [31m-[0m[0m {
              [31m-[0m[0m carrier_gateway_id         = ""
              [31m-[0m[0m cidr_block                 = "0.0.0.0/0"
              [31m-[0m[0m core_network_arn           = ""
              [31m-[0m[0m destination_prefix_list_id = ""
              [31m-[0m[0m egress_only_gateway_id     = ""
              [31m-[0m[0m gateway_id                 = "igw-0e53990e6d46ee42c"
              [31m-[0m[0m ipv6_cidr_block            = ""
              [31m-[0m[0m local_gateway_id           = ""
              [31m-[0m[0m nat_gateway_id             = ""
              [31m-[0m[0m network_interface_id       = ""
              [31m-[0m[0m transit_gateway_id         = ""
              [31m-[0m[0m vpc_endpoint_id            = ""
              [31m-[0m[0m vpc_peering_connection_id  = ""
            },
        ] [90m-> null[0m[0m
      [31m-[0m[0m tags             = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-gen-public-route-table"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "Type"        = "Public"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m tags_all         = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-gen-public-route-table"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "Type"        = "Public"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m vpc_id           = "vpc-02bb90f92fd70e6f6" [90m-> null[0m[0m
    }

[1m  # module.gen_facing.module.gen_facing_vpc.aws_route_table_association.private["app_subnet_az1_assoc"][0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_route_table_association" "private" {
      [31m-[0m[0m id             = "rtbassoc-0f38bb5f20a981d74" [90m-> null[0m[0m
      [31m-[0m[0m route_table_id = "rtb-096f1decba5710de5" [90m-> null[0m[0m
      [31m-[0m[0m subnet_id      = "subnet-087a114ef79afa85f" [90m-> null[0m[0m
    }

[1m  # module.gen_facing.module.gen_facing_vpc.aws_route_table_association.private["app_subnet_az2_assoc"][0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_route_table_association" "private" {
      [31m-[0m[0m id             = "rtbassoc-098db8c0990416e4a" [90m-> null[0m[0m
      [31m-[0m[0m route_table_id = "rtb-096f1decba5710de5" [90m-> null[0m[0m
      [31m-[0m[0m subnet_id      = "subnet-00a5a802525196bba" [90m-> null[0m[0m
    }

[1m  # module.gen_facing.module.gen_facing_vpc.aws_route_table_association.private["db_subnet_az1_assoc"][0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_route_table_association" "private" {
      [31m-[0m[0m id             = "rtbassoc-07c5a7158739f34a1" [90m-> null[0m[0m
      [31m-[0m[0m route_table_id = "rtb-096f1decba5710de5" [90m-> null[0m[0m
      [31m-[0m[0m subnet_id      = "subnet-01ae797696c34b263" [90m-> null[0m[0m
    }

[1m  # module.gen_facing.module.gen_facing_vpc.aws_route_table_association.private["db_subnet_az2_assoc"][0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_route_table_association" "private" {
      [31m-[0m[0m id             = "rtbassoc-01d2c7645ff9a7ac3" [90m-> null[0m[0m
      [31m-[0m[0m route_table_id = "rtb-096f1decba5710de5" [90m-> null[0m[0m
      [31m-[0m[0m subnet_id      = "subnet-0b749c2f19e463feb" [90m-> null[0m[0m
    }

[1m  # module.gen_facing.module.gen_facing_vpc.aws_route_table_association.private["gen_egress_assoc"][0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_route_table_association" "private" {
      [31m-[0m[0m id             = "rtbassoc-0de2892917acaee0c" [90m-> null[0m[0m
      [31m-[0m[0m route_table_id = "rtb-096f1decba5710de5" [90m-> null[0m[0m
      [31m-[0m[0m subnet_id      = "subnet-02163189325121054" [90m-> null[0m[0m
    }

[1m  # module.gen_facing.module.gen_facing_vpc.aws_route_table_association.private["gen_ingress_assoc"][0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_route_table_association" "private" {
      [31m-[0m[0m id             = "rtbassoc-004dac3395ab894e9" [90m-> null[0m[0m
      [31m-[0m[0m route_table_id = "rtb-096f1decba5710de5" [90m-> null[0m[0m
      [31m-[0m[0m subnet_id      = "subnet-0d670456ac150e9b7" [90m-> null[0m[0m
    }

[1m  # module.gen_facing.module.gen_facing_vpc.aws_route_table_association.private["gen_migration_assoc"][0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_route_table_association" "private" {
      [31m-[0m[0m id             = "rtbassoc-003bfe0c4415a10ed" [90m-> null[0m[0m
      [31m-[0m[0m route_table_id = "rtb-096f1decba5710de5" [90m-> null[0m[0m
      [31m-[0m[0m subnet_id      = "subnet-06ef006f5416a0e77" [90m-> null[0m[0m
    }

[1m  # module.gen_facing.module.gen_facing_vpc.aws_route_table_association.private["smtp_subnet_dlz1_assoc"][0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_route_table_association" "private" {
      [31m-[0m[0m id             = "rtbassoc-0394faea4b2e89be1" [90m-> null[0m[0m
      [31m-[0m[0m route_table_id = "rtb-096f1decba5710de5" [90m-> null[0m[0m
      [31m-[0m[0m subnet_id      = "subnet-048d2c6ad6104bce1" [90m-> null[0m[0m
    }

[1m  # module.gen_facing.module.gen_facing_vpc.aws_route_table_association.public["alb_subnet_az1_assoc"][0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_route_table_association" "public" {
      [31m-[0m[0m id             = "rtbassoc-05b2897e83d8f4dae" [90m-> null[0m[0m
      [31m-[0m[0m route_table_id = "rtb-08755f425d9cedaf6" [90m-> null[0m[0m
      [31m-[0m[0m subnet_id      = "subnet-0b93f0452a90bb94b" [90m-> null[0m[0m
    }

[1m  # module.gen_facing.module.gen_facing_vpc.aws_subnet.private["app_subnet_az1"][0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_subnet" "private" {
      [31m-[0m[0m arn                                            = "arn:aws:ec2:us-east-1:046276255144:subnet/subnet-087a114ef79afa85f" [90m-> null[0m[0m
      [31m-[0m[0m assign_ipv6_address_on_creation                = false [90m-> null[0m[0m
      [31m-[0m[0m availability_zone                              = "us-east-1a" [90m-> null[0m[0m
      [31m-[0m[0m availability_zone_id                           = "use1-az6" [90m-> null[0m[0m
      [31m-[0m[0m cidr_block                                     = "**********/24" [90m-> null[0m[0m
      [31m-[0m[0m enable_dns64                                   = false [90m-> null[0m[0m
      [31m-[0m[0m enable_lni_at_device_index                     = 0 [90m-> null[0m[0m
      [31m-[0m[0m enable_resource_name_dns_a_record_on_launch    = false [90m-> null[0m[0m
      [31m-[0m[0m enable_resource_name_dns_aaaa_record_on_launch = false [90m-> null[0m[0m
      [31m-[0m[0m id                                             = "subnet-087a114ef79afa85f" [90m-> null[0m[0m
      [31m-[0m[0m ipv6_native                                    = false [90m-> null[0m[0m
      [31m-[0m[0m map_customer_owned_ip_on_launch                = false [90m-> null[0m[0m
      [31m-[0m[0m map_public_ip_on_launch                        = false [90m-> null[0m[0m
      [31m-[0m[0m owner_id                                       = "046276255144" [90m-> null[0m[0m
      [31m-[0m[0m private_dns_hostname_type_on_launch            = "ip-name" [90m-> null[0m[0m
      [31m-[0m[0m tags                                           = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-app-subnet-az1"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "Type"        = "Private"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m tags_all                                       = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-app-subnet-az1"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "Type"        = "Private"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m vpc_id                                         = "vpc-02bb90f92fd70e6f6" [90m-> null[0m[0m
    }

[1m  # module.gen_facing.module.gen_facing_vpc.aws_subnet.private["app_subnet_az2"][0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_subnet" "private" {
      [31m-[0m[0m arn                                            = "arn:aws:ec2:us-east-1:046276255144:subnet/subnet-00a5a802525196bba" [90m-> null[0m[0m
      [31m-[0m[0m assign_ipv6_address_on_creation                = false [90m-> null[0m[0m
      [31m-[0m[0m availability_zone                              = "us-east-1b" [90m-> null[0m[0m
      [31m-[0m[0m availability_zone_id                           = "use1-az1" [90m-> null[0m[0m
      [31m-[0m[0m cidr_block                                     = "**********/24" [90m-> null[0m[0m
      [31m-[0m[0m enable_dns64                                   = false [90m-> null[0m[0m
      [31m-[0m[0m enable_lni_at_device_index                     = 0 [90m-> null[0m[0m
      [31m-[0m[0m enable_resource_name_dns_a_record_on_launch    = false [90m-> null[0m[0m
      [31m-[0m[0m enable_resource_name_dns_aaaa_record_on_launch = false [90m-> null[0m[0m
      [31m-[0m[0m id                                             = "subnet-00a5a802525196bba" [90m-> null[0m[0m
      [31m-[0m[0m ipv6_native                                    = false [90m-> null[0m[0m
      [31m-[0m[0m map_customer_owned_ip_on_launch                = false [90m-> null[0m[0m
      [31m-[0m[0m map_public_ip_on_launch                        = false [90m-> null[0m[0m
      [31m-[0m[0m owner_id                                       = "046276255144" [90m-> null[0m[0m
      [31m-[0m[0m private_dns_hostname_type_on_launch            = "ip-name" [90m-> null[0m[0m
      [31m-[0m[0m tags                                           = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-app-subnet-az2"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "Type"        = "Private"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m tags_all                                       = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-app-subnet-az2"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "Type"        = "Private"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m vpc_id                                         = "vpc-02bb90f92fd70e6f6" [90m-> null[0m[0m
    }

[1m  # module.gen_facing.module.gen_facing_vpc.aws_subnet.private["db_subnet_az1"][0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_subnet" "private" {
      [31m-[0m[0m arn                                            = "arn:aws:ec2:us-east-1:046276255144:subnet/subnet-01ae797696c34b263" [90m-> null[0m[0m
      [31m-[0m[0m assign_ipv6_address_on_creation                = false [90m-> null[0m[0m
      [31m-[0m[0m availability_zone                              = "us-east-1a" [90m-> null[0m[0m
      [31m-[0m[0m availability_zone_id                           = "use1-az6" [90m-> null[0m[0m
      [31m-[0m[0m cidr_block                                     = "**********/26" [90m-> null[0m[0m
      [31m-[0m[0m enable_dns64                                   = false [90m-> null[0m[0m
      [31m-[0m[0m enable_lni_at_device_index                     = 0 [90m-> null[0m[0m
      [31m-[0m[0m enable_resource_name_dns_a_record_on_launch    = false [90m-> null[0m[0m
      [31m-[0m[0m enable_resource_name_dns_aaaa_record_on_launch = false [90m-> null[0m[0m
      [31m-[0m[0m id                                             = "subnet-01ae797696c34b263" [90m-> null[0m[0m
      [31m-[0m[0m ipv6_native                                    = false [90m-> null[0m[0m
      [31m-[0m[0m map_customer_owned_ip_on_launch                = false [90m-> null[0m[0m
      [31m-[0m[0m map_public_ip_on_launch                        = false [90m-> null[0m[0m
      [31m-[0m[0m owner_id                                       = "046276255144" [90m-> null[0m[0m
      [31m-[0m[0m private_dns_hostname_type_on_launch            = "ip-name" [90m-> null[0m[0m
      [31m-[0m[0m tags                                           = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-db-subnet-az1"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "Type"        = "Private"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m tags_all                                       = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-db-subnet-az1"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "Type"        = "Private"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m vpc_id                                         = "vpc-02bb90f92fd70e6f6" [90m-> null[0m[0m
    }

[1m  # module.gen_facing.module.gen_facing_vpc.aws_subnet.private["db_subnet_az2"][0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_subnet" "private" {
      [31m-[0m[0m arn                                            = "arn:aws:ec2:us-east-1:046276255144:subnet/subnet-0b749c2f19e463feb" [90m-> null[0m[0m
      [31m-[0m[0m assign_ipv6_address_on_creation                = false [90m-> null[0m[0m
      [31m-[0m[0m availability_zone                              = "us-east-1b" [90m-> null[0m[0m
      [31m-[0m[0m availability_zone_id                           = "use1-az1" [90m-> null[0m[0m
      [31m-[0m[0m cidr_block                                     = "***********/26" [90m-> null[0m[0m
      [31m-[0m[0m enable_dns64                                   = false [90m-> null[0m[0m
      [31m-[0m[0m enable_lni_at_device_index                     = 0 [90m-> null[0m[0m
      [31m-[0m[0m enable_resource_name_dns_a_record_on_launch    = false [90m-> null[0m[0m
      [31m-[0m[0m enable_resource_name_dns_aaaa_record_on_launch = false [90m-> null[0m[0m
      [31m-[0m[0m id                                             = "subnet-0b749c2f19e463feb" [90m-> null[0m[0m
      [31m-[0m[0m ipv6_native                                    = false [90m-> null[0m[0m
      [31m-[0m[0m map_customer_owned_ip_on_launch                = false [90m-> null[0m[0m
      [31m-[0m[0m map_public_ip_on_launch                        = false [90m-> null[0m[0m
      [31m-[0m[0m owner_id                                       = "046276255144" [90m-> null[0m[0m
      [31m-[0m[0m private_dns_hostname_type_on_launch            = "ip-name" [90m-> null[0m[0m
      [31m-[0m[0m tags                                           = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-db-subnet-az2"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "Type"        = "Private"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m tags_all                                       = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-db-subnet-az2"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "Type"        = "Private"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m vpc_id                                         = "vpc-02bb90f92fd70e6f6" [90m-> null[0m[0m
    }

[1m  # module.gen_facing.module.gen_facing_vpc.aws_subnet.private["gen_egress_az1"][0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_subnet" "private" {
      [31m-[0m[0m arn                                            = "arn:aws:ec2:us-east-1:046276255144:subnet/subnet-02163189325121054" [90m-> null[0m[0m
      [31m-[0m[0m assign_ipv6_address_on_creation                = false [90m-> null[0m[0m
      [31m-[0m[0m availability_zone                              = "us-east-1a" [90m-> null[0m[0m
      [31m-[0m[0m availability_zone_id                           = "use1-az6" [90m-> null[0m[0m
      [31m-[0m[0m cidr_block                                     = "*********/28" [90m-> null[0m[0m
      [31m-[0m[0m enable_dns64                                   = false [90m-> null[0m[0m
      [31m-[0m[0m enable_lni_at_device_index                     = 0 [90m-> null[0m[0m
      [31m-[0m[0m enable_resource_name_dns_a_record_on_launch    = false [90m-> null[0m[0m
      [31m-[0m[0m enable_resource_name_dns_aaaa_record_on_launch = false [90m-> null[0m[0m
      [31m-[0m[0m id                                             = "subnet-02163189325121054" [90m-> null[0m[0m
      [31m-[0m[0m ipv6_native                                    = false [90m-> null[0m[0m
      [31m-[0m[0m map_customer_owned_ip_on_launch                = false [90m-> null[0m[0m
      [31m-[0m[0m map_public_ip_on_launch                        = false [90m-> null[0m[0m
      [31m-[0m[0m owner_id                                       = "046276255144" [90m-> null[0m[0m
      [31m-[0m[0m private_dns_hostname_type_on_launch            = "ip-name" [90m-> null[0m[0m
      [31m-[0m[0m tags                                           = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-gen-egress-az1"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "Type"        = "Private"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m tags_all                                       = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-gen-egress-az1"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "Type"        = "Private"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m vpc_id                                         = "vpc-02bb90f92fd70e6f6" [90m-> null[0m[0m
    }

[1m  # module.gen_facing.module.gen_facing_vpc.aws_subnet.private["gen_ingress_az1"][0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_subnet" "private" {
      [31m-[0m[0m arn                                            = "arn:aws:ec2:us-east-1:046276255144:subnet/subnet-0d670456ac150e9b7" [90m-> null[0m[0m
      [31m-[0m[0m assign_ipv6_address_on_creation                = false [90m-> null[0m[0m
      [31m-[0m[0m availability_zone                              = "us-east-1a" [90m-> null[0m[0m
      [31m-[0m[0m availability_zone_id                           = "use1-az6" [90m-> null[0m[0m
      [31m-[0m[0m cidr_block                                     = "**********/28" [90m-> null[0m[0m
      [31m-[0m[0m enable_dns64                                   = false [90m-> null[0m[0m
      [31m-[0m[0m enable_lni_at_device_index                     = 0 [90m-> null[0m[0m
      [31m-[0m[0m enable_resource_name_dns_a_record_on_launch    = false [90m-> null[0m[0m
      [31m-[0m[0m enable_resource_name_dns_aaaa_record_on_launch = false [90m-> null[0m[0m
      [31m-[0m[0m id                                             = "subnet-0d670456ac150e9b7" [90m-> null[0m[0m
      [31m-[0m[0m ipv6_native                                    = false [90m-> null[0m[0m
      [31m-[0m[0m map_customer_owned_ip_on_launch                = false [90m-> null[0m[0m
      [31m-[0m[0m map_public_ip_on_launch                        = false [90m-> null[0m[0m
      [31m-[0m[0m owner_id                                       = "046276255144" [90m-> null[0m[0m
      [31m-[0m[0m private_dns_hostname_type_on_launch            = "ip-name" [90m-> null[0m[0m
      [31m-[0m[0m tags                                           = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-gen-ingress-az1"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "Type"        = "Private"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m tags_all                                       = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-gen-ingress-az1"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "Type"        = "Private"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m vpc_id                                         = "vpc-02bb90f92fd70e6f6" [90m-> null[0m[0m
    }

[1m  # module.gen_facing.module.gen_facing_vpc.aws_subnet.private["gen_migration_az1"][0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_subnet" "private" {
      [31m-[0m[0m arn                                            = "arn:aws:ec2:us-east-1:046276255144:subnet/subnet-06ef006f5416a0e77" [90m-> null[0m[0m
      [31m-[0m[0m assign_ipv6_address_on_creation                = false [90m-> null[0m[0m
      [31m-[0m[0m availability_zone                              = "us-east-1a" [90m-> null[0m[0m
      [31m-[0m[0m availability_zone_id                           = "use1-az6" [90m-> null[0m[0m
      [31m-[0m[0m cidr_block                                     = "**********/28" [90m-> null[0m[0m
      [31m-[0m[0m enable_dns64                                   = false [90m-> null[0m[0m
      [31m-[0m[0m enable_lni_at_device_index                     = 0 [90m-> null[0m[0m
      [31m-[0m[0m enable_resource_name_dns_a_record_on_launch    = false [90m-> null[0m[0m
      [31m-[0m[0m enable_resource_name_dns_aaaa_record_on_launch = false [90m-> null[0m[0m
      [31m-[0m[0m id                                             = "subnet-06ef006f5416a0e77" [90m-> null[0m[0m
      [31m-[0m[0m ipv6_native                                    = false [90m-> null[0m[0m
      [31m-[0m[0m map_customer_owned_ip_on_launch                = false [90m-> null[0m[0m
      [31m-[0m[0m map_public_ip_on_launch                        = false [90m-> null[0m[0m
      [31m-[0m[0m owner_id                                       = "046276255144" [90m-> null[0m[0m
      [31m-[0m[0m private_dns_hostname_type_on_launch            = "ip-name" [90m-> null[0m[0m
      [31m-[0m[0m tags                                           = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-gen-migration-az1"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "Type"        = "Private"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m tags_all                                       = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-gen-migration-az1"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "Type"        = "Private"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m vpc_id                                         = "vpc-02bb90f92fd70e6f6" [90m-> null[0m[0m
    }

[1m  # module.gen_facing.module.gen_facing_vpc.aws_subnet.private["smtp_subnet_dlz1"][0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_subnet" "private" {
      [31m-[0m[0m arn                                            = "arn:aws:ec2:us-east-1:046276255144:subnet/subnet-048d2c6ad6104bce1" [90m-> null[0m[0m
      [31m-[0m[0m assign_ipv6_address_on_creation                = false [90m-> null[0m[0m
      [31m-[0m[0m availability_zone                              = "us-east-1a" [90m-> null[0m[0m
      [31m-[0m[0m availability_zone_id                           = "use1-az6" [90m-> null[0m[0m
      [31m-[0m[0m cidr_block                                     = "**********/28" [90m-> null[0m[0m
      [31m-[0m[0m enable_dns64                                   = false [90m-> null[0m[0m
      [31m-[0m[0m enable_lni_at_device_index                     = 0 [90m-> null[0m[0m
      [31m-[0m[0m enable_resource_name_dns_a_record_on_launch    = false [90m-> null[0m[0m
      [31m-[0m[0m enable_resource_name_dns_aaaa_record_on_launch = false [90m-> null[0m[0m
      [31m-[0m[0m id                                             = "subnet-048d2c6ad6104bce1" [90m-> null[0m[0m
      [31m-[0m[0m ipv6_native                                    = false [90m-> null[0m[0m
      [31m-[0m[0m map_customer_owned_ip_on_launch                = false [90m-> null[0m[0m
      [31m-[0m[0m map_public_ip_on_launch                        = false [90m-> null[0m[0m
      [31m-[0m[0m owner_id                                       = "046276255144" [90m-> null[0m[0m
      [31m-[0m[0m private_dns_hostname_type_on_launch            = "ip-name" [90m-> null[0m[0m
      [31m-[0m[0m tags                                           = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-smtp-subnet-dlz1"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "Type"        = "Private"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m tags_all                                       = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-smtp-subnet-dlz1"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "Type"        = "Private"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m vpc_id                                         = "vpc-02bb90f92fd70e6f6" [90m-> null[0m[0m
    }

[1m  # module.gen_facing.module.gen_facing_vpc.aws_subnet.public["alb_subnet_az1"][0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_subnet" "public" {
      [31m-[0m[0m arn                                            = "arn:aws:ec2:us-east-1:046276255144:subnet/subnet-0b93f0452a90bb94b" [90m-> null[0m[0m
      [31m-[0m[0m assign_ipv6_address_on_creation                = false [90m-> null[0m[0m
      [31m-[0m[0m availability_zone                              = "us-east-1a" [90m-> null[0m[0m
      [31m-[0m[0m availability_zone_id                           = "use1-az6" [90m-> null[0m[0m
      [31m-[0m[0m cidr_block                                     = "**********/28" [90m-> null[0m[0m
      [31m-[0m[0m enable_dns64                                   = false [90m-> null[0m[0m
      [31m-[0m[0m enable_lni_at_device_index                     = 0 [90m-> null[0m[0m
      [31m-[0m[0m enable_resource_name_dns_a_record_on_launch    = false [90m-> null[0m[0m
      [31m-[0m[0m enable_resource_name_dns_aaaa_record_on_launch = false [90m-> null[0m[0m
      [31m-[0m[0m id                                             = "subnet-0b93f0452a90bb94b" [90m-> null[0m[0m
      [31m-[0m[0m ipv6_native                                    = false [90m-> null[0m[0m
      [31m-[0m[0m map_customer_owned_ip_on_launch                = false [90m-> null[0m[0m
      [31m-[0m[0m map_public_ip_on_launch                        = true [90m-> null[0m[0m
      [31m-[0m[0m owner_id                                       = "046276255144" [90m-> null[0m[0m
      [31m-[0m[0m private_dns_hostname_type_on_launch            = "ip-name" [90m-> null[0m[0m
      [31m-[0m[0m tags                                           = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-alb-subnet-az1"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "Type"        = "Public"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m tags_all                                       = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-alb-subnet-az1"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "Type"        = "Public"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m vpc_id                                         = "vpc-02bb90f92fd70e6f6" [90m-> null[0m[0m
    }

[1m  # module.gen_facing.module.gen_facing_vpc.aws_vpc.main[0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_vpc" "main" {
      [31m-[0m[0m arn                                  = "arn:aws:ec2:us-east-1:046276255144:vpc/vpc-02bb90f92fd70e6f6" [90m-> null[0m[0m
      [31m-[0m[0m assign_generated_ipv6_cidr_block     = false [90m-> null[0m[0m
      [31m-[0m[0m cidr_block                           = "*********/26" [90m-> null[0m[0m
      [31m-[0m[0m default_network_acl_id               = "acl-0e8660d8c23754176" [90m-> null[0m[0m
      [31m-[0m[0m default_route_table_id               = "rtb-0257a6c353d95fbce" [90m-> null[0m[0m
      [31m-[0m[0m default_security_group_id            = "sg-0864829b2accd136c" [90m-> null[0m[0m
      [31m-[0m[0m dhcp_options_id                      = "dopt-06b31360512cdef7e" [90m-> null[0m[0m
      [31m-[0m[0m enable_dns_hostnames                 = true [90m-> null[0m[0m
      [31m-[0m[0m enable_dns_support                   = true [90m-> null[0m[0m
      [31m-[0m[0m enable_network_address_usage_metrics = false [90m-> null[0m[0m
      [31m-[0m[0m id                                   = "vpc-02bb90f92fd70e6f6" [90m-> null[0m[0m
      [31m-[0m[0m instance_tenancy                     = "default" [90m-> null[0m[0m
      [31m-[0m[0m ipv6_netmask_length                  = 0 [90m-> null[0m[0m
      [31m-[0m[0m main_route_table_id                  = "rtb-0257a6c353d95fbce" [90m-> null[0m[0m
      [31m-[0m[0m owner_id                             = "046276255144" [90m-> null[0m[0m
      [31m-[0m[0m tags                                 = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-gen-facing-compartment"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m tags_all                             = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-gen-facing-compartment"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
    }

[1m  # module.gen_facing.module.gen_facing_vpc.aws_vpc_ipv4_cidr_block_association.secondary["**********/22"][0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_vpc_ipv4_cidr_block_association" "secondary" {
      [31m-[0m[0m cidr_block = "**********/22" [90m-> null[0m[0m
      [31m-[0m[0m id         = "vpc-cidr-assoc-0f35f196d7c6c44b6" [90m-> null[0m[0m
      [31m-[0m[0m vpc_id     = "vpc-02bb90f92fd70e6f6" [90m-> null[0m[0m
    }

[1m  # module.internet_facing.module.Internet-facing-compartment.aws_internet_gateway.main[0][0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_internet_gateway" "main" {
      [31m-[0m[0m arn      = "arn:aws:ec2:us-east-1:046276255144:internet-gateway/igw-0a54195907b5b5079" [90m-> null[0m[0m
      [31m-[0m[0m id       = "igw-0a54195907b5b5079" [90m-> null[0m[0m
      [31m-[0m[0m owner_id = "046276255144" [90m-> null[0m[0m
      [31m-[0m[0m tags     = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Module"      = "networking"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-igw"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "Type"        = "InternetGateway"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m tags_all = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Module"      = "networking"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-igw"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "Type"        = "InternetGateway"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m vpc_id   = "vpc-08695098ddd82b831" [90m-> null[0m[0m
    }

[1m  # module.internet_facing.module.Internet-facing-compartment.aws_route.public_internet[0][0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_route" "public_internet" {
      [31m-[0m[0m destination_cidr_block = "0.0.0.0/0" [90m-> null[0m[0m
      [31m-[0m[0m gateway_id             = "igw-0a54195907b5b5079" [90m-> null[0m[0m
      [31m-[0m[0m id                     = "r-rtb-0c1eadd6394a31b841080289494" [90m-> null[0m[0m
      [31m-[0m[0m origin                 = "CreateRoute" [90m-> null[0m[0m
      [31m-[0m[0m route_table_id         = "rtb-0c1eadd6394a31b84" [90m-> null[0m[0m
      [31m-[0m[0m state                  = "active" [90m-> null[0m[0m
    }

[1m  # module.internet_facing.module.Internet-facing-compartment.aws_route_table.public[0][0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_route_table" "public" {
      [31m-[0m[0m arn              = "arn:aws:ec2:us-east-1:046276255144:route-table/rtb-0c1eadd6394a31b84" [90m-> null[0m[0m
      [31m-[0m[0m id               = "rtb-0c1eadd6394a31b84" [90m-> null[0m[0m
      [31m-[0m[0m owner_id         = "046276255144" [90m-> null[0m[0m
      [31m-[0m[0m propagating_vgws = [] [90m-> null[0m[0m
      [31m-[0m[0m route            = [
          [31m-[0m[0m {
              [31m-[0m[0m carrier_gateway_id         = ""
              [31m-[0m[0m cidr_block                 = "0.0.0.0/0"
              [31m-[0m[0m core_network_arn           = ""
              [31m-[0m[0m destination_prefix_list_id = ""
              [31m-[0m[0m egress_only_gateway_id     = ""
              [31m-[0m[0m gateway_id                 = "igw-0a54195907b5b5079"
              [31m-[0m[0m ipv6_cidr_block            = ""
              [31m-[0m[0m local_gateway_id           = ""
              [31m-[0m[0m nat_gateway_id             = ""
              [31m-[0m[0m network_interface_id       = ""
              [31m-[0m[0m transit_gateway_id         = ""
              [31m-[0m[0m vpc_endpoint_id            = ""
              [31m-[0m[0m vpc_peering_connection_id  = ""
            },
        ] [90m-> null[0m[0m
      [31m-[0m[0m tags             = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Module"      = "networking"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-public-rt"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "Type"        = "Public"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m tags_all         = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Module"      = "networking"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-public-rt"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "Type"        = "Public"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m vpc_id           = "vpc-08695098ddd82b831" [90m-> null[0m[0m
    }

[1m  # module.internet_facing.module.Internet-facing-compartment.aws_route_table_association.public["public_az1"][0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_route_table_association" "public" {
      [31m-[0m[0m id             = "rtbassoc-08740208b5b96da46" [90m-> null[0m[0m
      [31m-[0m[0m route_table_id = "rtb-0c1eadd6394a31b84" [90m-> null[0m[0m
      [31m-[0m[0m subnet_id      = "subnet-0ab44f0ae503eec07" [90m-> null[0m[0m
    }

[1m  # module.internet_facing.module.Internet-facing-compartment.aws_route_table_association.public["public_az2"][0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_route_table_association" "public" {
      [31m-[0m[0m id             = "rtbassoc-0c2ee32a10c0818e3" [90m-> null[0m[0m
      [31m-[0m[0m route_table_id = "rtb-0c1eadd6394a31b84" [90m-> null[0m[0m
      [31m-[0m[0m subnet_id      = "subnet-027ac562019ecdca7" [90m-> null[0m[0m
    }

[1m  # module.internet_facing.module.Internet-facing-compartment.aws_subnet.public["public_az1"][0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_subnet" "public" {
      [31m-[0m[0m arn                                            = "arn:aws:ec2:us-east-1:046276255144:subnet/subnet-0ab44f0ae503eec07" [90m-> null[0m[0m
      [31m-[0m[0m assign_ipv6_address_on_creation                = false [90m-> null[0m[0m
      [31m-[0m[0m availability_zone                              = "us-east-1a" [90m-> null[0m[0m
      [31m-[0m[0m availability_zone_id                           = "use1-az6" [90m-> null[0m[0m
      [31m-[0m[0m cidr_block                                     = "**********/28" [90m-> null[0m[0m
      [31m-[0m[0m enable_dns64                                   = false [90m-> null[0m[0m
      [31m-[0m[0m enable_lni_at_device_index                     = 0 [90m-> null[0m[0m
      [31m-[0m[0m enable_resource_name_dns_a_record_on_launch    = false [90m-> null[0m[0m
      [31m-[0m[0m enable_resource_name_dns_aaaa_record_on_launch = false [90m-> null[0m[0m
      [31m-[0m[0m id                                             = "subnet-0ab44f0ae503eec07" [90m-> null[0m[0m
      [31m-[0m[0m ipv6_native                                    = false [90m-> null[0m[0m
      [31m-[0m[0m map_customer_owned_ip_on_launch                = false [90m-> null[0m[0m
      [31m-[0m[0m map_public_ip_on_launch                        = true [90m-> null[0m[0m
      [31m-[0m[0m owner_id                                       = "046276255144" [90m-> null[0m[0m
      [31m-[0m[0m private_dns_hostname_type_on_launch            = "ip-name" [90m-> null[0m[0m
      [31m-[0m[0m tags                                           = {
          [31m-[0m[0m "AZ"          = "us-east-1a"
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Module"      = "networking"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-public-subnet-az1"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "Tier"        = "Public"
          [31m-[0m[0m "Type"        = "Public"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m tags_all                                       = {
          [31m-[0m[0m "AZ"          = "us-east-1a"
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Module"      = "networking"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-public-subnet-az1"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "Tier"        = "Public"
          [31m-[0m[0m "Type"        = "Public"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m vpc_id                                         = "vpc-08695098ddd82b831" [90m-> null[0m[0m
    }

[1m  # module.internet_facing.module.Internet-facing-compartment.aws_subnet.public["public_az2"][0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_subnet" "public" {
      [31m-[0m[0m arn                                            = "arn:aws:ec2:us-east-1:046276255144:subnet/subnet-027ac562019ecdca7" [90m-> null[0m[0m
      [31m-[0m[0m assign_ipv6_address_on_creation                = false [90m-> null[0m[0m
      [31m-[0m[0m availability_zone                              = "us-east-1b" [90m-> null[0m[0m
      [31m-[0m[0m availability_zone_id                           = "use1-az1" [90m-> null[0m[0m
      [31m-[0m[0m cidr_block                                     = "***********/28" [90m-> null[0m[0m
      [31m-[0m[0m enable_dns64                                   = false [90m-> null[0m[0m
      [31m-[0m[0m enable_lni_at_device_index                     = 0 [90m-> null[0m[0m
      [31m-[0m[0m enable_resource_name_dns_a_record_on_launch    = false [90m-> null[0m[0m
      [31m-[0m[0m enable_resource_name_dns_aaaa_record_on_launch = false [90m-> null[0m[0m
      [31m-[0m[0m id                                             = "subnet-027ac562019ecdca7" [90m-> null[0m[0m
      [31m-[0m[0m ipv6_native                                    = false [90m-> null[0m[0m
      [31m-[0m[0m map_customer_owned_ip_on_launch                = false [90m-> null[0m[0m
      [31m-[0m[0m map_public_ip_on_launch                        = true [90m-> null[0m[0m
      [31m-[0m[0m owner_id                                       = "046276255144" [90m-> null[0m[0m
      [31m-[0m[0m private_dns_hostname_type_on_launch            = "ip-name" [90m-> null[0m[0m
      [31m-[0m[0m tags                                           = {
          [31m-[0m[0m "AZ"          = "us-east-1b"
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Module"      = "networking"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-public-subnet-az2"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "Tier"        = "Public"
          [31m-[0m[0m "Type"        = "Public"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m tags_all                                       = {
          [31m-[0m[0m "AZ"          = "us-east-1b"
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Module"      = "networking"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-public-subnet-az2"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "Tier"        = "Public"
          [31m-[0m[0m "Type"        = "Public"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m vpc_id                                         = "vpc-08695098ddd82b831" [90m-> null[0m[0m
    }

[1m  # module.internet_facing.module.Internet-facing-compartment.aws_vpc.main[0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_vpc" "main" {
      [31m-[0m[0m arn                                  = "arn:aws:ec2:us-east-1:046276255144:vpc/vpc-08695098ddd82b831" [90m-> null[0m[0m
      [31m-[0m[0m assign_generated_ipv6_cidr_block     = false [90m-> null[0m[0m
      [31m-[0m[0m cidr_block                           = "**********/27" [90m-> null[0m[0m
      [31m-[0m[0m default_network_acl_id               = "acl-0772e47a019b208fa" [90m-> null[0m[0m
      [31m-[0m[0m default_route_table_id               = "rtb-0d175ecfc9bb9ac45" [90m-> null[0m[0m
      [31m-[0m[0m default_security_group_id            = "sg-008f95a19a7ca5784" [90m-> null[0m[0m
      [31m-[0m[0m dhcp_options_id                      = "dopt-06b31360512cdef7e" [90m-> null[0m[0m
      [31m-[0m[0m enable_dns_hostnames                 = true [90m-> null[0m[0m
      [31m-[0m[0m enable_dns_support                   = true [90m-> null[0m[0m
      [31m-[0m[0m enable_network_address_usage_metrics = false [90m-> null[0m[0m
      [31m-[0m[0m id                                   = "vpc-08695098ddd82b831" [90m-> null[0m[0m
      [31m-[0m[0m instance_tenancy                     = "default" [90m-> null[0m[0m
      [31m-[0m[0m ipv6_netmask_length                  = 0 [90m-> null[0m[0m
      [31m-[0m[0m main_route_table_id                  = "rtb-0d175ecfc9bb9ac45" [90m-> null[0m[0m
      [31m-[0m[0m owner_id                             = "046276255144" [90m-> null[0m[0m
      [31m-[0m[0m tags                                 = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Module"      = "networking"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-internet-facing"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "Type"        = "InternetFacing"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m tags_all                             = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Module"      = "networking"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-internet-facing"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "Type"        = "InternetFacing"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
    }

[1m  # module.internet_facing_security_groups.module.internet_facing_security_groups.aws_security_group.this["nfw_egress"][0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_security_group" "this" {
      [31m-[0m[0m arn                    = "arn:aws:ec2:us-east-1:046276255144:security-group/sg-00054adbcabec6df1" [90m-> null[0m[0m
      [31m-[0m[0m description            = "InfoVault Internet Facing Network Firewall Egress Security Group" [90m-> null[0m[0m
      [31m-[0m[0m egress                 = [
          [31m-[0m[0m {
              [31m-[0m[0m cidr_blocks      = [
                  [31m-[0m[0m "0.0.0.0/0",
                ]
              [31m-[0m[0m description      = "All outbound traffic"
              [31m-[0m[0m from_port        = 0
              [31m-[0m[0m ipv6_cidr_blocks = []
              [31m-[0m[0m prefix_list_ids  = []
              [31m-[0m[0m protocol         = "-1"
              [31m-[0m[0m security_groups  = []
              [31m-[0m[0m self             = false
              [31m-[0m[0m to_port          = 0
            },
        ] [90m-> null[0m[0m
      [31m-[0m[0m id                     = "sg-00054adbcabec6df1" [90m-> null[0m[0m
      [31m-[0m[0m ingress                = [
          [31m-[0m[0m {
              [31m-[0m[0m cidr_blocks      = [
                  [31m-[0m[0m "10.0.0.0/8",
                ]
              [31m-[0m[0m description      = "All TCP traffic from 10.0.0.0/8 network"
              [31m-[0m[0m from_port        = 0
              [31m-[0m[0m ipv6_cidr_blocks = []
              [31m-[0m[0m prefix_list_ids  = []
              [31m-[0m[0m protocol         = "tcp"
              [31m-[0m[0m security_groups  = []
              [31m-[0m[0m self             = false
              [31m-[0m[0m to_port          = 65535
            },
          [31m-[0m[0m {
              [31m-[0m[0m cidr_blocks      = [
                  [31m-[0m[0m "**********/10",
                ]
              [31m-[0m[0m description      = "All TCP traffic from **********/10 network"
              [31m-[0m[0m from_port        = 0
              [31m-[0m[0m ipv6_cidr_blocks = []
              [31m-[0m[0m prefix_list_ids  = []
              [31m-[0m[0m protocol         = "tcp"
              [31m-[0m[0m security_groups  = []
              [31m-[0m[0m self             = false
              [31m-[0m[0m to_port          = 65535
            },
        ] [90m-> null[0m[0m
      [31m-[0m[0m name                   = "infovault-nfw-eg-01" [90m-> null[0m[0m
      [31m-[0m[0m owner_id               = "046276255144" [90m-> null[0m[0m
      [31m-[0m[0m revoke_rules_on_delete = false [90m-> null[0m[0m
      [31m-[0m[0m tags                   = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Module"      = "security_groups"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-nfw-eg-01"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "Type"        = "SecurityGroup"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m tags_all               = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Module"      = "security_groups"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-nfw-eg-01"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "Type"        = "SecurityGroup"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m vpc_id                 = "vpc-08695098ddd82b831" [90m-> null[0m[0m
    }

[1m  # module.internet_facing_security_groups.module.internet_facing_security_groups.aws_security_group.this["proxy_egress"][0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_security_group" "this" {
      [31m-[0m[0m arn                    = "arn:aws:ec2:us-east-1:046276255144:security-group/sg-0ad42ef8fbb37202c" [90m-> null[0m[0m
      [31m-[0m[0m description            = "InfoVault Internet Facing Proxy Egress Security Group" [90m-> null[0m[0m
      [31m-[0m[0m egress                 = [
          [31m-[0m[0m {
              [31m-[0m[0m cidr_blocks      = [
                  [31m-[0m[0m "0.0.0.0/0",
                ]
              [31m-[0m[0m description      = "All outbound traffic"
              [31m-[0m[0m from_port        = 0
              [31m-[0m[0m ipv6_cidr_blocks = []
              [31m-[0m[0m prefix_list_ids  = []
              [31m-[0m[0m protocol         = "-1"
              [31m-[0m[0m security_groups  = []
              [31m-[0m[0m self             = false
              [31m-[0m[0m to_port          = 0
            },
        ] [90m-> null[0m[0m
      [31m-[0m[0m id                     = "sg-0ad42ef8fbb37202c" [90m-> null[0m[0m
      [31m-[0m[0m ingress                = [
          [31m-[0m[0m {
              [31m-[0m[0m cidr_blocks      = [
                  [31m-[0m[0m "0.0.0.0/0",
                ]
              [31m-[0m[0m description      = "HTTP inbound traffic"
              [31m-[0m[0m from_port        = 80
              [31m-[0m[0m ipv6_cidr_blocks = []
              [31m-[0m[0m prefix_list_ids  = []
              [31m-[0m[0m protocol         = "tcp"
              [31m-[0m[0m security_groups  = []
              [31m-[0m[0m self             = false
              [31m-[0m[0m to_port          = 80
            },
          [31m-[0m[0m {
              [31m-[0m[0m cidr_blocks      = [
                  [31m-[0m[0m "0.0.0.0/0",
                ]
              [31m-[0m[0m description      = "HTTPS inbound traffic"
              [31m-[0m[0m from_port        = 443
              [31m-[0m[0m ipv6_cidr_blocks = []
              [31m-[0m[0m prefix_list_ids  = []
              [31m-[0m[0m protocol         = "tcp"
              [31m-[0m[0m security_groups  = []
              [31m-[0m[0m self             = false
              [31m-[0m[0m to_port          = 443
            },
        ] [90m-> null[0m[0m
      [31m-[0m[0m name                   = "infovault-pxy-eg-01" [90m-> null[0m[0m
      [31m-[0m[0m owner_id               = "046276255144" [90m-> null[0m[0m
      [31m-[0m[0m revoke_rules_on_delete = false [90m-> null[0m[0m
      [31m-[0m[0m tags                   = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Module"      = "security_groups"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-pxy-eg-01"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "Type"        = "SecurityGroup"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m tags_all               = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Module"      = "security_groups"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-pxy-eg-01"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "Type"        = "SecurityGroup"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m vpc_id                 = "vpc-08695098ddd82b831" [90m-> null[0m[0m
    }

[1m  # module.internet_facing_security_groups.module.internet_facing_security_groups.aws_vpc_security_group_egress_rule.this["nfw_egress_all"][0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_vpc_security_group_egress_rule" "this" {
      [31m-[0m[0m arn                    = "arn:aws:ec2:us-east-1:046276255144:security-group-rule/sgr-0358f116dbc669418" [90m-> null[0m[0m
      [31m-[0m[0m cidr_ipv4              = "0.0.0.0/0" [90m-> null[0m[0m
      [31m-[0m[0m description            = "All outbound traffic" [90m-> null[0m[0m
      [31m-[0m[0m from_port              = -1 [90m-> null[0m[0m
      [31m-[0m[0m id                     = "sgr-0358f116dbc669418" [90m-> null[0m[0m
      [31m-[0m[0m ip_protocol            = "-1" [90m-> null[0m[0m
      [31m-[0m[0m security_group_id      = "sg-00054adbcabec6df1" [90m-> null[0m[0m
      [31m-[0m[0m security_group_rule_id = "sgr-0358f116dbc669418" [90m-> null[0m[0m
      [31m-[0m[0m tags                   = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Module"      = "security_groups"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "nfw_egress_all-egress"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "Type"        = "EgressRule"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m tags_all               = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Module"      = "security_groups"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "nfw_egress_all-egress"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "Type"        = "EgressRule"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m to_port                = -1 [90m-> null[0m[0m
    }

[1m  # module.internet_facing_security_groups.module.internet_facing_security_groups.aws_vpc_security_group_egress_rule.this["proxy_egress_all"][0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_vpc_security_group_egress_rule" "this" {
      [31m-[0m[0m arn                    = "arn:aws:ec2:us-east-1:046276255144:security-group-rule/sgr-0e7c5ce05837952bc" [90m-> null[0m[0m
      [31m-[0m[0m cidr_ipv4              = "0.0.0.0/0" [90m-> null[0m[0m
      [31m-[0m[0m description            = "All outbound traffic" [90m-> null[0m[0m
      [31m-[0m[0m from_port              = -1 [90m-> null[0m[0m
      [31m-[0m[0m id                     = "sgr-0e7c5ce05837952bc" [90m-> null[0m[0m
      [31m-[0m[0m ip_protocol            = "-1" [90m-> null[0m[0m
      [31m-[0m[0m security_group_id      = "sg-0ad42ef8fbb37202c" [90m-> null[0m[0m
      [31m-[0m[0m security_group_rule_id = "sgr-0e7c5ce05837952bc" [90m-> null[0m[0m
      [31m-[0m[0m tags                   = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Module"      = "security_groups"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "proxy_egress_all-egress"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "Type"        = "EgressRule"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m tags_all               = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Module"      = "security_groups"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "proxy_egress_all-egress"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "Type"        = "EgressRule"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m to_port                = -1 [90m-> null[0m[0m
    }

[1m  # module.internet_facing_security_groups.module.internet_facing_security_groups.aws_vpc_security_group_ingress_rule.this["nfw_egress_internal_10"][0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_vpc_security_group_ingress_rule" "this" {
      [31m-[0m[0m arn                    = "arn:aws:ec2:us-east-1:046276255144:security-group-rule/sgr-0abd9aa6ff5912ff6" [90m-> null[0m[0m
      [31m-[0m[0m cidr_ipv4              = "10.0.0.0/8" [90m-> null[0m[0m
      [31m-[0m[0m description            = "All TCP traffic from 10.0.0.0/8 network" [90m-> null[0m[0m
      [31m-[0m[0m from_port              = 0 [90m-> null[0m[0m
      [31m-[0m[0m id                     = "sgr-0abd9aa6ff5912ff6" [90m-> null[0m[0m
      [31m-[0m[0m ip_protocol            = "tcp" [90m-> null[0m[0m
      [31m-[0m[0m security_group_id      = "sg-00054adbcabec6df1" [90m-> null[0m[0m
      [31m-[0m[0m security_group_rule_id = "sgr-0abd9aa6ff5912ff6" [90m-> null[0m[0m
      [31m-[0m[0m tags                   = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Module"      = "security_groups"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "nfw_egress_internal_10-ingress"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "Type"        = "IngressRule"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m tags_all               = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Module"      = "security_groups"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "nfw_egress_internal_10-ingress"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "Type"        = "IngressRule"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m to_port                = 65535 [90m-> null[0m[0m
    }

[1m  # module.internet_facing_security_groups.module.internet_facing_security_groups.aws_vpc_security_group_ingress_rule.this["nfw_egress_internal_100"][0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_vpc_security_group_ingress_rule" "this" {
      [31m-[0m[0m arn                    = "arn:aws:ec2:us-east-1:046276255144:security-group-rule/sgr-0308580bc23def426" [90m-> null[0m[0m
      [31m-[0m[0m cidr_ipv4              = "**********/10" [90m-> null[0m[0m
      [31m-[0m[0m description            = "All TCP traffic from **********/10 network" [90m-> null[0m[0m
      [31m-[0m[0m from_port              = 0 [90m-> null[0m[0m
      [31m-[0m[0m id                     = "sgr-0308580bc23def426" [90m-> null[0m[0m
      [31m-[0m[0m ip_protocol            = "tcp" [90m-> null[0m[0m
      [31m-[0m[0m security_group_id      = "sg-00054adbcabec6df1" [90m-> null[0m[0m
      [31m-[0m[0m security_group_rule_id = "sgr-0308580bc23def426" [90m-> null[0m[0m
      [31m-[0m[0m tags                   = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Module"      = "security_groups"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "nfw_egress_internal_100-ingress"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "Type"        = "IngressRule"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m tags_all               = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Module"      = "security_groups"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "nfw_egress_internal_100-ingress"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "Type"        = "IngressRule"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m to_port                = 65535 [90m-> null[0m[0m
    }

[1m  # module.internet_facing_security_groups.module.internet_facing_security_groups.aws_vpc_security_group_ingress_rule.this["proxy_egress_http"][0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_vpc_security_group_ingress_rule" "this" {
      [31m-[0m[0m arn                    = "arn:aws:ec2:us-east-1:046276255144:security-group-rule/sgr-0af7a07b8ed88020f" [90m-> null[0m[0m
      [31m-[0m[0m cidr_ipv4              = "0.0.0.0/0" [90m-> null[0m[0m
      [31m-[0m[0m description            = "HTTP inbound traffic" [90m-> null[0m[0m
      [31m-[0m[0m from_port              = 80 [90m-> null[0m[0m
      [31m-[0m[0m id                     = "sgr-0af7a07b8ed88020f" [90m-> null[0m[0m
      [31m-[0m[0m ip_protocol            = "tcp" [90m-> null[0m[0m
      [31m-[0m[0m security_group_id      = "sg-0ad42ef8fbb37202c" [90m-> null[0m[0m
      [31m-[0m[0m security_group_rule_id = "sgr-0af7a07b8ed88020f" [90m-> null[0m[0m
      [31m-[0m[0m tags                   = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Module"      = "security_groups"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "proxy_egress_http-ingress"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "Type"        = "IngressRule"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m tags_all               = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Module"      = "security_groups"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "proxy_egress_http-ingress"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "Type"        = "IngressRule"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m to_port                = 80 [90m-> null[0m[0m
    }

[1m  # module.internet_facing_security_groups.module.internet_facing_security_groups.aws_vpc_security_group_ingress_rule.this["proxy_egress_https"][0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_vpc_security_group_ingress_rule" "this" {
      [31m-[0m[0m arn                    = "arn:aws:ec2:us-east-1:046276255144:security-group-rule/sgr-0777bd895eca786bc" [90m-> null[0m[0m
      [31m-[0m[0m cidr_ipv4              = "0.0.0.0/0" [90m-> null[0m[0m
      [31m-[0m[0m description            = "HTTPS inbound traffic" [90m-> null[0m[0m
      [31m-[0m[0m from_port              = 443 [90m-> null[0m[0m
      [31m-[0m[0m id                     = "sgr-0777bd895eca786bc" [90m-> null[0m[0m
      [31m-[0m[0m ip_protocol            = "tcp" [90m-> null[0m[0m
      [31m-[0m[0m security_group_id      = "sg-0ad42ef8fbb37202c" [90m-> null[0m[0m
      [31m-[0m[0m security_group_rule_id = "sgr-0777bd895eca786bc" [90m-> null[0m[0m
      [31m-[0m[0m tags                   = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Module"      = "security_groups"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "proxy_egress_https-ingress"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "Type"        = "IngressRule"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m tags_all               = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Module"      = "security_groups"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "proxy_egress_https-ingress"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "Type"        = "IngressRule"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m to_port                = 443 [90m-> null[0m[0m
    }

[1m  # module.intranet_management.module.intranet_management_vpc.aws_route_table.private["mgmt_main_rt"][0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_route_table" "private" {
      [31m-[0m[0m arn              = "arn:aws:ec2:us-east-1:046276255144:route-table/rtb-0ec4caf429261cbfc" [90m-> null[0m[0m
      [31m-[0m[0m id               = "rtb-0ec4caf429261cbfc" [90m-> null[0m[0m
      [31m-[0m[0m owner_id         = "046276255144" [90m-> null[0m[0m
      [31m-[0m[0m propagating_vgws = [] [90m-> null[0m[0m
      [31m-[0m[0m route            = [] [90m-> null[0m[0m
      [31m-[0m[0m tags             = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-mgmt-route-table"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "Type"        = "Private"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m tags_all         = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-mgmt-route-table"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "Type"        = "Private"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m vpc_id           = "vpc-05f38ae43b723b6e9" [90m-> null[0m[0m
    }

[1m  # module.intranet_management.module.intranet_management_vpc.aws_route_table_association.private["natad_assoc"][0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_route_table_association" "private" {
      [31m-[0m[0m id             = "rtbassoc-01963043ae3694069" [90m-> null[0m[0m
      [31m-[0m[0m route_table_id = "rtb-0ec4caf429261cbfc" [90m-> null[0m[0m
      [31m-[0m[0m subnet_id      = "subnet-0fc9b7dea2dfb2f5f" [90m-> null[0m[0m
    }

[1m  # module.intranet_management.module.intranet_management_vpc.aws_route_table_association.private["natmgt_assoc"][0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_route_table_association" "private" {
      [31m-[0m[0m id             = "rtbassoc-0a5cc3e2cf9d830c6" [90m-> null[0m[0m
      [31m-[0m[0m route_table_id = "rtb-0ec4caf429261cbfc" [90m-> null[0m[0m
      [31m-[0m[0m subnet_id      = "subnet-06742da032d3c33ed" [90m-> null[0m[0m
    }

[1m  # module.intranet_management.module.intranet_management_vpc.aws_route_table_association.private["natvpce_az1_assoc"][0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_route_table_association" "private" {
      [31m-[0m[0m id             = "rtbassoc-0bd686d34af03f5dd" [90m-> null[0m[0m
      [31m-[0m[0m route_table_id = "rtb-0ec4caf429261cbfc" [90m-> null[0m[0m
      [31m-[0m[0m subnet_id      = "subnet-09a1df115fcbf7e71" [90m-> null[0m[0m
    }

[1m  # module.intranet_management.module.intranet_management_vpc.aws_route_table_association.private["natvpce_az2_assoc"][0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_route_table_association" "private" {
      [31m-[0m[0m id             = "rtbassoc-0bd3c05c9e34dd9ca" [90m-> null[0m[0m
      [31m-[0m[0m route_table_id = "rtb-0ec4caf429261cbfc" [90m-> null[0m[0m
      [31m-[0m[0m subnet_id      = "subnet-008509e3c7a226a65" [90m-> null[0m[0m
    }

[1m  # module.intranet_management.module.intranet_management_vpc.aws_subnet.private["natad"][0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_subnet" "private" {
      [31m-[0m[0m arn                                            = "arn:aws:ec2:us-east-1:046276255144:subnet/subnet-0fc9b7dea2dfb2f5f" [90m-> null[0m[0m
      [31m-[0m[0m assign_ipv6_address_on_creation                = false [90m-> null[0m[0m
      [31m-[0m[0m availability_zone                              = "us-east-1a" [90m-> null[0m[0m
      [31m-[0m[0m availability_zone_id                           = "use1-az6" [90m-> null[0m[0m
      [31m-[0m[0m cidr_block                                     = "***********/28" [90m-> null[0m[0m
      [31m-[0m[0m enable_dns64                                   = false [90m-> null[0m[0m
      [31m-[0m[0m enable_lni_at_device_index                     = 0 [90m-> null[0m[0m
      [31m-[0m[0m enable_resource_name_dns_a_record_on_launch    = false [90m-> null[0m[0m
      [31m-[0m[0m enable_resource_name_dns_aaaa_record_on_launch = false [90m-> null[0m[0m
      [31m-[0m[0m id                                             = "subnet-0fc9b7dea2dfb2f5f" [90m-> null[0m[0m
      [31m-[0m[0m ipv6_native                                    = false [90m-> null[0m[0m
      [31m-[0m[0m map_customer_owned_ip_on_launch                = false [90m-> null[0m[0m
      [31m-[0m[0m map_public_ip_on_launch                        = false [90m-> null[0m[0m
      [31m-[0m[0m owner_id                                       = "046276255144" [90m-> null[0m[0m
      [31m-[0m[0m private_dns_hostname_type_on_launch            = "ip-name" [90m-> null[0m[0m
      [31m-[0m[0m tags                                           = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-natad-subnet"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "Type"        = "Private"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m tags_all                                       = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-natad-subnet"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "Type"        = "Private"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m vpc_id                                         = "vpc-05f38ae43b723b6e9" [90m-> null[0m[0m
    }

[1m  # module.intranet_management.module.intranet_management_vpc.aws_subnet.private["natmgt"][0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_subnet" "private" {
      [31m-[0m[0m arn                                            = "arn:aws:ec2:us-east-1:046276255144:subnet/subnet-06742da032d3c33ed" [90m-> null[0m[0m
      [31m-[0m[0m assign_ipv6_address_on_creation                = false [90m-> null[0m[0m
      [31m-[0m[0m availability_zone                              = "us-east-1a" [90m-> null[0m[0m
      [31m-[0m[0m availability_zone_id                           = "use1-az6" [90m-> null[0m[0m
      [31m-[0m[0m cidr_block                                     = "**********/28" [90m-> null[0m[0m
      [31m-[0m[0m enable_dns64                                   = false [90m-> null[0m[0m
      [31m-[0m[0m enable_lni_at_device_index                     = 0 [90m-> null[0m[0m
      [31m-[0m[0m enable_resource_name_dns_a_record_on_launch    = false [90m-> null[0m[0m
      [31m-[0m[0m enable_resource_name_dns_aaaa_record_on_launch = false [90m-> null[0m[0m
      [31m-[0m[0m id                                             = "subnet-06742da032d3c33ed" [90m-> null[0m[0m
      [31m-[0m[0m ipv6_native                                    = false [90m-> null[0m[0m
      [31m-[0m[0m map_customer_owned_ip_on_launch                = false [90m-> null[0m[0m
      [31m-[0m[0m map_public_ip_on_launch                        = false [90m-> null[0m[0m
      [31m-[0m[0m owner_id                                       = "046276255144" [90m-> null[0m[0m
      [31m-[0m[0m private_dns_hostname_type_on_launch            = "ip-name" [90m-> null[0m[0m
      [31m-[0m[0m tags                                           = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-natmgt-subnet"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "Type"        = "Private"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m tags_all                                       = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-natmgt-subnet"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "Type"        = "Private"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m vpc_id                                         = "vpc-05f38ae43b723b6e9" [90m-> null[0m[0m
    }

[1m  # module.intranet_management.module.intranet_management_vpc.aws_subnet.private["natvpce_az1"][0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_subnet" "private" {
      [31m-[0m[0m arn                                            = "arn:aws:ec2:us-east-1:046276255144:subnet/subnet-09a1df115fcbf7e71" [90m-> null[0m[0m
      [31m-[0m[0m assign_ipv6_address_on_creation                = false [90m-> null[0m[0m
      [31m-[0m[0m availability_zone                              = "us-east-1a" [90m-> null[0m[0m
      [31m-[0m[0m availability_zone_id                           = "use1-az6" [90m-> null[0m[0m
      [31m-[0m[0m cidr_block                                     = "***********/28" [90m-> null[0m[0m
      [31m-[0m[0m enable_dns64                                   = false [90m-> null[0m[0m
      [31m-[0m[0m enable_lni_at_device_index                     = 0 [90m-> null[0m[0m
      [31m-[0m[0m enable_resource_name_dns_a_record_on_launch    = false [90m-> null[0m[0m
      [31m-[0m[0m enable_resource_name_dns_aaaa_record_on_launch = false [90m-> null[0m[0m
      [31m-[0m[0m id                                             = "subnet-09a1df115fcbf7e71" [90m-> null[0m[0m
      [31m-[0m[0m ipv6_native                                    = false [90m-> null[0m[0m
      [31m-[0m[0m map_customer_owned_ip_on_launch                = false [90m-> null[0m[0m
      [31m-[0m[0m map_public_ip_on_launch                        = false [90m-> null[0m[0m
      [31m-[0m[0m owner_id                                       = "046276255144" [90m-> null[0m[0m
      [31m-[0m[0m private_dns_hostname_type_on_launch            = "ip-name" [90m-> null[0m[0m
      [31m-[0m[0m tags                                           = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-natvpce-subnet-az1"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "Type"        = "Private"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m tags_all                                       = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-natvpce-subnet-az1"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "Type"        = "Private"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m vpc_id                                         = "vpc-05f38ae43b723b6e9" [90m-> null[0m[0m
    }

[1m  # module.intranet_management.module.intranet_management_vpc.aws_subnet.private["natvpce_az2"][0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_subnet" "private" {
      [31m-[0m[0m arn                                            = "arn:aws:ec2:us-east-1:046276255144:subnet/subnet-008509e3c7a226a65" [90m-> null[0m[0m
      [31m-[0m[0m assign_ipv6_address_on_creation                = false [90m-> null[0m[0m
      [31m-[0m[0m availability_zone                              = "us-east-1b" [90m-> null[0m[0m
      [31m-[0m[0m availability_zone_id                           = "use1-az1" [90m-> null[0m[0m
      [31m-[0m[0m cidr_block                                     = "***********/28" [90m-> null[0m[0m
      [31m-[0m[0m enable_dns64                                   = false [90m-> null[0m[0m
      [31m-[0m[0m enable_lni_at_device_index                     = 0 [90m-> null[0m[0m
      [31m-[0m[0m enable_resource_name_dns_a_record_on_launch    = false [90m-> null[0m[0m
      [31m-[0m[0m enable_resource_name_dns_aaaa_record_on_launch = false [90m-> null[0m[0m
      [31m-[0m[0m id                                             = "subnet-008509e3c7a226a65" [90m-> null[0m[0m
      [31m-[0m[0m ipv6_native                                    = false [90m-> null[0m[0m
      [31m-[0m[0m map_customer_owned_ip_on_launch                = false [90m-> null[0m[0m
      [31m-[0m[0m map_public_ip_on_launch                        = false [90m-> null[0m[0m
      [31m-[0m[0m owner_id                                       = "046276255144" [90m-> null[0m[0m
      [31m-[0m[0m private_dns_hostname_type_on_launch            = "ip-name" [90m-> null[0m[0m
      [31m-[0m[0m tags                                           = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-natvpce-subnet-az2"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "Type"        = "Private"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m tags_all                                       = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-natvpce-subnet-az2"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "Type"        = "Private"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m vpc_id                                         = "vpc-05f38ae43b723b6e9" [90m-> null[0m[0m
    }

[1m  # module.intranet_management.module.intranet_management_vpc.aws_vpc.main[0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_vpc" "main" {
      [31m-[0m[0m arn                                  = "arn:aws:ec2:us-east-1:046276255144:vpc/vpc-05f38ae43b723b6e9" [90m-> null[0m[0m
      [31m-[0m[0m assign_generated_ipv6_cidr_block     = false [90m-> null[0m[0m
      [31m-[0m[0m cidr_block                           = "**********/26" [90m-> null[0m[0m
      [31m-[0m[0m default_network_acl_id               = "acl-02f13ba0d02883db6" [90m-> null[0m[0m
      [31m-[0m[0m default_route_table_id               = "rtb-0921c6aa1314e4d76" [90m-> null[0m[0m
      [31m-[0m[0m default_security_group_id            = "sg-04d4b09b1d60de68f" [90m-> null[0m[0m
      [31m-[0m[0m dhcp_options_id                      = "dopt-06b31360512cdef7e" [90m-> null[0m[0m
      [31m-[0m[0m enable_dns_hostnames                 = true [90m-> null[0m[0m
      [31m-[0m[0m enable_dns_support                   = true [90m-> null[0m[0m
      [31m-[0m[0m enable_network_address_usage_metrics = false [90m-> null[0m[0m
      [31m-[0m[0m id                                   = "vpc-05f38ae43b723b6e9" [90m-> null[0m[0m
      [31m-[0m[0m instance_tenancy                     = "default" [90m-> null[0m[0m
      [31m-[0m[0m ipv6_netmask_length                  = 0 [90m-> null[0m[0m
      [31m-[0m[0m main_route_table_id                  = "rtb-0921c6aa1314e4d76" [90m-> null[0m[0m
      [31m-[0m[0m owner_id                             = "046276255144" [90m-> null[0m[0m
      [31m-[0m[0m tags                                 = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-Intranet-management-compartment"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m tags_all                             = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-Intranet-management-compartment"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
    }

[1m  # module.patching.module.patching_vpc.aws_route_table.private["patching_rt"][0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_route_table" "private" {
      [31m-[0m[0m arn              = "arn:aws:ec2:us-east-1:046276255144:route-table/rtb-09532958ed81f7a74" [90m-> null[0m[0m
      [31m-[0m[0m id               = "rtb-09532958ed81f7a74" [90m-> null[0m[0m
      [31m-[0m[0m owner_id         = "046276255144" [90m-> null[0m[0m
      [31m-[0m[0m propagating_vgws = [] [90m-> null[0m[0m
      [31m-[0m[0m route            = [] [90m-> null[0m[0m
      [31m-[0m[0m tags             = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-patching-route-table"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "Type"        = "Private"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m tags_all         = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-patching-route-table"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "Type"        = "Private"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m vpc_id           = "vpc-0b1e6f41c37fe6624" [90m-> null[0m[0m
    }

[1m  # module.patching.module.patching_vpc.aws_route_table_association.private["patching_assoc"][0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_route_table_association" "private" {
      [31m-[0m[0m id             = "rtbassoc-06810216b99dbd053" [90m-> null[0m[0m
      [31m-[0m[0m route_table_id = "rtb-09532958ed81f7a74" [90m-> null[0m[0m
      [31m-[0m[0m subnet_id      = "subnet-0f08b6104f4626204" [90m-> null[0m[0m
    }

[1m  # module.patching.module.patching_vpc.aws_subnet.private["patching_az1"][0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_subnet" "private" {
      [31m-[0m[0m arn                                            = "arn:aws:ec2:us-east-1:046276255144:subnet/subnet-0f08b6104f4626204" [90m-> null[0m[0m
      [31m-[0m[0m assign_ipv6_address_on_creation                = false [90m-> null[0m[0m
      [31m-[0m[0m availability_zone                              = "us-east-1a" [90m-> null[0m[0m
      [31m-[0m[0m availability_zone_id                           = "use1-az6" [90m-> null[0m[0m
      [31m-[0m[0m cidr_block                                     = "**********/28" [90m-> null[0m[0m
      [31m-[0m[0m enable_dns64                                   = false [90m-> null[0m[0m
      [31m-[0m[0m enable_lni_at_device_index                     = 0 [90m-> null[0m[0m
      [31m-[0m[0m enable_resource_name_dns_a_record_on_launch    = false [90m-> null[0m[0m
      [31m-[0m[0m enable_resource_name_dns_aaaa_record_on_launch = false [90m-> null[0m[0m
      [31m-[0m[0m id                                             = "subnet-0f08b6104f4626204" [90m-> null[0m[0m
      [31m-[0m[0m ipv6_native                                    = false [90m-> null[0m[0m
      [31m-[0m[0m map_customer_owned_ip_on_launch                = false [90m-> null[0m[0m
      [31m-[0m[0m map_public_ip_on_launch                        = false [90m-> null[0m[0m
      [31m-[0m[0m owner_id                                       = "046276255144" [90m-> null[0m[0m
      [31m-[0m[0m private_dns_hostname_type_on_launch            = "ip-name" [90m-> null[0m[0m
      [31m-[0m[0m tags                                           = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-patching-subnet-az1"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "Type"        = "Private"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m tags_all                                       = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-patching-subnet-az1"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "Type"        = "Private"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m vpc_id                                         = "vpc-0b1e6f41c37fe6624" [90m-> null[0m[0m
    }

[1m  # module.patching.module.patching_vpc.aws_vpc.main[0m will be [1m[31mdestroyed[0m
[0m  [31m-[0m[0m resource "aws_vpc" "main" {
      [31m-[0m[0m arn                                  = "arn:aws:ec2:us-east-1:046276255144:vpc/vpc-0b1e6f41c37fe6624" [90m-> null[0m[0m
      [31m-[0m[0m assign_generated_ipv6_cidr_block     = false [90m-> null[0m[0m
      [31m-[0m[0m cidr_block                           = "**********/27" [90m-> null[0m[0m
      [31m-[0m[0m default_network_acl_id               = "acl-025178bbfb32c2c4b" [90m-> null[0m[0m
      [31m-[0m[0m default_route_table_id               = "rtb-03ea1ff9908a6b046" [90m-> null[0m[0m
      [31m-[0m[0m default_security_group_id            = "sg-09cccdb5de8a55a0d" [90m-> null[0m[0m
      [31m-[0m[0m dhcp_options_id                      = "dopt-06b31360512cdef7e" [90m-> null[0m[0m
      [31m-[0m[0m enable_dns_hostnames                 = true [90m-> null[0m[0m
      [31m-[0m[0m enable_dns_support                   = true [90m-> null[0m[0m
      [31m-[0m[0m enable_network_address_usage_metrics = false [90m-> null[0m[0m
      [31m-[0m[0m id                                   = "vpc-0b1e6f41c37fe6624" [90m-> null[0m[0m
      [31m-[0m[0m instance_tenancy                     = "default" [90m-> null[0m[0m
      [31m-[0m[0m ipv6_netmask_length                  = 0 [90m-> null[0m[0m
      [31m-[0m[0m main_route_table_id                  = "rtb-03ea1ff9908a6b046" [90m-> null[0m[0m
      [31m-[0m[0m owner_id                             = "046276255144" [90m-> null[0m[0m
      [31m-[0m[0m tags                                 = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-Patching-Compartment"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
      [31m-[0m[0m tags_all                             = {
          [31m-[0m[0m "Backup"      = "Required"
          [31m-[0m[0m "CostCenter"  = "InfoVault-Development"
          [31m-[0m[0m "Environment" = "dev"
          [31m-[0m[0m "ManagedBy"   = "Terraform"
          [31m-[0m[0m "Monitoring"  = "Enabled"
          [31m-[0m[0m "Name"        = "infovault-dev-Patching-Compartment"
          [31m-[0m[0m "Owner"       = "InfoVault-IAC-Team"
          [31m-[0m[0m "Project"     = "InfoVault"
          [31m-[0m[0m "Region"      = "us-east-1"
          [31m-[0m[0m "Terraform"   = "true"
          [31m-[0m[0m "UseCase"     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
        } [90m-> null[0m[0m
    }

[1mPlan:[0m 0 to add, 0 to change, 74 to destroy.
[0m
Changes to Outputs:
  [31m-[0m[0m aws_region  = "us-east-1" [90m-> null[0m[0m
  [31m-[0m[0m environment = "dev" [90m-> null[0m[0m
[33m╷[0m[0m
[33m│[0m [0m[1m[33mWarning: [0m[0m[1mArgument is deprecated[0m
[33m│[0m [0m
[33m│[0m [0m[0m  with module.eks.module.eks_cluster.aws_eks_addon.amazon_cloudwatch_observability,
[33m│[0m [0m  on ../../modules/compute/eks/main.tf line 127, in resource "aws_eks_addon" "amazon_cloudwatch_observability":
[33m│[0m [0m 127:   resolve_conflicts = [4m"OVERWRITE"[0m[0m
[33m│[0m [0m
[33m│[0m [0mresolve_conflicts is deprecated. The resolve_conflicts attribute can't be
[33m│[0m [0mset to "PRESERVE" on initial resource creation. Use
[33m│[0m [0mresolve_conflicts_on_create and/or resolve_conflicts_on_update instead.
[33m│[0m [0m
[33m│[0m [0m(and 6 more similar warnings elsewhere)
[33m╵[0m[0m
[90m
─────────────────────────────────────────────────────────────────────────────[0m

Note: You didn't use the -out option to save this plan, so Terraform can't
guarantee to take exactly these actions if you run "terraform apply" now.
Releasing state lock. This may take a few moments...
