#!/bin/bash

# S3 Native Locking Test Script
# This script tests Terraform S3 native locking functionality

set -e

echo "🔒 Testing S3 Native Locking for Terraform State"
echo "================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Test 1: Basic state operations
echo ""
print_status "Test 1: Basic state operations with S3 native locking"
echo "------------------------------------------------------"

print_status "Running terraform plan..."
terraform plan -out=test.tfplan

print_status "Checking if lock file is created during plan..."
# The lock file should be created and removed automatically
if [ -f ".terraform.tfstate.lock.info" ]; then
    print_warning "Lock file still exists after plan - this might indicate an issue"
    cat .terraform.tfstate.lock.info
else
    print_success "Lock file was properly created and removed during plan operation"
fi

echo ""
print_status "Test 2: Apply with locking (this will take ~10 seconds due to time_sleep resource)"
echo "--------------------------------------------------------------------------------"

print_status "Running terraform apply..."
terraform apply test.tfplan

print_success "Apply completed successfully with S3 native locking"

echo ""
print_status "Test 3: Checking state file in S3"
echo "----------------------------------"

print_status "Verifying state file exists in S3..."
aws s3 ls s3://infovault-s3-native-lock-setup-20250627/backend/ || print_warning "Could not list S3 bucket contents (check AWS credentials)"

echo ""
print_status "Test 4: State refresh with locking"
echo "----------------------------------"

print_status "Running terraform refresh..."
terraform refresh

print_success "Refresh completed successfully with S3 native locking"

echo ""
print_status "Test 5: Showing current state"
echo "-----------------------------"

terraform show

echo ""
print_success "All S3 native locking tests completed successfully!"
echo ""
print_status "Key observations:"
echo "- S3 native locking uses 'use_lockfile = true' in backend configuration"
echo "- No DynamoDB table required for locking"
echo "- Lock files are automatically managed by Terraform"
echo "- State operations complete successfully with proper locking"

# Cleanup
rm -f test.tfplan

echo ""
print_status "Test script completed. You can now run 'terraform destroy' to clean up test resources."
