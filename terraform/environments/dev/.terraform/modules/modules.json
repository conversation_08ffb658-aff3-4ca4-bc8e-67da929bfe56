{"Modules": [{"Key": "", "Source": "", "Dir": "."}, {"Key": "ablrhf", "Source": "../../stacks/networking/ablrhf-compartment", "Dir": "../../stacks/networking/ablrhf-compartment"}, {"Key": "ablrhf.ablrhf-compartment", "Source": "../../../modules/networking/ablrhf-compartment", "Dir": "../../modules/networking/ablrhf-compartment"}, {"Key": "ablrhf.ablrhf_vpc", "Source": "../../../modules/networking/vpc_compartment", "Dir": "../../modules/networking/vpc_compartment"}, {"Key": "ad_tooling_server", "Source": "../../modules/compute/ec2", "Dir": "../../modules/compute/ec2"}, {"Key": "app_data_bucket", "Source": "../../modules/s3", "Dir": "../../modules/s3"}, {"Key": "dra_admin_server", "Source": "../../modules/compute/ec2", "Dir": "../../modules/compute/ec2"}, {"Key": "dra_analytics_server", "Source": "../../modules/compute/ec2", "Dir": "../../modules/compute/ec2"}, {"Key": "ec2_instances", "Source": "../../stacks/compute/ec2", "Dir": "../../stacks/compute/ec2"}, {"Key": "ec2_instances.ad_tooling_windows_02", "Source": "../../../modules/compute/ec2", "Dir": "../../modules/compute/ec2"}, {"Key": "ec2_instances.dev_dra_admin_server_new", "Source": "../../../modules/compute/ec2", "Dir": "../../modules/compute/ec2"}, {"Key": "ec2_instances.dev_dra_analytics_server_new", "Source": "../../../modules/compute/ec2", "Dir": "../../modules/compute/ec2"}, {"Key": "ec2_instances.dev_management_server_avm150_imperva", "Source": "../../../modules/compute/ec2", "Dir": "../../modules/compute/ec2"}, {"Key": "ec2_instances.dev_management_server_avm150_new", "Source": "../../../modules/compute/ec2", "Dir": "../../modules/compute/ec2"}, {"Key": "ec2_instances.infovault_dev_gitlab_runner", "Source": "../../../modules/compute/ec2", "Dir": "../../modules/compute/ec2"}, {"Key": "ec2_instances.infovault_dev_linux_tooling_server", "Source": "../../../modules/compute/ec2", "Dir": "../../modules/compute/ec2"}, {"Key": "ec2_instances.mgmt_newgenadm_win_tooling_01", "Source": "../../../modules/compute/ec2", "Dir": "../../modules/compute/ec2"}, {"Key": "eks", "Source": "../../stacks/compute/eks", "Dir": "../../stacks/compute/eks"}, {"Key": "eks.eks_cluster", "Source": "../../../modules/compute/eks", "Dir": "../../modules/compute/eks"}, {"Key": "gen_facing", "Source": "../../stacks/networking/gen-facing-compartment", "Dir": "../../stacks/networking/gen-facing-compartment"}, {"Key": "gen_facing.gen-facing-compartment", "Source": "../../../modules/networking/gen-facing-compartment", "Dir": "../../modules/networking/gen-facing-compartment"}, {"Key": "gen_facing.gen_facing_vpc", "Source": "../../../modules/networking/vpc_compartment", "Dir": "../../modules/networking/vpc_compartment"}, {"Key": "gitlab_runner", "Source": "../../modules/compute/ec2", "Dir": "../../modules/compute/ec2"}, {"Key": "internet_facing", "Source": "../../stacks/networking/Internet-facing-compartment", "Dir": "../../stacks/networking/Internet-facing-compartment"}, {"Key": "internet_facing.Internet-facing-compartment", "Source": "../../../modules/networking/Internet-facing-compartment", "Dir": "../../modules/networking/Internet-facing-compartment"}, {"Key": "internet_facing_security_groups", "Source": "../../stacks/security/security_groups", "Dir": "../../stacks/security/security_groups"}, {"Key": "internet_facing_security_groups.internet_facing_security_groups", "Source": "../../../modules/security/security_groups", "Dir": "../../modules/security/security_groups"}, {"Key": "intranet_management", "Source": "../../stacks/networking/intranet_management", "Dir": "../../stacks/networking/intranet_management"}, {"Key": "intranet_management.intranet_management_vpc", "Source": "../../../modules/networking/vpc_compartment", "Dir": "../../modules/networking/vpc_compartment"}, {"Key": "linux_tooling_server", "Source": "../../modules/compute/ec2", "Dir": "../../modules/compute/ec2"}, {"Key": "patching", "Source": "../../stacks/networking/Patching-Compartment", "Dir": "../../stacks/networking/Patching-Compartment"}, {"Key": "patching.Patching-Compartment", "Source": "../../../modules/networking/Patching-Compartment", "Dir": "../../modules/networking/Patching-Compartment"}, {"Key": "patching.patching_vpc", "Source": "../../../modules/networking/vpc_compartment", "Dir": "../../modules/networking/vpc_compartment"}, {"Key": "windows_tooling_server", "Source": "../../modules/compute/ec2", "Dir": "../../modules/compute/ec2"}]}