# Variables for InfoVault DEV Environment
# Use Case 4: Setup / Manage AWS Infrastructure (DEV)

variable "environment" {
  description = "Environment name"
  type        = string
  default     = "dev"
}

variable "aws_region" {
  description = "AWS region for resources"
  type        = string
  default     = "ap-southeast-1"
}

variable "project_name" {
  description = "Project name for resource naming"
  type        = string
  default     = "infovault"
}

# S3 Configuration
variable "s3_bucket_name" {
  description = "Name for the InfoVault application S3 bucket (auto-generated if empty)"
  type        = string
  default     = ""
}

variable "s3_force_destroy" {
  description = "Allow force destroy of S3 bucket (for dev/testing environments)"
  type        = bool
  default     = true
}

# S3 Versioning
variable "enable_s3_versioning" {
  description = "Enable versioning on S3 bucket"
  type        = bool
  default     = true
}

# S3 Encryption
variable "s3_encryption_algorithm" {
  description = "Server-side encryption algorithm (AES256 or aws:kms)"
  type        = string
  default     = "AES256"
}

variable "s3_kms_key_id" {
  description = "KMS key ID for encryption (required if encryption_algorithm is aws:kms)"
  type        = string
  default     = null
}

variable "s3_bucket_key_enabled" {
  description = "Enable S3 bucket key for KMS encryption"
  type        = bool
  default     = true
}

# S3 Public Access Block
variable "s3_block_public_acls" {
  description = "Block public ACLs"
  type        = bool
  default     = true
}

variable "s3_block_public_policy" {
  description = "Block public bucket policies"
  type        = bool
  default     = true
}

variable "s3_ignore_public_acls" {
  description = "Ignore public ACLs"
  type        = bool
  default     = true
}

variable "s3_restrict_public_buckets" {
  description = "Restrict public bucket policies"
  type        = bool
  default     = true
}

# S3 Lifecycle Management
variable "s3_lifecycle_enabled" {
  description = "Enable lifecycle management"
  type        = bool
  default     = true
}

variable "s3_transition_to_ia_days" {
  description = "Number of days after which to transition objects to Standard-IA"
  type        = number
  default     = 30
}

variable "s3_transition_to_glacier_days" {
  description = "Number of days after which to transition objects to Glacier"
  type        = number
  default     = 90
}

variable "s3_expiration_days" {
  description = "Number of days after which to expire objects (0 = disabled)"
  type        = number
  default     = 365
}

variable "s3_multipart_upload_days" {
  description = "Number of days after which to abort incomplete multipart uploads"
  type        = number
  default     = 7
}

# S3 Logging
variable "s3_logging_enabled" {
  description = "Enable S3 access logging"
  type        = bool
  default     = false
}

variable "s3_logging_target_bucket" {
  description = "Target bucket for access logs"
  type        = string
  default     = ""
}

variable "s3_logging_target_prefix" {
  description = "Prefix for access log objects"
  type        = string
  default     = "access-logs/"
}

# S3 Notifications
variable "s3_notification_enabled" {
  description = "Enable S3 bucket notifications"
  type        = bool
  default     = false
}

# =============================================================================
# NETWORKING VARIABLES
# =============================================================================

# VPC Compartment Configuration Objects
variable "intranet_management" {
  description = "Configuration for Intranet Management VPC compartment"
  type = object({
    # VPC Configuration
    vpc_cidr                             = string
    vpc_name                             = string
    enable_dns_hostnames                 = bool
    enable_dns_support                   = bool
    enable_network_address_usage_metrics = bool
    instance_tenancy                     = string
    create_internet_gateway              = bool

    # Subnets
    public_subnets = map(object({
      cidr_block                      = string
      availability_zone               = string
      map_public_ip_on_launch         = bool
      assign_ipv6_address_on_creation = bool
      name                            = string
    }))

    private_subnets = map(object({
      cidr_block                      = string
      availability_zone               = string
      map_public_ip_on_launch         = bool
      assign_ipv6_address_on_creation = bool
      name                            = string
    }))

    # NAT Gateways
    nat_gateways = map(object({
      name       = string
      subnet_key = string
    }))

    # Route Tables
    public_route_tables = map(object({
      name = string
    }))

    private_route_tables = map(object({
      name = string
    }))

    # Route Table Associations
    public_subnet_route_associations = map(object({
      subnet_key      = string
      route_table_key = string
    }))

    private_subnet_route_associations = map(object({
      subnet_key      = string
      route_table_key = string
    }))

    # Routes
    private_nat_routes = map(object({
      route_table_key        = string
      destination_cidr_block = string
      nat_gateway_key        = string
    }))

    vpc_peering_routes = map(object({
      route_table_key           = string
      route_table_type          = string
      destination_cidr_block    = string
      vpc_peering_connection_id = string
    }))

    # Security Groups
    security_groups = map(object({
      name_prefix = string
      description = string
      name        = string
    }))

    security_group_ingress_rules = map(object({
      security_group_key       = string
      from_port                = number
      to_port                  = number
      protocol                 = string
      cidr_blocks              = list(string)
      source_security_group_id = string
      description              = string
    }))

    security_group_egress_rules = map(object({
      security_group_key       = string
      from_port                = number
      to_port                  = number
      protocol                 = string
      cidr_blocks              = list(string)
      source_security_group_id = string
      description              = string
    }))

    # Network ACLs
    network_acls = map(object({
      name        = string
      subnet_keys = list(string)
    }))

    network_acl_ingress_rules = map(object({
      network_acl_key = string
      rule_number     = number
      protocol        = string
      rule_action     = string
      cidr_block      = string
      from_port       = number
      to_port         = number
    }))

    network_acl_egress_rules = map(object({
      network_acl_key = string
      rule_number     = number
      protocol        = string
      rule_action     = string
      cidr_block      = string
      from_port       = number
      to_port         = number
    }))

    # VPC Endpoints
    vpc_endpoints = map(object({
      name                = string
      service_name        = string
      vpc_endpoint_type   = string
      route_table_ids     = list(string)
      subnet_ids          = list(string)
      security_group_ids  = list(string)
      private_dns_enabled = bool
    }))
  })
}

variable "internet_facing" {
  description = "Configuration for Internet Facing VPC compartment"
  type = object({
    # VPC Configuration
    vpc_cidr                             = string
    vpc_name                             = string
    enable_dns_hostnames                 = bool
    enable_dns_support                   = bool
    enable_network_address_usage_metrics = bool
    instance_tenancy                     = string
    create_internet_gateway              = bool

    # Subnets
    public_subnets = map(object({
      cidr_block                      = string
      availability_zone               = string
      map_public_ip_on_launch         = bool
      assign_ipv6_address_on_creation = bool
      name                            = string
    }))

    private_subnets = map(object({
      cidr_block                      = string
      availability_zone               = string
      map_public_ip_on_launch         = bool
      assign_ipv6_address_on_creation = bool
      name                            = string
    }))

    # NAT Gateways
    nat_gateways = map(object({
      name       = string
      subnet_key = string
    }))

    # Route Tables
    public_route_tables = map(object({
      name = string
    }))

    private_route_tables = map(object({
      name = string
    }))

    # Route Table Associations
    public_subnet_route_associations = map(object({
      subnet_key      = string
      route_table_key = string
    }))

    private_subnet_route_associations = map(object({
      subnet_key      = string
      route_table_key = string
    }))

    # Routes
    private_nat_routes = map(object({
      route_table_key        = string
      destination_cidr_block = string
      nat_gateway_key        = string
    }))

    vpc_peering_routes = map(object({
      route_table_key           = string
      route_table_type          = string
      destination_cidr_block    = string
      vpc_peering_connection_id = string
    }))

    # Security Groups
    security_groups = map(object({
      name_prefix = string
      description = string
      name        = string
    }))

    security_group_ingress_rules = map(object({
      security_group_key       = string
      from_port                = number
      to_port                  = number
      protocol                 = string
      cidr_blocks              = list(string)
      source_security_group_id = string
      description              = string
    }))

    security_group_egress_rules = map(object({
      security_group_key       = string
      from_port                = number
      to_port                  = number
      protocol                 = string
      cidr_blocks              = list(string)
      source_security_group_id = string
      description              = string
    }))

    # Network ACLs
    network_acls = map(object({
      name        = string
      subnet_keys = list(string)
    }))

    network_acl_ingress_rules = map(object({
      network_acl_key = string
      rule_number     = number
      protocol        = string
      rule_action     = string
      cidr_block      = string
      from_port       = number
      to_port         = number
    }))

    network_acl_egress_rules = map(object({
      network_acl_key = string
      rule_number     = number
      protocol        = string
      rule_action     = string
      cidr_block      = string
      from_port       = number
      to_port         = number
    }))

    # VPC Endpoints
    vpc_endpoints = map(object({
      name                = string
      service_name        = string
      vpc_endpoint_type   = string
      route_table_ids     = list(string)
      subnet_ids          = list(string)
      security_group_ids  = list(string)
      private_dns_enabled = bool
    }))
  })
}

variable "gen_facing" {
  description = "Configuration for Gen Facing VPC compartment"
  type = object({
    # VPC Configuration
    vpc_cidr                             = string
    vpc_name                             = string
    enable_dns_hostnames                 = bool
    enable_dns_support                   = bool
    enable_network_address_usage_metrics = bool
    instance_tenancy                     = string
    create_internet_gateway              = bool
    secondary_cidr_blocks                = list(string)

    # Subnets
    public_subnets = map(object({
      cidr_block                      = string
      availability_zone               = string
      map_public_ip_on_launch         = bool
      assign_ipv6_address_on_creation = bool
      name                            = string
    }))

    private_subnets = map(object({
      cidr_block                      = string
      availability_zone               = string
      map_public_ip_on_launch         = bool
      assign_ipv6_address_on_creation = bool
      name                            = string
    }))

    # NAT Gateways
    nat_gateways = map(object({
      name       = string
      subnet_key = string
    }))

    # Route Tables
    public_route_tables = map(object({
      name = string
    }))

    private_route_tables = map(object({
      name = string
    }))

    # Route Table Associations
    public_subnet_route_associations = map(object({
      subnet_key      = string
      route_table_key = string
    }))

    private_subnet_route_associations = map(object({
      subnet_key      = string
      route_table_key = string
    }))

    # Routes
    private_nat_routes = map(object({
      route_table_key        = string
      destination_cidr_block = string
      nat_gateway_key        = string
    }))

    vpc_peering_routes = map(object({
      route_table_key           = string
      route_table_type          = string
      destination_cidr_block    = string
      vpc_peering_connection_id = string
    }))

    # Security Groups
    security_groups = map(object({
      name_prefix = string
      description = string
      name        = string
    }))

    security_group_ingress_rules = map(object({
      security_group_key       = string
      from_port                = number
      to_port                  = number
      protocol                 = string
      cidr_blocks              = list(string)
      source_security_group_id = string
      description              = string
    }))

    security_group_egress_rules = map(object({
      security_group_key       = string
      from_port                = number
      to_port                  = number
      protocol                 = string
      cidr_blocks              = list(string)
      source_security_group_id = string
      description              = string
    }))

    # Network ACLs
    network_acls = map(object({
      name        = string
      subnet_keys = list(string)
    }))

    network_acl_ingress_rules = map(object({
      network_acl_key = string
      rule_number     = number
      protocol        = string
      rule_action     = string
      cidr_block      = string
      from_port       = number
      to_port         = number
    }))

    network_acl_egress_rules = map(object({
      network_acl_key = string
      rule_number     = number
      protocol        = string
      rule_action     = string
      cidr_block      = string
      from_port       = number
      to_port         = number
    }))

    # VPC Endpoints
    vpc_endpoints = map(object({
      name                = string
      service_name        = string
      vpc_endpoint_type   = string
      route_table_ids     = list(string)
      subnet_ids          = list(string)
      security_group_ids  = list(string)
      private_dns_enabled = bool
    }))
  })
}

variable "ablrhf" {
  description = "Configuration for ABLRHF VPC compartment"
  type = object({
    # VPC Configuration
    vpc_cidr                             = string
    vpc_name                             = string
    enable_dns_hostnames                 = bool
    enable_dns_support                   = bool
    enable_network_address_usage_metrics = bool
    instance_tenancy                     = string
    create_internet_gateway              = bool

    # Subnets
    public_subnets = map(object({
      cidr_block                      = string
      availability_zone               = string
      map_public_ip_on_launch         = bool
      assign_ipv6_address_on_creation = bool
      name                            = string
    }))

    private_subnets = map(object({
      cidr_block                      = string
      availability_zone               = string
      map_public_ip_on_launch         = bool
      assign_ipv6_address_on_creation = bool
      name                            = string
    }))

    # NAT Gateways
    nat_gateways = map(object({
      name       = string
      subnet_key = string
    }))

    # Route Tables
    public_route_tables = map(object({
      name = string
    }))

    private_route_tables = map(object({
      name = string
    }))

    # Route Table Associations
    public_subnet_route_associations = map(object({
      subnet_key      = string
      route_table_key = string
    }))

    private_subnet_route_associations = map(object({
      subnet_key      = string
      route_table_key = string
    }))

    # Routes
    private_nat_routes = map(object({
      route_table_key        = string
      destination_cidr_block = string
      nat_gateway_key        = string
    }))

    vpc_peering_routes = map(object({
      route_table_key           = string
      route_table_type          = string
      destination_cidr_block    = string
      vpc_peering_connection_id = string
    }))

    # Security Groups
    security_groups = map(object({
      name_prefix = string
      description = string
      name        = string
    }))

    security_group_ingress_rules = map(object({
      security_group_key       = string
      from_port                = number
      to_port                  = number
      protocol                 = string
      cidr_blocks              = list(string)
      source_security_group_id = string
      description              = string
    }))

    security_group_egress_rules = map(object({
      security_group_key       = string
      from_port                = number
      to_port                  = number
      protocol                 = string
      cidr_blocks              = list(string)
      source_security_group_id = string
      description              = string
    }))

    # Network ACLs
    network_acls = map(object({
      name        = string
      subnet_keys = list(string)
    }))

    network_acl_ingress_rules = map(object({
      network_acl_key = string
      rule_number     = number
      protocol        = string
      rule_action     = string
      cidr_block      = string
      from_port       = number
      to_port         = number
    }))

    network_acl_egress_rules = map(object({
      network_acl_key = string
      rule_number     = number
      protocol        = string
      rule_action     = string
      cidr_block      = string
      from_port       = number
      to_port         = number
    }))

    # VPC Endpoints
    vpc_endpoints = map(object({
      name                = string
      service_name        = string
      vpc_endpoint_type   = string
      route_table_ids     = list(string)
      subnet_ids          = list(string)
      security_group_ids  = list(string)
      private_dns_enabled = bool
    }))
  })
}

variable "patching" {
  description = "Configuration for Patching VPC compartment"
  type = object({
    # VPC Configuration
    vpc_cidr                             = string
    vpc_name                             = string
    enable_dns_hostnames                 = bool
    enable_dns_support                   = bool
    enable_network_address_usage_metrics = bool
    instance_tenancy                     = string
    create_internet_gateway              = bool

    # Subnets
    public_subnets = map(object({
      cidr_block                      = string
      availability_zone               = string
      map_public_ip_on_launch         = bool
      assign_ipv6_address_on_creation = bool
      name                            = string
    }))

    private_subnets = map(object({
      cidr_block                      = string
      availability_zone               = string
      map_public_ip_on_launch         = bool
      assign_ipv6_address_on_creation = bool
      name                            = string
    }))

    # NAT Gateways
    nat_gateways = map(object({
      name       = string
      subnet_key = string
    }))

    # Route Tables
    public_route_tables = map(object({
      name = string
    }))

    private_route_tables = map(object({
      name = string
    }))

    # Route Table Associations
    public_subnet_route_associations = map(object({
      subnet_key      = string
      route_table_key = string
    }))

    private_subnet_route_associations = map(object({
      subnet_key      = string
      route_table_key = string
    }))

    # Routes
    private_nat_routes = map(object({
      route_table_key        = string
      destination_cidr_block = string
      nat_gateway_key        = string
    }))

    vpc_peering_routes = map(object({
      route_table_key           = string
      route_table_type          = string
      destination_cidr_block    = string
      vpc_peering_connection_id = string
    }))

    # Security Groups
    security_groups = map(object({
      name_prefix = string
      description = string
      name        = string
    }))

    security_group_ingress_rules = map(object({
      security_group_key       = string
      from_port                = number
      to_port                  = number
      protocol                 = string
      cidr_blocks              = list(string)
      source_security_group_id = string
      description              = string
    }))

    security_group_egress_rules = map(object({
      security_group_key       = string
      from_port                = number
      to_port                  = number
      protocol                 = string
      cidr_blocks              = list(string)
      source_security_group_id = string
      description              = string
    }))

    # Network ACLs
    network_acls = map(object({
      name        = string
      subnet_keys = list(string)
    }))

    network_acl_ingress_rules = map(object({
      network_acl_key = string
      rule_number     = number
      protocol        = string
      rule_action     = string
      cidr_block      = string
      from_port       = number
      to_port         = number
    }))

    network_acl_egress_rules = map(object({
      network_acl_key = string
      rule_number     = number
      protocol        = string
      rule_action     = string
      cidr_block      = string
      from_port       = number
      to_port         = number
    }))

    # VPC Endpoints
    vpc_endpoints = map(object({
      name                = string
      service_name        = string
      vpc_endpoint_type   = string
      route_table_ids     = list(string)
      subnet_ids          = list(string)
      security_group_ids  = list(string)
      private_dns_enabled = bool
    }))
  })
}

# Common Tags
variable "common_tags" {
  description = "Common tags to apply to all resources"
  type        = map(string)
  default = {
    Project     = "InfoVault"
    Environment = "dev"
    ManagedBy   = "Terraform"
    UseCase     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
  }
}

# Internet Facing Security Groups
variable "internet_facing_security_groups" {
  description = "Map of security groups to create for Internet Facing VPC"
  type = map(object({
    name        = string
    description = string
  }))
  default = {}
}

variable "internet_facing_sg_ingress_rules" {
  description = "Map of ingress rules for Internet Facing security groups"
  type = map(object({
    security_group_key       = string
    from_port                = number
    to_port                  = number
    protocol                 = string
    cidr_blocks              = optional(list(string))
    source_security_group_id = optional(string)
    description              = string
  }))
  default = {}
}

variable "internet_facing_sg_egress_rules" {
  description = "Map of egress rules for Internet Facing security groups"
  type = map(object({
    security_group_key            = string
    from_port                     = number
    to_port                       = number
    protocol                      = string
    cidr_blocks                   = optional(list(string))
    destination_security_group_id = optional(string)
    description                   = string
  }))
  default = {}
}

# =============================================================================
# EKS VARIABLES
# =============================================================================

# EKS Cluster Variables
variable "cluster_name" {
  description = "Name of the EKS cluster"
  type        = string
}

variable "cluster_version" {
  description = "Kubernetes version for the EKS cluster"
  type        = string
}

variable "cluster_role_name" {
  description = "Name of the IAM role for the EKS cluster"
  type        = string
}

variable "node_group_role_name" {
  description = "Name of the IAM role for EKS node groups"
  type        = string
}

variable "subnet_ids" {
  description = "List of subnet IDs for the EKS cluster"
  type        = list(string)
}

variable "cluster_security_group_ids" {
  description = "List of security group IDs for the EKS cluster"
  type        = list(string)
  default     = []
}

variable "endpoint_private_access" {
  description = "Enable private API server endpoint"
  type        = bool
  default     = true
}

variable "endpoint_public_access" {
  description = "Enable public API server endpoint"
  type        = bool
  default     = true
}

variable "public_access_cidrs" {
  description = "List of CIDR blocks that can access the public API server endpoint"
  type        = list(string)
  default     = ["0.0.0.0/0"]
}

variable "cluster_log_types" {
  description = "List of control plane logging types to enable"
  type        = list(string)
  default     = ["api", "audit", "authenticator", "controllerManager", "scheduler"]
}

variable "kms_key_arn" {
  description = "ARN of the KMS key for EKS cluster encryption"
  type        = string
  default     = null
}

# Node Group Variables
variable "node_group_name" {
  description = "Name of the EKS node group"
  type        = string
}

variable "node_group_subnet_ids" {
  description = "List of subnet IDs for the EKS node group"
  type        = list(string)
}

variable "node_group_instance_types" {
  description = "List of instance types for the EKS node group"
  type        = list(string)
  default     = ["t3.medium"]
}

variable "node_group_ami_type" {
  description = "AMI type for the EKS node group"
  type        = string
  default     = "AL2_x86_64"
}

variable "node_group_capacity_type" {
  description = "Capacity type for the EKS node group (ON_DEMAND or SPOT)"
  type        = string
  default     = "ON_DEMAND"
}

variable "node_group_disk_size" {
  description = "Disk size in GB for EKS node group instances"
  type        = number
  default     = 20
}

variable "node_group_desired_size" {
  description = "Desired number of nodes in the EKS node group"
  type        = number
  default     = 2
}

variable "node_group_max_size" {
  description = "Maximum number of nodes in the EKS node group"
  type        = number
  default     = 4
}

variable "node_group_min_size" {
  description = "Minimum number of nodes in the EKS node group"
  type        = number
  default     = 1
}

variable "node_group_labels" {
  description = "Key-value map of Kubernetes labels for EKS node group"
  type        = map(string)
  default     = {}
}

# EKS Addon Variables
variable "addon_amazon_cloudwatch_observability_version" {
  description = "Version of the Amazon CloudWatch Observability addon"
  type        = string
  default     = null
}

variable "addon_aws_ebs_csi_driver_version" {
  description = "Version of the AWS EBS CSI driver addon"
  type        = string
  default     = null
}

variable "addon_aws_guardduty_agent_version" {
  description = "Version of the AWS GuardDuty agent addon"
  type        = string
  default     = null
}

variable "addon_coredns_version" {
  description = "Version of the CoreDNS addon"
  type        = string
  default     = null
}

variable "addon_eks_pod_identity_agent_version" {
  description = "Version of the EKS Pod Identity Agent addon"
  type        = string
  default     = null
}

variable "addon_kube_proxy_version" {
  description = "Version of the kube-proxy addon"
  type        = string
  default     = null
}

variable "addon_vpc_cni_version" {
  description = "Version of the VPC CNI addon"
  type        = string
  default     = null
}

# =============================================================================
# EC2 VARIABLES
# =============================================================================

# Variables for infovault-dev-gitlab-runner
variable "infovault_dev_gitlab_runner_name" {
  description = "Name of the infovault-dev-gitlab-runner instance"
  type        = string
}

variable "infovault_dev_gitlab_runner_ami_id" {
  description = "AMI ID for infovault-dev-gitlab-runner"
  type        = string
}

variable "infovault_dev_gitlab_runner_instance_type" {
  description = "Instance type for infovault-dev-gitlab-runner"
  type        = string
}

variable "infovault_dev_gitlab_runner_key_name" {
  description = "Key pair name for infovault-dev-gitlab-runner"
  type        = string
  default     = null
}

variable "infovault_dev_gitlab_runner_security_group_ids" {
  description = "Security group IDs for infovault-dev-gitlab-runner"
  type        = list(string)
}

variable "infovault_dev_gitlab_runner_subnet_id" {
  description = "Subnet ID for infovault-dev-gitlab-runner"
  type        = string
}

variable "infovault_dev_gitlab_runner_iam_instance_profile_name" {
  description = "IAM instance profile name for infovault-dev-gitlab-runner"
  type        = string
  default     = null
}

variable "infovault_dev_gitlab_runner_availability_zone" {
  description = "Availability zone for infovault-dev-gitlab-runner"
  type        = string
}

variable "infovault_dev_gitlab_runner_monitoring_enabled" {
  description = "Enable monitoring for infovault-dev-gitlab-runner"
  type        = bool
  default     = false
}

variable "infovault_dev_gitlab_runner_ebs_optimized" {
  description = "Enable EBS optimization for infovault-dev-gitlab-runner"
  type        = bool
  default     = false
}

variable "infovault_dev_gitlab_runner_source_dest_check" {
  description = "Enable source destination check for infovault-dev-gitlab-runner"
  type        = bool
  default     = true
}

variable "infovault_dev_gitlab_runner_private_ip_address" {
  description = "Private IP address for infovault-dev-gitlab-runner"
  type        = string
  default     = null
}

variable "infovault_dev_gitlab_runner_root_block_device" {
  description = "Root block device configuration for infovault-dev-gitlab-runner"
  type = object({
    volume_type           = string
    volume_size           = number
    delete_on_termination = bool
    encrypted             = bool
  })
  default = null
}

variable "infovault_dev_gitlab_runner_ebs_block_devices" {
  description = "Additional EBS block devices for infovault-dev-gitlab-runner"
  type = list(object({
    device_name           = string
    volume_type           = string
    volume_size           = number
    delete_on_termination = bool
    encrypted             = bool
  }))
  default = []
}

variable "infovault_dev_gitlab_runner_user_data" {
  description = "User data script for infovault-dev-gitlab-runner"
  type        = string
  default     = null
}

# Variables for infovault-dev-linux-tooling-server
variable "infovault_dev_linux_tooling_server_name" {
  description = "Name of the infovault-dev-linux-tooling-server instance"
  type        = string
}

variable "infovault_dev_linux_tooling_server_ami_id" {
  description = "AMI ID for infovault-dev-linux-tooling-server"
  type        = string
}

variable "infovault_dev_linux_tooling_server_instance_type" {
  description = "Instance type for infovault-dev-linux-tooling-server"
  type        = string
}

variable "infovault_dev_linux_tooling_server_key_name" {
  description = "Key pair name for infovault-dev-linux-tooling-server"
  type        = string
  default     = null
}

variable "infovault_dev_linux_tooling_server_security_group_ids" {
  description = "Security group IDs for infovault-dev-linux-tooling-server"
  type        = list(string)
}

variable "infovault_dev_linux_tooling_server_subnet_id" {
  description = "Subnet ID for infovault-dev-linux-tooling-server"
  type        = string
}

variable "infovault_dev_linux_tooling_server_iam_instance_profile_name" {
  description = "IAM instance profile name for infovault-dev-linux-tooling-server"
  type        = string
  default     = null
}

variable "infovault_dev_linux_tooling_server_availability_zone" {
  description = "Availability zone for infovault-dev-linux-tooling-server"
  type        = string
}

variable "infovault_dev_linux_tooling_server_monitoring_enabled" {
  description = "Enable monitoring for infovault-dev-linux-tooling-server"
  type        = bool
  default     = false
}

variable "infovault_dev_linux_tooling_server_ebs_optimized" {
  description = "Enable EBS optimization for infovault-dev-linux-tooling-server"
  type        = bool
  default     = false
}

variable "infovault_dev_linux_tooling_server_source_dest_check" {
  description = "Enable source destination check for infovault-dev-linux-tooling-server"
  type        = bool
  default     = true
}

variable "infovault_dev_linux_tooling_server_private_ip_address" {
  description = "Private IP address for infovault-dev-linux-tooling-server"
  type        = string
  default     = null
}

variable "infovault_dev_linux_tooling_server_root_block_device" {
  description = "Root block device configuration for infovault-dev-linux-tooling-server"
  type = object({
    volume_type           = string
    volume_size           = number
    delete_on_termination = bool
    encrypted             = bool
  })
  default = null
}

variable "infovault_dev_linux_tooling_server_ebs_block_devices" {
  description = "Additional EBS block devices for infovault-dev-linux-tooling-server"
  type = list(object({
    device_name           = string
    volume_type           = string
    volume_size           = number
    delete_on_termination = bool
    encrypted             = bool
  }))
  default = []
}

variable "infovault_dev_linux_tooling_server_user_data" {
  description = "User data script for infovault-dev-linux-tooling-server"
  type        = string
  default     = null
}

# Variables for mgmt-newgenadm-win-tooling-01 (Windows Tooling Server)
variable "mgmt_newgenadm_win_tooling_01_name" {
  description = "Name of the Windows tooling server instance"
  type        = string
}

variable "mgmt_newgenadm_win_tooling_01_ami_id" {
  description = "AMI ID for Windows tooling server"
  type        = string
}

variable "mgmt_newgenadm_win_tooling_01_instance_type" {
  description = "Instance type for Windows tooling server"
  type        = string
}

variable "mgmt_newgenadm_win_tooling_01_key_name" {
  description = "Key pair name for Windows tooling server"
  type        = string
  default     = null
}

variable "mgmt_newgenadm_win_tooling_01_security_group_ids" {
  description = "Security group IDs for Windows tooling server"
  type        = list(string)
}

variable "mgmt_newgenadm_win_tooling_01_subnet_id" {
  description = "Subnet ID for Windows tooling server"
  type        = string
}

variable "mgmt_newgenadm_win_tooling_01_iam_instance_profile_name" {
  description = "IAM instance profile name for Windows tooling server"
  type        = string
  default     = null
}

variable "mgmt_newgenadm_win_tooling_01_availability_zone" {
  description = "Availability zone for Windows tooling server"
  type        = string
}

variable "mgmt_newgenadm_win_tooling_01_monitoring_enabled" {
  description = "Enable monitoring for Windows tooling server"
  type        = bool
  default     = false
}

variable "mgmt_newgenadm_win_tooling_01_ebs_optimized" {
  description = "Enable EBS optimization for Windows tooling server"
  type        = bool
  default     = false
}

variable "mgmt_newgenadm_win_tooling_01_source_dest_check" {
  description = "Enable source destination check for Windows tooling server"
  type        = bool
  default     = true
}

variable "mgmt_newgenadm_win_tooling_01_private_ip_address" {
  description = "Private IP address for Windows tooling server"
  type        = string
  default     = null
}

variable "mgmt_newgenadm_win_tooling_01_root_block_device" {
  description = "Root block device configuration for Windows tooling server"
  type = object({
    volume_type           = string
    volume_size           = number
    delete_on_termination = bool
    encrypted             = bool
  })
  default = null
}

variable "mgmt_newgenadm_win_tooling_01_ebs_block_devices" {
  description = "Additional EBS block devices for Windows tooling server"
  type = list(object({
    device_name           = string
    volume_type           = string
    volume_size           = number
    delete_on_termination = bool
    encrypted             = bool
  }))
  default = []
}

variable "mgmt_newgenadm_win_tooling_01_user_data" {
  description = "User data script for Windows tooling server"
  type        = string
  default     = null
}

# Variables for dev-dra-admin-server-new (DRA Admin Server)
variable "dev_dra_admin_server_new_name" {
  description = "Name of the DRA admin server instance"
  type        = string
}

variable "dev_dra_admin_server_new_ami_id" {
  description = "AMI ID for DRA admin server"
  type        = string
}

variable "dev_dra_admin_server_new_instance_type" {
  description = "Instance type for DRA admin server"
  type        = string
}

variable "dev_dra_admin_server_new_key_name" {
  description = "Key pair name for DRA admin server"
  type        = string
  default     = null
}

variable "dev_dra_admin_server_new_security_group_ids" {
  description = "Security group IDs for DRA admin server"
  type        = list(string)
}

variable "dev_dra_admin_server_new_subnet_id" {
  description = "Subnet ID for DRA admin server"
  type        = string
}

variable "dev_dra_admin_server_new_iam_instance_profile_name" {
  description = "IAM instance profile name for DRA admin server"
  type        = string
  default     = null
}

variable "dev_dra_admin_server_new_availability_zone" {
  description = "Availability zone for DRA admin server"
  type        = string
}

variable "dev_dra_admin_server_new_monitoring_enabled" {
  description = "Enable monitoring for DRA admin server"
  type        = bool
  default     = false
}

variable "dev_dra_admin_server_new_ebs_optimized" {
  description = "Enable EBS optimization for DRA admin server"
  type        = bool
  default     = false
}

variable "dev_dra_admin_server_new_source_dest_check" {
  description = "Enable source destination check for DRA admin server"
  type        = bool
  default     = true
}

variable "dev_dra_admin_server_new_private_ip_address" {
  description = "Private IP address for DRA admin server"
  type        = string
  default     = null
}

variable "dev_dra_admin_server_new_root_block_device" {
  description = "Root block device configuration for DRA admin server"
  type = object({
    volume_type           = string
    volume_size           = number
    delete_on_termination = bool
    encrypted             = bool
  })
  default = null
}

variable "dev_dra_admin_server_new_ebs_block_devices" {
  description = "Additional EBS block devices for DRA admin server"
  type = list(object({
    device_name           = string
    volume_type           = string
    volume_size           = number
    delete_on_termination = bool
    encrypted             = bool
  }))
  default = []
}

variable "dev_dra_admin_server_new_user_data" {
  description = "User data script for DRA admin server"
  type        = string
  default     = null
}

# Variables for dev-dra-analytics-server-new (DRA Analytics Server)
variable "dev_dra_analytics_server_new_name" {
  description = "Name of the DRA analytics server instance"
  type        = string
}

variable "dev_dra_analytics_server_new_ami_id" {
  description = "AMI ID for DRA analytics server"
  type        = string
}

variable "dev_dra_analytics_server_new_instance_type" {
  description = "Instance type for DRA analytics server"
  type        = string
}

variable "dev_dra_analytics_server_new_key_name" {
  description = "Key pair name for DRA analytics server"
  type        = string
  default     = null
}

variable "dev_dra_analytics_server_new_security_group_ids" {
  description = "Security group IDs for DRA analytics server"
  type        = list(string)
}

variable "dev_dra_analytics_server_new_subnet_id" {
  description = "Subnet ID for DRA analytics server"
  type        = string
}

variable "dev_dra_analytics_server_new_iam_instance_profile_name" {
  description = "IAM instance profile name for DRA analytics server"
  type        = string
  default     = null
}

variable "dev_dra_analytics_server_new_availability_zone" {
  description = "Availability zone for DRA analytics server"
  type        = string
}

variable "dev_dra_analytics_server_new_monitoring_enabled" {
  description = "Enable monitoring for DRA analytics server"
  type        = bool
  default     = false
}

variable "dev_dra_analytics_server_new_ebs_optimized" {
  description = "Enable EBS optimization for DRA analytics server"
  type        = bool
  default     = false
}

variable "dev_dra_analytics_server_new_source_dest_check" {
  description = "Enable source destination check for DRA analytics server"
  type        = bool
  default     = true
}

variable "dev_dra_analytics_server_new_private_ip_address" {
  description = "Private IP address for DRA analytics server"
  type        = string
  default     = null
}

variable "dev_dra_analytics_server_new_root_block_device" {
  description = "Root block device configuration for DRA analytics server"
  type = object({
    volume_type           = string
    volume_size           = number
    delete_on_termination = bool
    encrypted             = bool
  })
  default = null
}

variable "dev_dra_analytics_server_new_ebs_block_devices" {
  description = "Additional EBS block devices for DRA analytics server"
  type = list(object({
    device_name           = string
    volume_type           = string
    volume_size           = number
    delete_on_termination = bool
    encrypted             = bool
  }))
  default = []
}

variable "dev_dra_analytics_server_new_user_data" {
  description = "User data script for DRA analytics server"
  type        = string
  default     = null
}

# Variables for ad-tooling-windows-02 (AD Tooling Server)
variable "ad_tooling_windows_02_name" {
  description = "Name of the AD tooling server instance"
  type        = string
}

variable "ad_tooling_windows_02_ami_id" {
  description = "AMI ID for AD tooling server"
  type        = string
}

variable "ad_tooling_windows_02_instance_type" {
  description = "Instance type for AD tooling server"
  type        = string
}

variable "ad_tooling_windows_02_key_name" {
  description = "Key pair name for AD tooling server"
  type        = string
  default     = null
}

variable "ad_tooling_windows_02_security_group_ids" {
  description = "Security group IDs for AD tooling server"
  type        = list(string)
}

variable "ad_tooling_windows_02_subnet_id" {
  description = "Subnet ID for AD tooling server"
  type        = string
}

variable "ad_tooling_windows_02_iam_instance_profile_name" {
  description = "IAM instance profile name for AD tooling server"
  type        = string
  default     = null
}

variable "ad_tooling_windows_02_availability_zone" {
  description = "Availability zone for AD tooling server"
  type        = string
}

variable "ad_tooling_windows_02_monitoring_enabled" {
  description = "Enable monitoring for AD tooling server"
  type        = bool
  default     = false
}

variable "ad_tooling_windows_02_ebs_optimized" {
  description = "Enable EBS optimization for AD tooling server"
  type        = bool
  default     = false
}

variable "ad_tooling_windows_02_source_dest_check" {
  description = "Enable source destination check for AD tooling server"
  type        = bool
  default     = true
}

variable "ad_tooling_windows_02_private_ip_address" {
  description = "Private IP address for AD tooling server"
  type        = string
  default     = null
}

variable "ad_tooling_windows_02_root_block_device" {
  description = "Root block device configuration for AD tooling server"
  type = object({
    volume_type           = string
    volume_size           = number
    delete_on_termination = bool
    encrypted             = bool
  })
  default = null
}

variable "ad_tooling_windows_02_ebs_block_devices" {
  description = "Additional EBS block devices for AD tooling server"
  type = list(object({
    device_name           = string
    volume_type           = string
    volume_size           = number
    delete_on_termination = bool
    encrypted             = bool
  }))
  default = []
}

variable "ad_tooling_windows_02_user_data" {
  description = "User data script for AD tooling server"
  type        = string
  default     = null
}

# Variables for dev-management-server-avm150-new
variable "dev_management_server_avm150_new_name" {
  description = "Name of the management server AVM150 new instance"
  type        = string
}

variable "dev_management_server_avm150_new_ami_id" {
  description = "AMI ID for management server AVM150 new"
  type        = string
}

variable "dev_management_server_avm150_new_instance_type" {
  description = "Instance type for management server AVM150 new"
  type        = string
}

variable "dev_management_server_avm150_new_key_name" {
  description = "Key pair name for management server AVM150 new"
  type        = string
  default     = null
}

variable "dev_management_server_avm150_new_security_group_ids" {
  description = "Security group IDs for management server AVM150 new"
  type        = list(string)
}

variable "dev_management_server_avm150_new_subnet_id" {
  description = "Subnet ID for management server AVM150 new"
  type        = string
}

variable "dev_management_server_avm150_new_iam_instance_profile_name" {
  description = "IAM instance profile name for management server AVM150 new"
  type        = string
  default     = null
}

variable "dev_management_server_avm150_new_availability_zone" {
  description = "Availability zone for management server AVM150 new"
  type        = string
}

variable "dev_management_server_avm150_new_monitoring_enabled" {
  description = "Enable monitoring for management server AVM150 new"
  type        = bool
  default     = false
}

variable "dev_management_server_avm150_new_ebs_optimized" {
  description = "Enable EBS optimization for management server AVM150 new"
  type        = bool
  default     = false
}

variable "dev_management_server_avm150_new_source_dest_check" {
  description = "Enable source destination check for management server AVM150 new"
  type        = bool
  default     = true
}

variable "dev_management_server_avm150_new_private_ip_address" {
  description = "Private IP address for management server AVM150 new"
  type        = string
  default     = null
}

variable "dev_management_server_avm150_new_root_block_device" {
  description = "Root block device configuration for management server AVM150 new"
  type = object({
    volume_type           = string
    volume_size           = number
    delete_on_termination = bool
    encrypted             = bool
  })
  default = null
}

variable "dev_management_server_avm150_new_ebs_block_devices" {
  description = "Additional EBS block devices for management server AVM150 new"
  type = list(object({
    device_name           = string
    volume_type           = string
    volume_size           = number
    delete_on_termination = bool
    encrypted             = bool
  }))
  default = []
}

variable "dev_management_server_avm150_new_user_data" {
  description = "User data script for management server AVM150 new"
  type        = string
  default     = null
}

# Variables for dev-management-server-avm150-imperva
variable "dev_management_server_avm150_imperva_name" {
  description = "Name of the management server AVM150 imperva instance"
  type        = string
}

variable "dev_management_server_avm150_imperva_ami_id" {
  description = "AMI ID for management server AVM150 imperva"
  type        = string
}

variable "dev_management_server_avm150_imperva_instance_type" {
  description = "Instance type for management server AVM150 imperva"
  type        = string
}

variable "dev_management_server_avm150_imperva_key_name" {
  description = "Key pair name for management server AVM150 imperva"
  type        = string
  default     = null
}

variable "dev_management_server_avm150_imperva_security_group_ids" {
  description = "Security group IDs for management server AVM150 imperva"
  type        = list(string)
}

variable "dev_management_server_avm150_imperva_subnet_id" {
  description = "Subnet ID for management server AVM150 imperva"
  type        = string
}

variable "dev_management_server_avm150_imperva_iam_instance_profile_name" {
  description = "IAM instance profile name for management server AVM150 imperva"
  type        = string
  default     = null
}

variable "dev_management_server_avm150_imperva_availability_zone" {
  description = "Availability zone for management server AVM150 imperva"
  type        = string
}

variable "dev_management_server_avm150_imperva_monitoring_enabled" {
  description = "Enable monitoring for management server AVM150 imperva"
  type        = bool
  default     = false
}

variable "dev_management_server_avm150_imperva_ebs_optimized" {
  description = "Enable EBS optimization for management server AVM150 imperva"
  type        = bool
  default     = false
}

variable "dev_management_server_avm150_imperva_source_dest_check" {
  description = "Enable source destination check for management server AVM150 imperva"
  type        = bool
  default     = true
}

variable "dev_management_server_avm150_imperva_private_ip_address" {
  description = "Private IP address for management server AVM150 imperva"
  type        = string
  default     = null
}

variable "dev_management_server_avm150_imperva_root_block_device" {
  description = "Root block device configuration for management server AVM150 imperva"
  type = object({
    volume_type           = string
    volume_size           = number
    delete_on_termination = bool
    encrypted             = bool
  })
  default = null
}

variable "dev_management_server_avm150_imperva_ebs_block_devices" {
  description = "Additional EBS block devices for management server AVM150 imperva"
  type = list(object({
    device_name           = string
    volume_type           = string
    volume_size           = number
    delete_on_termination = bool
    encrypted             = bool
  }))
  default = []
}

variable "dev_management_server_avm150_imperva_user_data" {
  description = "User data script for management server AVM150 imperva"
  type        = string
  default     = null
}
