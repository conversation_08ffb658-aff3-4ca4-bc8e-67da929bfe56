# InfoVault DEV Environment Infrastructure
# Use Case 4: Setup / Manage AWS Infrastructure (DEV)

# Local values for resource naming and configuration
locals {
  name_prefix = "${var.project_name}-${var.environment}"

  # Merge common tags with environment-specific tags
  common_tags = merge(var.common_tags, {
    Environment = var.environment
    Region      = var.aws_region
    Terraform   = "true"
  })
}

# =============================================================================
# NETWORKING INFRASTRUCTURE
# =============================================================================

# Intranet Management Compartment VPC
module "intranet_management" {
  source = "../../stacks/networking/intranet_management"

  # VPC Configuration
  vpc_cidr                             = var.intranet_management.vpc_cidr
  vpc_name                             = var.intranet_management.vpc_name
  enable_dns_hostnames                 = var.intranet_management.enable_dns_hostnames
  enable_dns_support                   = var.intranet_management.enable_dns_support
  enable_network_address_usage_metrics = var.intranet_management.enable_network_address_usage_metrics
  instance_tenancy                     = var.intranet_management.instance_tenancy
  create_internet_gateway              = var.intranet_management.create_internet_gateway

  # Subnets
  public_subnets  = var.intranet_management.public_subnets
  private_subnets = var.intranet_management.private_subnets

  # NAT Gateways
  nat_gateways = var.intranet_management.nat_gateways

  # Route Tables
  public_route_tables  = var.intranet_management.public_route_tables
  private_route_tables = var.intranet_management.private_route_tables

  # Route Table Associations
  public_subnet_route_associations  = var.intranet_management.public_subnet_route_associations
  private_subnet_route_associations = var.intranet_management.private_subnet_route_associations

  # Routes
  private_nat_routes = var.intranet_management.private_nat_routes
  vpc_peering_routes = var.intranet_management.vpc_peering_routes

  # Security Groups
  security_groups              = var.intranet_management.security_groups
  security_group_ingress_rules = var.intranet_management.security_group_ingress_rules
  security_group_egress_rules  = var.intranet_management.security_group_egress_rules

  # Network ACLs
  network_acls              = var.intranet_management.network_acls
  network_acl_ingress_rules = var.intranet_management.network_acl_ingress_rules
  network_acl_egress_rules  = var.intranet_management.network_acl_egress_rules

  # VPC Endpoints
  vpc_endpoints = var.intranet_management.vpc_endpoints

  # Common Tags
  common_tags = local.common_tags
}

# Internet Facing Compartment VPC
module "internet_facing" {
  source = "../../stacks/networking/Internet-facing-compartment"

  environment     = var.environment
  common_tags     = local.common_tags
  internet_facing = var.internet_facing
}

# Gen Facing Compartment VPC
module "gen_facing" {
  source = "../../stacks/networking/gen-facing-compartment"

  environment = var.environment
  common_tags = local.common_tags
  gen_facing  = var.gen_facing
}

# ABLRHF Compartment VPC
module "ablrhf" {
  source = "../../stacks/networking/ablrhf-compartment"

  ablrhf      = var.ablrhf
  environment = var.environment
  common_tags = local.common_tags
}

# Patching Compartment VPC
module "patching" {
  source = "../../stacks/networking/Patching-Compartment"

  patching    = var.patching
  environment = var.environment
  common_tags = local.common_tags
}

# =============================================================================
# SECURITY INFRASTRUCTURE
# =============================================================================

# Internet Facing Security Groups
module "internet_facing_security_groups" {
  source = "../../stacks/security/security_groups"

  vpc_id                           = module.internet_facing.vpc_id
  environment                      = var.environment
  internet_facing_security_groups  = var.internet_facing_security_groups
  internet_facing_sg_ingress_rules = var.internet_facing_sg_ingress_rules
  internet_facing_sg_egress_rules  = var.internet_facing_sg_egress_rules
  common_tags                      = local.common_tags

  depends_on = [module.internet_facing]
}

# =============================================================================
# COMPUTE INFRASTRUCTURE
# =============================================================================

# EKS Cluster
module "eks" {
  source = "../../stacks/compute/eks"

  environment = var.environment
  region      = var.aws_region
  common_tags = local.common_tags

  # EKS Cluster Configuration
  cluster_name               = var.cluster_name
  cluster_version            = var.cluster_version
  cluster_role_name          = var.cluster_role_name
  node_group_role_name       = var.node_group_role_name
  subnet_ids                 = var.subnet_ids
  cluster_security_group_ids = var.cluster_security_group_ids
  endpoint_private_access    = var.endpoint_private_access
  endpoint_public_access     = var.endpoint_public_access
  public_access_cidrs        = var.public_access_cidrs
  cluster_log_types          = var.cluster_log_types
  kms_key_arn                = var.kms_key_arn

  # Node Group Configuration
  node_group_name           = var.node_group_name
  node_group_subnet_ids     = var.node_group_subnet_ids
  node_group_instance_types = var.node_group_instance_types
  node_group_ami_type       = var.node_group_ami_type
  node_group_capacity_type  = var.node_group_capacity_type
  node_group_disk_size      = var.node_group_disk_size
  node_group_desired_size   = var.node_group_desired_size
  node_group_max_size       = var.node_group_max_size
  node_group_min_size       = var.node_group_min_size
  node_group_labels         = var.node_group_labels

  # EKS Addon Versions
  addon_amazon_cloudwatch_observability_version = var.addon_amazon_cloudwatch_observability_version
  addon_aws_ebs_csi_driver_version              = var.addon_aws_ebs_csi_driver_version
  addon_aws_guardduty_agent_version             = var.addon_aws_guardduty_agent_version
  addon_coredns_version                         = var.addon_coredns_version
  addon_eks_pod_identity_agent_version          = var.addon_eks_pod_identity_agent_version
  addon_kube_proxy_version                      = var.addon_kube_proxy_version
  addon_vpc_cni_version                         = var.addon_vpc_cni_version

  depends_on = [module.gen_facing]
}

# EC2 Instances Stack
module "ec2_instances" {
  source = "../../stacks/compute/ec2"

  # GitLab Runner Configuration
  infovault_dev_gitlab_runner_name                      = var.infovault_dev_gitlab_runner_name
  infovault_dev_gitlab_runner_ami_id                    = var.infovault_dev_gitlab_runner_ami_id
  infovault_dev_gitlab_runner_instance_type             = var.infovault_dev_gitlab_runner_instance_type
  infovault_dev_gitlab_runner_key_name                  = var.infovault_dev_gitlab_runner_key_name
  infovault_dev_gitlab_runner_security_group_ids        = var.infovault_dev_gitlab_runner_security_group_ids
  infovault_dev_gitlab_runner_subnet_id                 = var.infovault_dev_gitlab_runner_subnet_id
  infovault_dev_gitlab_runner_iam_instance_profile_name = var.infovault_dev_gitlab_runner_iam_instance_profile_name
  infovault_dev_gitlab_runner_availability_zone         = var.infovault_dev_gitlab_runner_availability_zone
  infovault_dev_gitlab_runner_monitoring_enabled        = var.infovault_dev_gitlab_runner_monitoring_enabled
  infovault_dev_gitlab_runner_ebs_optimized             = var.infovault_dev_gitlab_runner_ebs_optimized
  infovault_dev_gitlab_runner_source_dest_check         = var.infovault_dev_gitlab_runner_source_dest_check
  infovault_dev_gitlab_runner_private_ip_address        = var.infovault_dev_gitlab_runner_private_ip_address
  infovault_dev_gitlab_runner_root_block_device         = var.infovault_dev_gitlab_runner_root_block_device
  infovault_dev_gitlab_runner_ebs_block_devices         = var.infovault_dev_gitlab_runner_ebs_block_devices
  infovault_dev_gitlab_runner_user_data                 = var.infovault_dev_gitlab_runner_user_data

  # Linux Tooling Server Configuration
  infovault_dev_linux_tooling_server_name                      = var.infovault_dev_linux_tooling_server_name
  infovault_dev_linux_tooling_server_ami_id                    = var.infovault_dev_linux_tooling_server_ami_id
  infovault_dev_linux_tooling_server_instance_type             = var.infovault_dev_linux_tooling_server_instance_type
  infovault_dev_linux_tooling_server_key_name                  = var.infovault_dev_linux_tooling_server_key_name
  infovault_dev_linux_tooling_server_security_group_ids        = var.infovault_dev_linux_tooling_server_security_group_ids
  infovault_dev_linux_tooling_server_subnet_id                 = var.infovault_dev_linux_tooling_server_subnet_id
  infovault_dev_linux_tooling_server_iam_instance_profile_name = var.infovault_dev_linux_tooling_server_iam_instance_profile_name
  infovault_dev_linux_tooling_server_availability_zone         = var.infovault_dev_linux_tooling_server_availability_zone
  infovault_dev_linux_tooling_server_monitoring_enabled        = var.infovault_dev_linux_tooling_server_monitoring_enabled
  infovault_dev_linux_tooling_server_ebs_optimized             = var.infovault_dev_linux_tooling_server_ebs_optimized
  infovault_dev_linux_tooling_server_source_dest_check         = var.infovault_dev_linux_tooling_server_source_dest_check
  infovault_dev_linux_tooling_server_private_ip_address        = var.infovault_dev_linux_tooling_server_private_ip_address
  infovault_dev_linux_tooling_server_root_block_device         = var.infovault_dev_linux_tooling_server_root_block_device
  infovault_dev_linux_tooling_server_ebs_block_devices         = var.infovault_dev_linux_tooling_server_ebs_block_devices
  infovault_dev_linux_tooling_server_user_data                 = var.infovault_dev_linux_tooling_server_user_data

  # Windows Tooling Server Configuration
  mgmt_newgenadm_win_tooling_01_name                      = var.mgmt_newgenadm_win_tooling_01_name
  mgmt_newgenadm_win_tooling_01_ami_id                    = var.mgmt_newgenadm_win_tooling_01_ami_id
  mgmt_newgenadm_win_tooling_01_instance_type             = var.mgmt_newgenadm_win_tooling_01_instance_type
  mgmt_newgenadm_win_tooling_01_key_name                  = var.mgmt_newgenadm_win_tooling_01_key_name
  mgmt_newgenadm_win_tooling_01_security_group_ids        = var.mgmt_newgenadm_win_tooling_01_security_group_ids
  mgmt_newgenadm_win_tooling_01_subnet_id                 = var.mgmt_newgenadm_win_tooling_01_subnet_id
  mgmt_newgenadm_win_tooling_01_iam_instance_profile_name = var.mgmt_newgenadm_win_tooling_01_iam_instance_profile_name
  mgmt_newgenadm_win_tooling_01_availability_zone         = var.mgmt_newgenadm_win_tooling_01_availability_zone
  mgmt_newgenadm_win_tooling_01_monitoring_enabled        = var.mgmt_newgenadm_win_tooling_01_monitoring_enabled
  mgmt_newgenadm_win_tooling_01_ebs_optimized             = var.mgmt_newgenadm_win_tooling_01_ebs_optimized
  mgmt_newgenadm_win_tooling_01_source_dest_check         = var.mgmt_newgenadm_win_tooling_01_source_dest_check
  mgmt_newgenadm_win_tooling_01_private_ip_address        = var.mgmt_newgenadm_win_tooling_01_private_ip_address
  mgmt_newgenadm_win_tooling_01_root_block_device         = var.mgmt_newgenadm_win_tooling_01_root_block_device
  mgmt_newgenadm_win_tooling_01_ebs_block_devices         = var.mgmt_newgenadm_win_tooling_01_ebs_block_devices
  mgmt_newgenadm_win_tooling_01_user_data                 = var.mgmt_newgenadm_win_tooling_01_user_data

  # DRA Admin Server Configuration
  dev_dra_admin_server_new_name                      = var.dev_dra_admin_server_new_name
  dev_dra_admin_server_new_ami_id                    = var.dev_dra_admin_server_new_ami_id
  dev_dra_admin_server_new_instance_type             = var.dev_dra_admin_server_new_instance_type
  dev_dra_admin_server_new_key_name                  = var.dev_dra_admin_server_new_key_name
  dev_dra_admin_server_new_security_group_ids        = var.dev_dra_admin_server_new_security_group_ids
  dev_dra_admin_server_new_subnet_id                 = var.dev_dra_admin_server_new_subnet_id
  dev_dra_admin_server_new_iam_instance_profile_name = var.dev_dra_admin_server_new_iam_instance_profile_name
  dev_dra_admin_server_new_availability_zone         = var.dev_dra_admin_server_new_availability_zone
  dev_dra_admin_server_new_monitoring_enabled        = var.dev_dra_admin_server_new_monitoring_enabled
  dev_dra_admin_server_new_ebs_optimized             = var.dev_dra_admin_server_new_ebs_optimized
  dev_dra_admin_server_new_source_dest_check         = var.dev_dra_admin_server_new_source_dest_check
  dev_dra_admin_server_new_private_ip_address        = var.dev_dra_admin_server_new_private_ip_address
  dev_dra_admin_server_new_root_block_device         = var.dev_dra_admin_server_new_root_block_device
  dev_dra_admin_server_new_ebs_block_devices         = var.dev_dra_admin_server_new_ebs_block_devices
  dev_dra_admin_server_new_user_data                 = var.dev_dra_admin_server_new_user_data

  # DRA Analytics Server Configuration
  dev_dra_analytics_server_new_name                      = var.dev_dra_analytics_server_new_name
  dev_dra_analytics_server_new_ami_id                    = var.dev_dra_analytics_server_new_ami_id
  dev_dra_analytics_server_new_instance_type             = var.dev_dra_analytics_server_new_instance_type
  dev_dra_analytics_server_new_key_name                  = var.dev_dra_analytics_server_new_key_name
  dev_dra_analytics_server_new_security_group_ids        = var.dev_dra_analytics_server_new_security_group_ids
  dev_dra_analytics_server_new_subnet_id                 = var.dev_dra_analytics_server_new_subnet_id
  dev_dra_analytics_server_new_iam_instance_profile_name = var.dev_dra_analytics_server_new_iam_instance_profile_name
  dev_dra_analytics_server_new_availability_zone         = var.dev_dra_analytics_server_new_availability_zone
  dev_dra_analytics_server_new_monitoring_enabled        = var.dev_dra_analytics_server_new_monitoring_enabled
  dev_dra_analytics_server_new_ebs_optimized             = var.dev_dra_analytics_server_new_ebs_optimized
  dev_dra_analytics_server_new_source_dest_check         = var.dev_dra_analytics_server_new_source_dest_check
  dev_dra_analytics_server_new_private_ip_address        = var.dev_dra_analytics_server_new_private_ip_address
  dev_dra_analytics_server_new_root_block_device         = var.dev_dra_analytics_server_new_root_block_device
  dev_dra_analytics_server_new_ebs_block_devices         = var.dev_dra_analytics_server_new_ebs_block_devices
  dev_dra_analytics_server_new_user_data                 = var.dev_dra_analytics_server_new_user_data

  # AD Tooling Server Configuration
  ad_tooling_windows_02_name                      = var.ad_tooling_windows_02_name
  ad_tooling_windows_02_ami_id                    = var.ad_tooling_windows_02_ami_id
  ad_tooling_windows_02_instance_type             = var.ad_tooling_windows_02_instance_type
  ad_tooling_windows_02_key_name                  = var.ad_tooling_windows_02_key_name
  ad_tooling_windows_02_security_group_ids        = var.ad_tooling_windows_02_security_group_ids
  ad_tooling_windows_02_subnet_id                 = var.ad_tooling_windows_02_subnet_id
  ad_tooling_windows_02_iam_instance_profile_name = var.ad_tooling_windows_02_iam_instance_profile_name
  ad_tooling_windows_02_availability_zone         = var.ad_tooling_windows_02_availability_zone
  ad_tooling_windows_02_monitoring_enabled        = var.ad_tooling_windows_02_monitoring_enabled
  ad_tooling_windows_02_ebs_optimized             = var.ad_tooling_windows_02_ebs_optimized
  ad_tooling_windows_02_source_dest_check         = var.ad_tooling_windows_02_source_dest_check
  ad_tooling_windows_02_private_ip_address        = var.ad_tooling_windows_02_private_ip_address
  ad_tooling_windows_02_root_block_device         = var.ad_tooling_windows_02_root_block_device
  ad_tooling_windows_02_ebs_block_devices         = var.ad_tooling_windows_02_ebs_block_devices
  ad_tooling_windows_02_user_data                 = var.ad_tooling_windows_02_user_data

  # Marvin AI 02 Configuration
  infovault_dev_marvin_ai_02_name                      = var.infovault_dev_marvin_ai_02_name
  infovault_dev_marvin_ai_02_ami_id                    = var.infovault_dev_marvin_ai_02_ami_id
  infovault_dev_marvin_ai_02_instance_type             = var.infovault_dev_marvin_ai_02_instance_type
  infovault_dev_marvin_ai_02_key_name                  = var.infovault_dev_marvin_ai_02_key_name
  infovault_dev_marvin_ai_02_security_group_ids        = var.infovault_dev_marvin_ai_02_security_group_ids
  infovault_dev_marvin_ai_02_subnet_id                 = var.infovault_dev_marvin_ai_02_subnet_id
  infovault_dev_marvin_ai_02_iam_instance_profile_name = var.infovault_dev_marvin_ai_02_iam_instance_profile_name
  infovault_dev_marvin_ai_02_availability_zone         = var.infovault_dev_marvin_ai_02_availability_zone
  infovault_dev_marvin_ai_02_monitoring_enabled        = var.infovault_dev_marvin_ai_02_monitoring_enabled
  infovault_dev_marvin_ai_02_ebs_optimized             = var.infovault_dev_marvin_ai_02_ebs_optimized
  infovault_dev_marvin_ai_02_source_dest_check         = var.infovault_dev_marvin_ai_02_source_dest_check
  infovault_dev_marvin_ai_02_private_ip_address        = var.infovault_dev_marvin_ai_02_private_ip_address
  infovault_dev_marvin_ai_02_root_block_device         = var.infovault_dev_marvin_ai_02_root_block_device
  infovault_dev_marvin_ai_02_ebs_block_devices         = var.infovault_dev_marvin_ai_02_ebs_block_devices
  infovault_dev_marvin_ai_02_user_data                 = var.infovault_dev_marvin_ai_02_user_data

  # Management Server AVM150 New Configuration
  dev_management_server_avm150_new_name                      = var.dev_management_server_avm150_new_name
  dev_management_server_avm150_new_ami_id                    = var.dev_management_server_avm150_new_ami_id
  dev_management_server_avm150_new_instance_type             = var.dev_management_server_avm150_new_instance_type
  dev_management_server_avm150_new_key_name                  = var.dev_management_server_avm150_new_key_name
  dev_management_server_avm150_new_security_group_ids        = var.dev_management_server_avm150_new_security_group_ids
  dev_management_server_avm150_new_subnet_id                 = var.dev_management_server_avm150_new_subnet_id
  dev_management_server_avm150_new_iam_instance_profile_name = var.dev_management_server_avm150_new_iam_instance_profile_name
  dev_management_server_avm150_new_availability_zone         = var.dev_management_server_avm150_new_availability_zone
  dev_management_server_avm150_new_monitoring_enabled        = var.dev_management_server_avm150_new_monitoring_enabled
  dev_management_server_avm150_new_ebs_optimized             = var.dev_management_server_avm150_new_ebs_optimized
  dev_management_server_avm150_new_source_dest_check         = var.dev_management_server_avm150_new_source_dest_check
  dev_management_server_avm150_new_private_ip_address        = var.dev_management_server_avm150_new_private_ip_address
  dev_management_server_avm150_new_root_block_device         = var.dev_management_server_avm150_new_root_block_device
  dev_management_server_avm150_new_ebs_block_devices         = var.dev_management_server_avm150_new_ebs_block_devices
  dev_management_server_avm150_new_user_data                 = var.dev_management_server_avm150_new_user_data

  # Management Server AVM150 Imperva Configuration
  dev_management_server_avm150_imperva_name                      = var.dev_management_server_avm150_imperva_name
  dev_management_server_avm150_imperva_ami_id                    = var.dev_management_server_avm150_imperva_ami_id
  dev_management_server_avm150_imperva_instance_type             = var.dev_management_server_avm150_imperva_instance_type
  dev_management_server_avm150_imperva_key_name                  = var.dev_management_server_avm150_imperva_key_name
  dev_management_server_avm150_imperva_security_group_ids        = var.dev_management_server_avm150_imperva_security_group_ids
  dev_management_server_avm150_imperva_subnet_id                 = var.dev_management_server_avm150_imperva_subnet_id
  dev_management_server_avm150_imperva_iam_instance_profile_name = var.dev_management_server_avm150_imperva_iam_instance_profile_name
  dev_management_server_avm150_imperva_availability_zone         = var.dev_management_server_avm150_imperva_availability_zone
  dev_management_server_avm150_imperva_monitoring_enabled        = var.dev_management_server_avm150_imperva_monitoring_enabled
  dev_management_server_avm150_imperva_ebs_optimized             = var.dev_management_server_avm150_imperva_ebs_optimized
  dev_management_server_avm150_imperva_source_dest_check         = var.dev_management_server_avm150_imperva_source_dest_check
  dev_management_server_avm150_imperva_private_ip_address        = var.dev_management_server_avm150_imperva_private_ip_address
  dev_management_server_avm150_imperva_root_block_device         = var.dev_management_server_avm150_imperva_root_block_device
  dev_management_server_avm150_imperva_ebs_block_devices         = var.dev_management_server_avm150_imperva_ebs_block_devices
  dev_management_server_avm150_imperva_user_data                 = var.dev_management_server_avm150_imperva_user_data

  # Tags
  common_tags = local.common_tags

  depends_on = [module.intranet_management]
}



# =============================================================================
# STORAGE INFRASTRUCTURE
# =============================================================================

# S3 Bucket for InfoVault Application Data
module "app_data_bucket" {
  source = "../../modules/s3"

  # Bucket Configuration
  bucket_name    = var.s3_bucket_name
  name_prefix    = "${local.name_prefix}-storage"
  bucket_purpose = "InfoVault Application Data Storage"
  force_destroy  = var.s3_force_destroy

  # Versioning
  versioning_enabled = var.enable_s3_versioning

  # Encryption
  encryption_algorithm = var.s3_encryption_algorithm
  kms_key_id           = var.s3_kms_key_id
  bucket_key_enabled   = var.s3_bucket_key_enabled

  # Public Access Block (Security)
  block_public_acls       = var.s3_block_public_acls
  block_public_policy     = var.s3_block_public_policy
  ignore_public_acls      = var.s3_ignore_public_acls
  restrict_public_buckets = var.s3_restrict_public_buckets

  # Lifecycle Management
  lifecycle_enabled          = var.s3_lifecycle_enabled
  transition_to_ia_days      = var.s3_transition_to_ia_days
  transition_to_glacier_days = var.s3_transition_to_glacier_days
  expiration_days            = var.s3_expiration_days
  multipart_upload_days      = var.s3_multipart_upload_days

  # Logging
  logging_enabled       = var.s3_logging_enabled
  logging_target_bucket = var.s3_logging_target_bucket
  logging_target_prefix = var.s3_logging_target_prefix

  # Notifications
  notification_enabled = var.s3_notification_enabled

  # Tags
  common_tags = local.common_tags
}
