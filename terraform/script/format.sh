#!/bin/bash

# Terraform Format Script
# This script formats all terraform files according to InfoVault coding standards

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    case $status in
        "SUCCESS")
            echo -e "${GREEN}✓ $message${NC}"
            ;;
        "ERROR")
            echo -e "${RED}✗ $message${NC}"
            ;;
        "WARNING")
            echo -e "${YELLOW}⚠ $message${NC}"
            ;;
        "INFO")
            echo -e "${YELLOW}ℹ $message${NC}"
            ;;
    esac
}

# Function to format terraform files
format_terraform() {
    print_status "INFO" "Formatting Terraform files..."
    
    local files_formatted=0
    
    # Find all directories with .tf files and format them
    while IFS= read -r -d '' dir; do
        if [[ -f "$dir/main.tf" || -f "$dir/variables.tf" || -f "$dir/outputs.tf" ]]; then
            print_status "INFO" "Formatting directory: $dir"
            
            # Change to directory and format
            (
                cd "$dir"
                if terraform fmt -write=true -diff=true; then
                    files_formatted=$((files_formatted + 1))
                fi
            )
        fi
    done < <(find terraform/ -type d -print0)
    
    print_status "SUCCESS" "Formatted $files_formatted directories"
}

# Function to check formatting
check_formatting() {
    print_status "INFO" "Checking Terraform formatting..."
    
    local formatting_issues=false
    
    # Find all directories with .tf files and check formatting
    while IFS= read -r -d '' dir; do
        if [[ -f "$dir/main.tf" || -f "$dir/variables.tf" || -f "$dir/outputs.tf" ]]; then
            # Change to directory and check formatting
            (
                cd "$dir"
                if ! terraform fmt -check=true -diff=true; then
                    print_status "ERROR" "Formatting issues found in $dir"
                    formatting_issues=true
                fi
            )
        fi
    done < <(find terraform/ -type d -print0)
    
    if [ "$formatting_issues" = true ]; then
        print_status "ERROR" "Formatting check failed"
        return 1
    else
        print_status "SUCCESS" "All files are properly formatted"
        return 0
    fi
}

# Main function
main() {
    local action=${1:-"format"}
    
    case $action in
        "format")
            print_status "INFO" "Starting Terraform formatting..."
            format_terraform
            ;;
        "check")
            print_status "INFO" "Starting Terraform format check..."
            check_formatting
            ;;
        *)
            echo "Usage: $0 [format|check]"
            echo "  format: Format all terraform files (default)"
            echo "  check:  Check if all files are properly formatted"
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
