#!/bin/bash

# Terraform Validation Script
# This script validates terraform configurations according to InfoVault coding patterns

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    case $status in
        "SUCCESS")
            echo -e "${GREEN}✓ $message${NC}"
            ;;
        "ERROR")
            echo -e "${RED}✗ $message${NC}"
            ;;
        "WARNING")
            echo -e "${YELLOW}⚠ $message${NC}"
            ;;
        "INFO")
            echo -e "${YELLOW}ℹ $message${NC}"
            ;;
    esac
}

# Function to validate terraform syntax
validate_terraform_syntax() {
    print_status "INFO" "Validating Terraform syntax..."
    
    local validation_failed=false
    
    # Find all directories with .tf files
    while IFS= read -r -d '' dir; do
        if [[ -f "$dir/main.tf" || -f "$dir/variables.tf" || -f "$dir/outputs.tf" ]]; then
            print_status "INFO" "Validating directory: $dir"
            
            # Change to directory and validate
            (
                cd "$dir"
                if ! terraform fmt -check=true -diff=true; then
                    print_status "ERROR" "Terraform formatting issues found in $dir"
                    validation_failed=true
                fi
                
                if ! terraform validate; then
                    print_status "ERROR" "Terraform validation failed in $dir"
                    validation_failed=true
                fi
            )
        fi
    done < <(find . -type d -print0)
    
    if [ "$validation_failed" = true ]; then
        print_status "ERROR" "Terraform syntax validation failed"
        return 1
    else
        print_status "SUCCESS" "Terraform syntax validation passed"
        return 0
    fi
}

# Function to validate coding patterns
validate_coding_patterns() {
    print_status "INFO" "Validating coding patterns..."
    
    local pattern_violations=false
    
    # Check if environments call stacks, not modules directly
    if grep -r "source.*modules" terraform/environments/ 2>/dev/null; then
        print_status "ERROR" "Environments should call stacks, not modules directly"
        pattern_violations=true
    fi
    
    # Check if stacks call modules, not other stacks
    if grep -r "source.*stacks" terraform/stacks/ 2>/dev/null; then
        print_status "ERROR" "Stacks should call modules, not other stacks"
        pattern_violations=true
    fi
    
    # Check for proper variable naming conventions
    if grep -r "variable.*[A-Z]" terraform/ 2>/dev/null | grep -v "# Variables for"; then
        print_status "WARNING" "Variable names should use snake_case"
    fi
    
    # Check for proper resource naming
    if grep -r "resource.*[A-Z]" terraform/ 2>/dev/null; then
        print_status "WARNING" "Resource names should use snake_case"
    fi
    
    if [ "$pattern_violations" = true ]; then
        print_status "ERROR" "Coding pattern validation failed"
        return 1
    else
        print_status "SUCCESS" "Coding pattern validation passed"
        return 0
    fi
}

# Function to validate file structure
validate_file_structure() {
    print_status "INFO" "Validating file structure..."
    
    local structure_issues=false
    
    # Check required directories exist
    required_dirs=("terraform/modules" "terraform/stacks" "terraform/environments" "terraform/script")
    for dir in "${required_dirs[@]}"; do
        if [[ ! -d "$dir" ]]; then
            print_status "ERROR" "Required directory missing: $dir"
            structure_issues=true
        fi
    done
    
    # Check that each terraform directory has required files
    while IFS= read -r -d '' dir; do
        if [[ -f "$dir/main.tf" ]]; then
            # Check for variables.tf if variables are used
            if grep -q "var\." "$dir/main.tf" && [[ ! -f "$dir/variables.tf" ]]; then
                print_status "ERROR" "Missing variables.tf in $dir (variables are referenced in main.tf)"
                structure_issues=true
            fi
        fi
    done < <(find terraform/ -type d -print0)
    
    if [ "$structure_issues" = true ]; then
        print_status "ERROR" "File structure validation failed"
        return 1
    else
        print_status "SUCCESS" "File structure validation passed"
        return 0
    fi
}

# Main validation function
main() {
    print_status "INFO" "Starting Terraform validation..."
    
    local overall_status=0
    
    # Run all validations
    validate_file_structure || overall_status=1
    validate_coding_patterns || overall_status=1
    validate_terraform_syntax || overall_status=1
    
    if [ $overall_status -eq 0 ]; then
        print_status "SUCCESS" "All validations passed!"
    else
        print_status "ERROR" "Some validations failed!"
    fi
    
    return $overall_status
}

# Run main function
main "$@"
