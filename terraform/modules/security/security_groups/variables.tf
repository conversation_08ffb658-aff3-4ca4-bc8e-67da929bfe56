# Security Groups Module Variables
# InfoVault Security Groups Configuration

variable "vpc_id" {
  description = "VPC ID where security groups will be created"
  type        = string
}

variable "environment" {
  description = "Environment name (dev, staging, prod)"
  type        = string
}

variable "security_groups" {
  description = "Map of security groups to create"
  type = map(object({
    name        = string
    description = string
  }))
  default = {}
}

variable "security_group_ingress_rules" {
  description = "Map of ingress rules for security groups"
  type = map(object({
    security_group_key       = string
    from_port                = number
    to_port                  = number
    protocol                 = string
    cidr_blocks              = optional(list(string))
    source_security_group_id = optional(string)
    description              = string
  }))
  default = {}
}

variable "security_group_egress_rules" {
  description = "Map of egress rules for security groups"
  type = map(object({
    security_group_key            = string
    from_port                     = number
    to_port                       = number
    protocol                      = string
    cidr_blocks                   = optional(list(string))
    destination_security_group_id = optional(string)
    description                   = string
  }))
  default = {}
}

variable "common_tags" {
  description = "Common tags for all resources"
  type        = map(string)
  default     = {}
}
