# Security Groups Module
# InfoVault Security Groups Implementation

# Create Security Groups
resource "aws_security_group" "this" {
  for_each = var.security_groups

  name        = each.value.name
  description = each.value.description
  vpc_id      = var.vpc_id

  tags = merge(var.common_tags, {
    Name        = each.value.name
    Environment = var.environment
    Module      = "security_groups"
    Type        = "SecurityGroup"
  })

  lifecycle {
    create_before_destroy = true
  }
}

# Create Ingress Rules
resource "aws_vpc_security_group_ingress_rule" "this" {
  for_each = var.security_group_ingress_rules

  security_group_id = aws_security_group.this[each.value.security_group_key].id

  from_port   = each.value.from_port
  to_port     = each.value.to_port
  ip_protocol = each.value.protocol

  # Use CIDR blocks if provided, otherwise use source security group
  cidr_ipv4                    = each.value.cidr_blocks != null && length(each.value.cidr_blocks) > 0 ? each.value.cidr_blocks[0] : null
  referenced_security_group_id = each.value.source_security_group_id

  description = each.value.description

  tags = merge(var.common_tags, {
    Name        = "${each.key}-ingress"
    Environment = var.environment
    Module      = "security_groups"
    Type        = "IngressRule"
  })
}

# Create Egress Rules
resource "aws_vpc_security_group_egress_rule" "this" {
  for_each = var.security_group_egress_rules

  security_group_id = aws_security_group.this[each.value.security_group_key].id

  from_port   = each.value.from_port
  to_port     = each.value.to_port
  ip_protocol = each.value.protocol

  # Use CIDR blocks if provided, otherwise use destination security group
  cidr_ipv4                    = each.value.cidr_blocks != null && length(each.value.cidr_blocks) > 0 ? each.value.cidr_blocks[0] : null
  referenced_security_group_id = each.value.destination_security_group_id

  description = each.value.description

  tags = merge(var.common_tags, {
    Name        = "${each.key}-egress"
    Environment = var.environment
    Module      = "security_groups"
    Type        = "EgressRule"
  })
}
