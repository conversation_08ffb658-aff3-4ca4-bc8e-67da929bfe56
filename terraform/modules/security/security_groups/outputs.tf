# Security Groups Module Outputs
# InfoVault Security Groups Outputs

output "security_group_ids" {
  description = "Map of security group names to their IDs"
  value       = { for k, v in aws_security_group.this : k => v.id }
}

output "security_group_arns" {
  description = "Map of security group names to their ARNs"
  value       = { for k, v in aws_security_group.this : k => v.arn }
}

output "security_group_names" {
  description = "Map of security group keys to their names"
  value       = { for k, v in aws_security_group.this : k => v.name }
}

output "security_groups" {
  description = "Complete security group objects"
  value       = aws_security_group.this
}

output "ingress_rules" {
  description = "Created ingress rules"
  value       = aws_vpc_security_group_ingress_rule.this
}

output "egress_rules" {
  description = "Created egress rules"
  value       = aws_vpc_security_group_egress_rule.this
}
