# Generated by Terraformer for VPC vpc-0ca0480cce82946b3
# Source: /Users/<USER>/Documents/Projects/infovault-infrastructure/terraform/modules/networking/Patching-Compartment/raw_terraformer/aws/nat/nat_gateway.tf
# Generated on: 2025-06-09 13:16:58

resource "aws_nat_gateway" "tfer--nat-0431bfe8618055bd8" {
  allocation_id                      = "eipalloc-073d1259e09e3694d"
  connectivity_type                  = "public"
  private_ip                         = "**********"
  secondary_private_ip_address_count = "0"
  subnet_id                          = "subnet-043dd23a18b80a280"

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-gen-facing-compartment-nat-gateway"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-gen-facing-compartment-nat-gateway"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }
}

resource "aws_nat_gateway" "tfer--nat-0faca993cb72fc7d6" {
  allocation_id                      = "eipalloc-05b293b1c50d5ff91"
  connectivity_type                  = "public"
  private_ip                         = "**********"
  secondary_private_ip_address_count = "0"
  subnet_id                          = "subnet-01e871c49dc4b9b1f"

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "dev-net-natgw-az1"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "dev-net-natgw-az1"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }
}
