# Generated by Terraformer for VPC vpc-0ca0480cce82946b3
# Source: /Users/<USER>/Documents/Projects/infovault-infrastructure/terraform/modules/networking/Patching-Compartment/raw_terraformer/aws/igw/internet_gateway.tf
# Generated on: 2025-06-09 13:16:58

resource "aws_internet_gateway" "tfer--igw-00a312b81674ba618" {
  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "dev-aps1-igw-network-main"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "dev-aps1-igw-network-main"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_id = "vpc-037f619602fa293a1"
}

resource "aws_internet_gateway" "tfer--igw-05beaf9de3b1e3902" {
  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-igw"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-igw"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_id = "vpc-05e0e8104b3321c40"
}

resource "aws_internet_gateway" "tfer--igw-066231fa2c240e55d" {
  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-igw"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-igw"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_id = "vpc-05f1b20a6634c6a38"
}

resource "aws_internet_gateway" "tfer--igw-0b197ebf6057fbe90" {
  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-igw"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-igw"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_id = "vpc-0e75036a2ad9e5b4f"
}
