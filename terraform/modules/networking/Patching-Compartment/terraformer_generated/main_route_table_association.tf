# Generated by Terraformer for VPC vpc-0ca0480cce82946b3
# Source: /Users/<USER>/Documents/Projects/infovault-infrastructure/terraform/modules/networking/Patching-Compartment/raw_terraformer/aws/route_table/main_route_table_association.tf
# Generated on: 2025-06-09 13:16:58

resource "aws_main_route_table_association" "tfer--vpc-02282d8d4c9c7764c" {
  route_table_id = data.terraform_remote_state.route_table.outputs.aws_route_table_tfer--rtb-04e5bfdb40e860b95_id
  vpc_id         = "vpc-02282d8d4c9c7764c"
}

resource "aws_main_route_table_association" "tfer--vpc-037f619602fa293a1" {
  route_table_id = data.terraform_remote_state.route_table.outputs.aws_route_table_tfer--rtb-0de7189dd22690311_id
  vpc_id         = "vpc-037f619602fa293a1"
}

resource "aws_main_route_table_association" "tfer--vpc-05e0e8104b3321c40" {
  route_table_id = data.terraform_remote_state.route_table.outputs.aws_route_table_tfer--rtb-08edcd5b7304aa2aa_id
  vpc_id         = "vpc-05e0e8104b3321c40"
}

resource "aws_main_route_table_association" "tfer--vpc-05f1b20a6634c6a38" {
  route_table_id = data.terraform_remote_state.route_table.outputs.aws_route_table_tfer--rtb-02a850249ccfbe80b_id
  vpc_id         = "vpc-05f1b20a6634c6a38"
}

resource "aws_main_route_table_association" "tfer--vpc-0ca0480cce82946b3" {
  route_table_id = data.terraform_remote_state.route_table.outputs.aws_route_table_tfer--rtb-00c5fd2f2cd25c1f8_id
  vpc_id         = data.terraform_remote_state.vpc.outputs.aws_vpc_tfer--vpc-0ca0480cce82946b3_id
}

resource "aws_main_route_table_association" "tfer--vpc-0e75036a2ad9e5b4f" {
  route_table_id = data.terraform_remote_state.route_table.outputs.aws_route_table_tfer--rtb-0cb7cabf4d81ee7bd_id
  vpc_id         = "vpc-0e75036a2ad9e5b4f"
}
