# Generated by Terraformer for VPC vpc-0ca0480cce82946b3
# Source: /Users/<USER>/Documents/Projects/infovault-infrastructure/terraform/modules/networking/Patching-Compartment/raw_terraformer/aws/nacl/default_network_acl.tf
# Generated on: 2025-06-09 13:16:58

resource "aws_default_network_acl" "tfer--acl-0022470dc4a0a4d11" {
  egress {
    action     = "allow"
    cidr_block = "0.0.0.0/0"
    from_port  = "0"
    icmp_code  = "0"
    icmp_type  = "0"
    protocol   = "-1"
    rule_no    = "100"
    to_port    = "0"
  }

  ingress {
    action     = "allow"
    cidr_block = "0.0.0.0/0"
    from_port  = "0"
    icmp_code  = "0"
    icmp_type  = "0"
    protocol   = "-1"
    rule_no    = "100"
    to_port    = "0"
  }

  subnet_ids = ["${data.terraform_remote_state.subnet.outputs.aws_subnet_tfer--subnet-056cc33d6674e5027_id}", "${data.terraform_remote_state.subnet.outputs.aws_subnet_tfer--subnet-0669d84e56a7dc53e_id}", "${data.terraform_remote_state.subnet.outputs.aws_subnet_tfer--subnet-0b9afa4ae103de5b5_id}"]
}

resource "aws_default_network_acl" "tfer--acl-0228326a161d09c04" {
  egress {
    action     = "allow"
    cidr_block = "0.0.0.0/0"
    from_port  = "0"
    icmp_code  = "0"
    icmp_type  = "0"
    protocol   = "-1"
    rule_no    = "100"
    to_port    = "0"
  }

  ingress {
    action     = "allow"
    cidr_block = "0.0.0.0/0"
    from_port  = "0"
    icmp_code  = "0"
    icmp_type  = "0"
    protocol   = "-1"
    rule_no    = "100"
    to_port    = "0"
  }
}

resource "aws_default_network_acl" "tfer--acl-024c584c2c3382d5c" {
  egress {
    action     = "allow"
    cidr_block = "0.0.0.0/0"
    from_port  = "0"
    icmp_code  = "0"
    icmp_type  = "0"
    protocol   = "-1"
    rule_no    = "100"
    to_port    = "0"
  }

  ingress {
    action     = "allow"
    cidr_block = "0.0.0.0/0"
    from_port  = "0"
    icmp_code  = "0"
    icmp_type  = "0"
    protocol   = "-1"
    rule_no    = "100"
    to_port    = "0"
  }
}

resource "aws_default_network_acl" "tfer--acl-030537d8bab318bd1" {
  egress {
    action     = "allow"
    cidr_block = "0.0.0.0/0"
    from_port  = "0"
    icmp_code  = "0"
    icmp_type  = "0"
    protocol   = "-1"
    rule_no    = "100"
    to_port    = "0"
  }

  ingress {
    action     = "allow"
    cidr_block = "0.0.0.0/0"
    from_port  = "0"
    icmp_code  = "0"
    icmp_type  = "0"
    protocol   = "-1"
    rule_no    = "100"
    to_port    = "0"
  }

  subnet_ids = ["${data.terraform_remote_state.subnet.outputs.aws_subnet_tfer--subnet-04910ced717a5584a_id}", "${data.terraform_remote_state.subnet.outputs.aws_subnet_tfer--subnet-07ab4b059d69e4044_id}", "${data.terraform_remote_state.subnet.outputs.aws_subnet_tfer--subnet-0ebbc016c2fc6b3db_id}", "${data.terraform_remote_state.subnet.outputs.aws_subnet_tfer--subnet-0f80c3ddc6536fd2a_id}"]
}

resource "aws_default_network_acl" "tfer--acl-06b07cdd2cf4b4ab2" {
  egress {
    action     = "allow"
    cidr_block = "0.0.0.0/0"
    from_port  = "0"
    icmp_code  = "0"
    icmp_type  = "0"
    protocol   = "-1"
    rule_no    = "100"
    to_port    = "0"
  }

  ingress {
    action     = "allow"
    cidr_block = "0.0.0.0/0"
    from_port  = "0"
    icmp_code  = "0"
    icmp_type  = "0"
    protocol   = "-1"
    rule_no    = "100"
    to_port    = "0"
  }

  subnet_ids = ["${data.terraform_remote_state.subnet.outputs.aws_subnet_tfer--subnet-01e871c49dc4b9b1f_id}", "${data.terraform_remote_state.subnet.outputs.aws_subnet_tfer--subnet-0aec855b39a7be02e_id}"]
}

resource "aws_default_network_acl" "tfer--acl-0cf3a7e6c0d8d70ff" {
  egress {
    action     = "allow"
    cidr_block = "0.0.0.0/0"
    from_port  = "0"
    icmp_code  = "0"
    icmp_type  = "0"
    protocol   = "-1"
    rule_no    = "100"
    to_port    = "0"
  }

  ingress {
    action     = "allow"
    cidr_block = "0.0.0.0/0"
    from_port  = "0"
    icmp_code  = "0"
    icmp_type  = "0"
    protocol   = "-1"
    rule_no    = "100"
    to_port    = "0"
  }

  subnet_ids = ["${data.terraform_remote_state.subnet.outputs.aws_subnet_tfer--subnet-015e4b2acb87091d9_id}", "${data.terraform_remote_state.subnet.outputs.aws_subnet_tfer--subnet-023cafbedbb31d13e_id}", "${data.terraform_remote_state.subnet.outputs.aws_subnet_tfer--subnet-043dd23a18b80a280_id}", "${data.terraform_remote_state.subnet.outputs.aws_subnet_tfer--subnet-080d8fdb4e9add74c_id}", "${data.terraform_remote_state.subnet.outputs.aws_subnet_tfer--subnet-081b6eb841b4451b3_id}", "${data.terraform_remote_state.subnet.outputs.aws_subnet_tfer--subnet-0a1802eb99e96cbed_id}", "${data.terraform_remote_state.subnet.outputs.aws_subnet_tfer--subnet-0a5d4aa5489b9ab3a_id}", "${data.terraform_remote_state.subnet.outputs.aws_subnet_tfer--subnet-0c775a4f57dfa0e61_id}", "${data.terraform_remote_state.subnet.outputs.aws_subnet_tfer--subnet-0d4f42cf30b20c42d_id}", "${data.terraform_remote_state.subnet.outputs.aws_subnet_tfer--subnet-0e2a201f2503f59ee_id}"]
}
