data "terraform_remote_state" "route_table" {
  backend = "local"

  config = {
    path = "../../../../../../../../../../../../..//Users/<USER>/Documents/Projects/infovault-infrastructure/terraform/modules/networking/Patching-Compartment/raw_terraformer/aws/route_table/terraform.tfstate"
  }
}

data "terraform_remote_state" "subnet" {
  backend = "local"

  config = {
    path = "../../../../../../../../../../../../..//Users/<USER>/Documents/Projects/infovault-infrastructure/terraform/modules/networking/Patching-Compartment/raw_terraformer/aws/subnet/terraform.tfstate"
  }
}

data "terraform_remote_state" "vpc" {
  backend = "local"

  config = {
    path = "../../../../../../../../../../../../..//Users/<USER>/Documents/Projects/infovault-infrastructure/terraform/modules/networking/Patching-Compartment/raw_terraformer/aws/vpc/terraform.tfstate"
  }
}
