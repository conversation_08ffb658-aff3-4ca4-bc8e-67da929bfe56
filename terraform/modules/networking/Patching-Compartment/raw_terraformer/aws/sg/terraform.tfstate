{"version": 3, "terraform_version": "0.12.31", "serial": 1, "lineage": "5bc86153-51a8-ee7c-71eb-13a58fddfe6e", "modules": [{"path": ["root"], "outputs": {"aws_security_group_tfer--Database-SQL-infovault-gen-facing-compartment_sg-0d8a5fa845dca93c9_id": {"sensitive": false, "type": "string", "value": "sg-0d8a5fa845dca93c9"}, "aws_security_group_tfer--GuardDutyManagedSecurityGroup-vpc-037f619602fa293a1_sg-0455dd83ca1b155d3_id": {"sensitive": false, "type": "string", "value": "sg-0455dd83ca1b155d3"}, "aws_security_group_tfer--GuardDutyManagedSecurityGroup-vpc-05e0e8104b3321c40_sg-0650a95e2c0c1aaf5_id": {"sensitive": false, "type": "string", "value": "sg-0650a95e2c0c1aaf5"}, "aws_security_group_tfer--GuardDutyManagedSecurityGroup-vpc-05f1b20a6634c6a38_sg-065d1e24d43126489_id": {"sensitive": false, "type": "string", "value": "sg-065d1e24d43126489"}, "aws_security_group_tfer--GuardDutyManagedSecurityGroup-vpc-0ca0480cce82946b3_sg-0330a127b43c54ab2_id": {"sensitive": false, "type": "string", "value": "sg-0330a127b43c54ab2"}, "aws_security_group_tfer--InfoVault-MigrationServer-SG_sg-0c081d79227db7d86_id": {"sensitive": false, "type": "string", "value": "sg-0c081d79227db7d86"}, "aws_security_group_tfer--InfoVault-SPS2016-SG_sg-08b7a467b37acf0e7_id": {"sensitive": false, "type": "string", "value": "sg-08b7a467b37acf0e7"}, "aws_security_group_tfer--ablrhf-general_sg-0a6d2925236dcbd18_id": {"sensitive": false, "type": "string", "value": "sg-0a6d2925236dcbd18"}, "aws_security_group_tfer--ablrhf-vpce_sg-082d3fc2b26b1004e_id": {"sensitive": false, "type": "string", "value": "sg-082d3fc2b26b1004e"}, "aws_security_group_tfer--d-9667b2da13_controllers_sg-0f48a9bd414cfd781_id": {"sensitive": false, "type": "string", "value": "sg-0f48a9bd414cfd781"}, "aws_security_group_tfer--dam-agent-gateway-sg_sg-043af8ab2fe4ec50f_id": {"sensitive": false, "type": "string", "value": "sg-043af8ab2fe4ec50f"}, "aws_security_group_tfer--dam-gateway-dev-az1-sg_sg-0ebab82f4aa7f2a90_id": {"sensitive": false, "type": "string", "value": "sg-0ebab82f4aa7f2a90"}, "aws_security_group_tfer--dam-gateway-dev-az2-sg_sg-06546af93a8931ae6_id": {"sensitive": false, "type": "string", "value": "sg-06546af93a8931ae6"}, "aws_security_group_tfer--default_sg-010c8f879a2498c96_id": {"sensitive": false, "type": "string", "value": "sg-010c8f879a2498c96"}, "aws_security_group_tfer--default_sg-011238e3ae52fd306_id": {"sensitive": false, "type": "string", "value": "sg-011238e3ae52fd306"}, "aws_security_group_tfer--default_sg-019036bd03d215313_id": {"sensitive": false, "type": "string", "value": "sg-019036bd03d215313"}, "aws_security_group_tfer--default_sg-0a2302a73783eb575_id": {"sensitive": false, "type": "string", "value": "sg-0a2302a73783eb575"}, "aws_security_group_tfer--default_sg-0b4e61e6b8c88c982_id": {"sensitive": false, "type": "string", "value": "sg-0b4e61e6b8c88c982"}, "aws_security_group_tfer--default_sg-0b9bba8ae21bf0bbd_id": {"sensitive": false, "type": "string", "value": "sg-0b9bba8ae21bf0bbd"}, "aws_security_group_tfer--dev-dra-admin-server-sg_sg-0b67c6b7904908adf_id": {"sensitive": false, "type": "string", "value": "sg-0b67c6b7904908adf"}, "aws_security_group_tfer--dev-dra-analytics-server-sg_sg-0d7508facd515c5ea_id": {"sensitive": false, "type": "string", "value": "sg-0d7508facd515c5ea"}, "aws_security_group_tfer--dev-management-server-sg_sg-0f2fdd192120dbf17_id": {"sensitive": false, "type": "string", "value": "sg-0f2fdd192120dbf17"}, "aws_security_group_tfer--dra-agent-gateway-sg_sg-02794d7f8eaa42fea_id": {"sensitive": false, "type": "string", "value": "sg-02794d7f8eaa42fea"}, "aws_security_group_tfer--dra-gateway-dev-az1-sg_sg-03ee4d0a2a324d311_id": {"sensitive": false, "type": "string", "value": "sg-03ee4d0a2a324d311"}, "aws_security_group_tfer--dra-gateway-dev-az2-sg_sg-09b7938cd606d28b2_id": {"sensitive": false, "type": "string", "value": "sg-09b7938cd606d28b2"}, "aws_security_group_tfer--eks-cluster-sg-infovault-dev-eks-cluster-v130-**********_sg-0950397297ecb203f_id": {"sensitive": false, "type": "string", "value": "sg-0950397297ecb203f"}, "aws_security_group_tfer--gen-alb-internet-facing-sg_sg-0c08c4e2aabf88592_id": {"sensitive": false, "type": "string", "value": "sg-0c08c4e2aabf88592"}, "aws_security_group_tfer--infovault-ad-sg_sg-03f591c0a35d3af71_id": {"sensitive": false, "type": "string", "value": "sg-03f591c0a35d3af71"}, "aws_security_group_tfer--infovault-ad-tooling-sg_sg-0511f8964219f7537_id": {"sensitive": false, "type": "string", "value": "sg-0511f8964219f7537"}, "aws_security_group_tfer--infovault-alb-sg_sg-0d3cb3a1b9bea45d6_id": {"sensitive": false, "type": "string", "value": "sg-0d3cb3a1b9bea45d6"}, "aws_security_group_tfer--infovault-app-tier-sg_sg-095c31fa3b94c41ae_id": {"sensitive": false, "type": "string", "value": "sg-095c31fa3b94c41ae"}, "aws_security_group_tfer--infovault-dev-alb-sg_sg-0b9856c92519afb2f_id": {"sensitive": false, "type": "string", "value": "sg-0b9856c92519afb2f"}, "aws_security_group_tfer--infovault-dev-alb-sg_sg-0c2c1a0a80463a69b_id": {"sensitive": false, "type": "string", "value": "sg-0c2c1a0a80463a69b"}, "aws_security_group_tfer--infovault-dev-alb-sg_sg-0e4c62110e9593bfe_id": {"sensitive": false, "type": "string", "value": "sg-0e4c62110e9593bfe"}, "aws_security_group_tfer--infovault-dev-alb-sg_sg-0e921258047172c67_id": {"sensitive": false, "type": "string", "value": "sg-0e921258047172c67"}, "aws_security_group_tfer--infovault-dev-anfw-endpoint-sg_sg-0015e747db8058575_id": {"sensitive": false, "type": "string", "value": "sg-0015e747db8058575"}, "aws_security_group_tfer--infovault-dev-anfw-endpoint-sg_sg-02dba0def0203d5a0_id": {"sensitive": false, "type": "string", "value": "sg-02dba0def0203d5a0"}, "aws_security_group_tfer--infovault-dev-anfw-endpoint-sg_sg-038c81aff399e6abb_id": {"sensitive": false, "type": "string", "value": "sg-038c81aff399e6abb"}, "aws_security_group_tfer--infovault-dev-anfw-endpoint-sg_sg-0e2bbdad5eddfe99c_id": {"sensitive": false, "type": "string", "value": "sg-0e2bbdad5eddfe99c"}, "aws_security_group_tfer--infovault-dev-gen-facing-compartment-alb-sg_sg-0acb991f6917ba443_id": {"sensitive": false, "type": "string", "value": "sg-0acb991f6917ba443"}, "aws_security_group_tfer--infovault-dev-gen-facing-compartment-squid-sg_sg-011e29a277dde9f4a_id": {"sensitive": false, "type": "string", "value": "sg-011e29a277dde9f4a"}, "aws_security_group_tfer--infovault-dev-gen-facing-compartment-stfp-sg_sg-06936b8418430a397_id": {"sensitive": false, "type": "string", "value": "sg-06936b8418430a397"}, "aws_security_group_tfer--infovault-dev-gen-facing-compartment-vpce-sg_sg-066975c77a66a64d9_id": {"sensitive": false, "type": "string", "value": "sg-066975c77a66a64d9"}, "aws_security_group_tfer--infovault-dev-rds-sg_sg-02f66e75b6d635108_id": {"sensitive": false, "type": "string", "value": "sg-02f66e75b6d635108"}, "aws_security_group_tfer--infovault-dev-rds-sg_sg-09780ff2c4d7753e1_id": {"sensitive": false, "type": "string", "value": "sg-09780ff2c4d7753e1"}, "aws_security_group_tfer--infovault-dev-rds-sg_sg-0dd095db031d21cf3_id": {"sensitive": false, "type": "string", "value": "sg-0dd095db031d21cf3"}, "aws_security_group_tfer--infovault-dev-rds-sg_sg-0e904d365617c1e2d_id": {"sensitive": false, "type": "string", "value": "sg-0e904d365617c1e2d"}, "aws_security_group_tfer--infovault-eks-cluster-sg_sg-0353836b1b373e27d_id": {"sensitive": false, "type": "string", "value": "sg-0353836b1b373e27d"}, "aws_security_group_tfer--infovault-eks-node-sg_sg-05f09a0dc7ab8c53f_id": {"sensitive": false, "type": "string", "value": "sg-05f09a0dc7ab8c53f"}, "aws_security_group_tfer--infovault-mgmt-gitlab-sg_sg-00df94fe66c7464f5_id": {"sensitive": false, "type": "string", "value": "sg-00df94fe66c7464f5"}, "aws_security_group_tfer--infovault-mgmt-servers-sg_sg-095117b1a793c2ce0_id": {"sensitive": false, "type": "string", "value": "sg-095117b1a793c2ce0"}, "aws_security_group_tfer--infovault-mgmt-vpce-sg_sg-0d28b5ada1bb60c9a_id": {"sensitive": false, "type": "string", "value": "sg-0d28b5ada1bb60c9a"}, "aws_security_group_tfer--net-squid-sg_sg-00b66d34fb63637f3_id": {"sensitive": false, "type": "string", "value": "sg-00b66d34fb63637f3"}, "aws_security_group_tfer--net-squid-sg_sg-01c71a601fbce5d26_id": {"sensitive": false, "type": "string", "value": "sg-01c71a601fbce5d26"}, "aws_security_group_tfer--net-squid-sg_sg-0906e124ed285bd7e_id": {"sensitive": false, "type": "string", "value": "sg-0906e124ed285bd7e"}, "aws_security_group_tfer--net-squid-sg_sg-097db697cb7a5f558_id": {"sensitive": false, "type": "string", "value": "sg-097db697cb7a5f558"}, "aws_security_group_tfer--patching-rhel-repo-sg_sg-01f0c371bd11aa6f1_id": {"sensitive": false, "type": "string", "value": "sg-01f0c371bd11aa6f1"}, "aws_security_group_tfer--patching-vpce-sg_sg-0870c466d0db940c4_id": {"sensitive": false, "type": "string", "value": "sg-0870c466d0db940c4"}, "aws_security_group_tfer--patching-wsus-sg_sg-04080d467ff2ca6c4_id": {"sensitive": false, "type": "string", "value": "sg-04080d467ff2ca6c4"}, "aws_security_group_tfer--poc-host-naga_sg-054a1946b939e0eca_id": {"sensitive": false, "type": "string", "value": "sg-054a1946b939e0eca"}, "aws_security_group_tfer--sgrp-dev-cam-srvr-01_sg-029c5f3f1ddf2855e_id": {"sensitive": false, "type": "string", "value": "sg-029c5f3f1ddf2855e"}, "aws_security_group_tfer--sgrp-dev-smtp-srvr-01_sg-0764227cd7e054e0a_id": {"sensitive": false, "type": "string", "value": "sg-0764227cd7e054e0a"}, "aws_security_group_tfer--sgrp-dev-squid-pxy-iz-01_sg-023130e17836a50d3_id": {"sensitive": false, "type": "string", "value": "sg-023130e17836a50d3"}}, "resources": {"aws_security_group.tfer--Database-SQL-infovault-gen-facing-compartment_sg-0d8a5fa845dca93c9": {"type": "aws_security_group", "depends_on": [], "primary": {"id": "sg-0d8a5fa845dca93c9", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:security-group/sg-0d8a5fa845dca93c9", "description": "Database Security for SQL Server Database", "egress.#": "1", "egress.0.cidr_blocks.#": "1", "egress.0.cidr_blocks.0": "0.0.0.0/0", "egress.0.description": "", "egress.0.from_port": "0", "egress.0.ipv6_cidr_blocks.#": "0", "egress.0.prefix_list_ids.#": "0", "egress.0.protocol": "-1", "egress.0.security_groups.#": "0", "egress.0.self": "false", "egress.0.to_port": "0", "id": "sg-0d8a5fa845dca93c9", "ingress.#": "12", "ingress.0.cidr_blocks.#": "2", "ingress.0.cidr_blocks.0": "0.0.0.0/0", "ingress.0.cidr_blocks.1": "**********/24", "ingress.0.description": "", "ingress.0.from_port": "443", "ingress.0.ipv6_cidr_blocks.#": "0", "ingress.0.prefix_list_ids.#": "0", "ingress.0.protocol": "tcp", "ingress.0.security_groups.#": "0", "ingress.0.self": "false", "ingress.0.to_port": "443", "ingress.1.cidr_blocks.#": "1", "ingress.1.cidr_blocks.0": "**********/26", "ingress.1.description": "HTTPS from Management VPC", "ingress.1.from_port": "443", "ingress.1.ipv6_cidr_blocks.#": "0", "ingress.1.prefix_list_ids.#": "0", "ingress.1.protocol": "tcp", "ingress.1.security_groups.#": "0", "ingress.1.self": "false", "ingress.1.to_port": "443", "ingress.10.cidr_blocks.#": "0", "ingress.10.description": "EKS Cluster", "ingress.10.from_port": "0", "ingress.10.ipv6_cidr_blocks.#": "0", "ingress.10.prefix_list_ids.#": "0", "ingress.10.protocol": "-1", "ingress.10.security_groups.#": "1", "ingress.10.security_groups.0": "sg-0950397297ecb203f", "ingress.10.self": "false", "ingress.10.to_port": "0", "ingress.11.cidr_blocks.#": "0", "ingress.11.description": "ToolingServer-Win", "ingress.11.from_port": "1433", "ingress.11.ipv6_cidr_blocks.#": "0", "ingress.11.prefix_list_ids.#": "0", "ingress.11.protocol": "tcp", "ingress.11.security_groups.#": "1", "ingress.11.security_groups.0": "sg-095117b1a793c2ce0", "ingress.11.self": "false", "ingress.11.to_port": "1433", "ingress.2.cidr_blocks.#": "1", "ingress.2.cidr_blocks.0": "**********/16", "ingress.2.description": "", "ingress.2.from_port": "10000", "ingress.2.ipv6_cidr_blocks.#": "0", "ingress.2.prefix_list_ids.#": "0", "ingress.2.protocol": "tcp", "ingress.2.security_groups.#": "0", "ingress.2.self": "false", "ingress.2.to_port": "10000", "ingress.3.cidr_blocks.#": "1", "ingress.3.cidr_blocks.0": "**********/16", "ingress.3.description": "", "ingress.3.from_port": "1111", "ingress.3.ipv6_cidr_blocks.#": "0", "ingress.3.prefix_list_ids.#": "0", "ingress.3.protocol": "tcp", "ingress.3.security_groups.#": "0", "ingress.3.self": "false", "ingress.3.to_port": "1111", "ingress.4.cidr_blocks.#": "1", "ingress.4.cidr_blocks.0": "**********/16", "ingress.4.description": "", "ingress.4.from_port": "6379", "ingress.4.ipv6_cidr_blocks.#": "0", "ingress.4.prefix_list_ids.#": "0", "ingress.4.protocol": "tcp", "ingress.4.security_groups.#": "0", "ingress.4.self": "false", "ingress.4.to_port": "6379", "ingress.5.cidr_blocks.#": "1", "ingress.5.cidr_blocks.0": "**********/24", "ingress.5.description": "", "ingress.5.from_port": "80", "ingress.5.ipv6_cidr_blocks.#": "0", "ingress.5.prefix_list_ids.#": "0", "ingress.5.protocol": "tcp", "ingress.5.security_groups.#": "0", "ingress.5.self": "false", "ingress.5.to_port": "80", "ingress.6.cidr_blocks.#": "2", "ingress.6.cidr_blocks.0": "**********/24", "ingress.6.cidr_blocks.1": "**********/24", "ingress.6.description": "", "ingress.6.from_port": "1433", "ingress.6.ipv6_cidr_blocks.#": "0", "ingress.6.prefix_list_ids.#": "0", "ingress.6.protocol": "tcp", "ingress.6.security_groups.#": "0", "ingress.6.self": "false", "ingress.6.to_port": "1433", "ingress.7.cidr_blocks.#": "1", "ingress.7.cidr_blocks.0": "*************/32", "ingress.7.description": "SSH access from user IP", "ingress.7.from_port": "22", "ingress.7.ipv6_cidr_blocks.#": "0", "ingress.7.prefix_list_ids.#": "0", "ingress.7.protocol": "tcp", "ingress.7.security_groups.#": "0", "ingress.7.self": "false", "ingress.7.to_port": "22", "ingress.8.cidr_blocks.#": "1", "ingress.8.cidr_blocks.0": "*************/32", "ingress.8.description": "SSH access from Candor office", "ingress.8.from_port": "22", "ingress.8.ipv6_cidr_blocks.#": "0", "ingress.8.prefix_list_ids.#": "0", "ingress.8.protocol": "tcp", "ingress.8.security_groups.#": "0", "ingress.8.self": "false", "ingress.8.to_port": "22", "ingress.9.cidr_blocks.#": "1", "ingress.9.cidr_blocks.0": "************/32", "ingress.9.description": "SSH access from B-19 office", "ingress.9.from_port": "22", "ingress.9.ipv6_cidr_blocks.#": "0", "ingress.9.prefix_list_ids.#": "0", "ingress.9.protocol": "tcp", "ingress.9.security_groups.#": "0", "ingress.9.self": "false", "ingress.9.to_port": "22", "name": "Database-SQL-infovault-gen-facing-compartment", "name_prefix": "", "owner_id": "046276255144", "tags.%": "0", "tags_all.%": "0", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_security_group.tfer--GuardDutyManagedSecurityGroup-vpc-037f619602fa293a1_sg-0455dd83ca1b155d3": {"type": "aws_security_group", "depends_on": [], "primary": {"id": "sg-0455dd83ca1b155d3", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:security-group/sg-0455dd83ca1b155d3", "description": "Associated with VPC-vpc-037f619602fa293a1 and tagged as GuardDutyManaged", "egress.#": "0", "id": "sg-0455dd83ca1b155d3", "ingress.#": "1", "ingress.0.cidr_blocks.#": "1", "ingress.0.cidr_blocks.0": "**********/27", "ingress.0.description": "GuardDuty managed security group inbound rule associated with VPC vpc-037f619602fa293a1", "ingress.0.from_port": "443", "ingress.0.ipv6_cidr_blocks.#": "0", "ingress.0.prefix_list_ids.#": "0", "ingress.0.protocol": "tcp", "ingress.0.security_groups.#": "0", "ingress.0.self": "false", "ingress.0.to_port": "443", "name": "GuardDutyManagedSecurityGroup-vpc-037f619602fa293a1", "name_prefix": "", "owner_id": "046276255144", "tags.%": "1", "tags.GuardDutyManaged": "true", "tags_all.%": "1", "tags_all.GuardDutyManaged": "true", "vpc_id": "vpc-037f619602fa293a1"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_security_group.tfer--GuardDutyManagedSecurityGroup-vpc-05e0e8104b3321c40_sg-0650a95e2c0c1aaf5": {"type": "aws_security_group", "depends_on": [], "primary": {"id": "sg-0650a95e2c0c1aaf5", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:security-group/sg-0650a95e2c0c1aaf5", "description": "Associated with VPC-vpc-05e0e8104b3321c40 and tagged as GuardDutyManaged", "egress.#": "0", "id": "sg-0650a95e2c0c1aaf5", "ingress.#": "1", "ingress.0.cidr_blocks.#": "2", "ingress.0.cidr_blocks.0": "*********/26", "ingress.0.cidr_blocks.1": "**********/22", "ingress.0.description": "GuardDuty managed security group inbound rule associated with VPC vpc-05e0e8104b3321c40", "ingress.0.from_port": "443", "ingress.0.ipv6_cidr_blocks.#": "0", "ingress.0.prefix_list_ids.#": "0", "ingress.0.protocol": "tcp", "ingress.0.security_groups.#": "0", "ingress.0.self": "false", "ingress.0.to_port": "443", "name": "GuardDutyManagedSecurityGroup-vpc-05e0e8104b3321c40", "name_prefix": "", "owner_id": "046276255144", "tags.%": "1", "tags.GuardDutyManaged": "true", "tags_all.%": "1", "tags_all.GuardDutyManaged": "true", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_security_group.tfer--GuardDutyManagedSecurityGroup-vpc-05f1b20a6634c6a38_sg-065d1e24d43126489": {"type": "aws_security_group", "depends_on": [], "primary": {"id": "sg-065d1e24d43126489", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:security-group/sg-065d1e24d43126489", "description": "Associated with VPC-vpc-05f1b20a6634c6a38 and tagged as GuardDutyManaged", "egress.#": "0", "id": "sg-065d1e24d43126489", "ingress.#": "1", "ingress.0.cidr_blocks.#": "1", "ingress.0.cidr_blocks.0": "**********/26", "ingress.0.description": "GuardDuty managed security group inbound rule associated with VPC vpc-05f1b20a6634c6a38", "ingress.0.from_port": "443", "ingress.0.ipv6_cidr_blocks.#": "0", "ingress.0.prefix_list_ids.#": "0", "ingress.0.protocol": "tcp", "ingress.0.security_groups.#": "0", "ingress.0.self": "false", "ingress.0.to_port": "443", "name": "GuardDutyManagedSecurityGroup-vpc-05f1b20a6634c6a38", "name_prefix": "", "owner_id": "046276255144", "tags.%": "1", "tags.GuardDutyManaged": "true", "tags_all.%": "1", "tags_all.GuardDutyManaged": "true", "vpc_id": "vpc-05f1b20a6634c6a38"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_security_group.tfer--GuardDutyManagedSecurityGroup-vpc-0ca0480cce82946b3_sg-0330a127b43c54ab2": {"type": "aws_security_group", "depends_on": [], "primary": {"id": "sg-0330a127b43c54ab2", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:security-group/sg-0330a127b43c54ab2", "description": "Associated with VPC-vpc-0ca0480cce82946b3 and tagged as GuardDutyManaged", "egress.#": "0", "id": "sg-0330a127b43c54ab2", "ingress.#": "1", "ingress.0.cidr_blocks.#": "1", "ingress.0.cidr_blocks.0": "**********/27", "ingress.0.description": "GuardDuty managed security group inbound rule associated with VPC vpc-0ca0480cce82946b3", "ingress.0.from_port": "443", "ingress.0.ipv6_cidr_blocks.#": "0", "ingress.0.prefix_list_ids.#": "0", "ingress.0.protocol": "tcp", "ingress.0.security_groups.#": "0", "ingress.0.self": "false", "ingress.0.to_port": "443", "name": "GuardDutyManagedSecurityGroup-vpc-0ca0480cce82946b3", "name_prefix": "", "owner_id": "046276255144", "tags.%": "1", "tags.GuardDutyManaged": "true", "tags_all.%": "1", "tags_all.GuardDutyManaged": "true", "vpc_id": "vpc-0ca0480cce82946b3"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_security_group.tfer--InfoVault-MigrationServer-SG_sg-0c081d79227db7d86": {"type": "aws_security_group", "depends_on": [], "primary": {"id": "sg-0c081d79227db7d86", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:security-group/sg-0c081d79227db7d86", "description": "Security group for Migration Server", "egress.#": "1", "egress.0.cidr_blocks.#": "1", "egress.0.cidr_blocks.0": "0.0.0.0/0", "egress.0.description": "", "egress.0.from_port": "0", "egress.0.ipv6_cidr_blocks.#": "0", "egress.0.prefix_list_ids.#": "0", "egress.0.protocol": "-1", "egress.0.security_groups.#": "0", "egress.0.self": "false", "egress.0.to_port": "0", "id": "sg-0c081d79227db7d86", "ingress.#": "4", "ingress.0.cidr_blocks.#": "1", "ingress.0.cidr_blocks.0": "0.0.0.0/0", "ingress.0.description": "", "ingress.0.from_port": "3389", "ingress.0.ipv6_cidr_blocks.#": "0", "ingress.0.prefix_list_ids.#": "0", "ingress.0.protocol": "tcp", "ingress.0.security_groups.#": "0", "ingress.0.self": "false", "ingress.0.to_port": "3389", "ingress.1.cidr_blocks.#": "1", "ingress.1.cidr_blocks.0": "0.0.0.0/0", "ingress.1.description": "", "ingress.1.from_port": "443", "ingress.1.ipv6_cidr_blocks.#": "0", "ingress.1.prefix_list_ids.#": "0", "ingress.1.protocol": "tcp", "ingress.1.security_groups.#": "0", "ingress.1.self": "false", "ingress.1.to_port": "443", "ingress.2.cidr_blocks.#": "1", "ingress.2.cidr_blocks.0": "10.0.0.0/8", "ingress.2.description": "", "ingress.2.from_port": "1433", "ingress.2.ipv6_cidr_blocks.#": "0", "ingress.2.prefix_list_ids.#": "0", "ingress.2.protocol": "tcp", "ingress.2.security_groups.#": "0", "ingress.2.self": "false", "ingress.2.to_port": "1433", "ingress.3.cidr_blocks.#": "1", "ingress.3.cidr_blocks.0": "10.0.0.0/8", "ingress.3.description": "", "ingress.3.from_port": "5985", "ingress.3.ipv6_cidr_blocks.#": "0", "ingress.3.prefix_list_ids.#": "0", "ingress.3.protocol": "tcp", "ingress.3.security_groups.#": "0", "ingress.3.self": "false", "ingress.3.to_port": "5986", "name": "InfoVault-MigrationServer-SG", "name_prefix": "", "owner_id": "046276255144", "tags.%": "0", "tags_all.%": "0", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_security_group.tfer--InfoVault-SPS2016-SG_sg-08b7a467b37acf0e7": {"type": "aws_security_group", "depends_on": [], "primary": {"id": "sg-08b7a467b37acf0e7", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:security-group/sg-08b7a467b37acf0e7", "description": "Security group for SharePoint 2016 server", "egress.#": "1", "egress.0.cidr_blocks.#": "1", "egress.0.cidr_blocks.0": "0.0.0.0/0", "egress.0.description": "", "egress.0.from_port": "0", "egress.0.ipv6_cidr_blocks.#": "0", "egress.0.prefix_list_ids.#": "0", "egress.0.protocol": "-1", "egress.0.security_groups.#": "0", "egress.0.self": "false", "egress.0.to_port": "0", "id": "sg-08b7a467b37acf0e7", "ingress.#": "5", "ingress.0.cidr_blocks.#": "1", "ingress.0.cidr_blocks.0": "0.0.0.0/0", "ingress.0.description": "", "ingress.0.from_port": "3389", "ingress.0.ipv6_cidr_blocks.#": "0", "ingress.0.prefix_list_ids.#": "0", "ingress.0.protocol": "tcp", "ingress.0.security_groups.#": "0", "ingress.0.self": "false", "ingress.0.to_port": "3389", "ingress.1.cidr_blocks.#": "1", "ingress.1.cidr_blocks.0": "0.0.0.0/0", "ingress.1.description": "", "ingress.1.from_port": "443", "ingress.1.ipv6_cidr_blocks.#": "0", "ingress.1.prefix_list_ids.#": "0", "ingress.1.protocol": "tcp", "ingress.1.security_groups.#": "0", "ingress.1.self": "false", "ingress.1.to_port": "443", "ingress.2.cidr_blocks.#": "1", "ingress.2.cidr_blocks.0": "0.0.0.0/0", "ingress.2.description": "", "ingress.2.from_port": "80", "ingress.2.ipv6_cidr_blocks.#": "0", "ingress.2.prefix_list_ids.#": "0", "ingress.2.protocol": "tcp", "ingress.2.security_groups.#": "0", "ingress.2.self": "false", "ingress.2.to_port": "80", "ingress.3.cidr_blocks.#": "1", "ingress.3.cidr_blocks.0": "10.0.0.0/8", "ingress.3.description": "", "ingress.3.from_port": "1433", "ingress.3.ipv6_cidr_blocks.#": "0", "ingress.3.prefix_list_ids.#": "0", "ingress.3.protocol": "tcp", "ingress.3.security_groups.#": "0", "ingress.3.self": "false", "ingress.3.to_port": "1433", "ingress.4.cidr_blocks.#": "1", "ingress.4.cidr_blocks.0": "10.0.0.0/8", "ingress.4.description": "", "ingress.4.from_port": "5985", "ingress.4.ipv6_cidr_blocks.#": "0", "ingress.4.prefix_list_ids.#": "0", "ingress.4.protocol": "tcp", "ingress.4.security_groups.#": "0", "ingress.4.self": "false", "ingress.4.to_port": "5986", "name": "InfoVault-SPS2016-SG", "name_prefix": "", "owner_id": "046276255144", "tags.%": "0", "tags_all.%": "0", "vpc_id": "vpc-037f619602fa293a1"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_security_group.tfer--ablrhf-general_sg-0a6d2925236dcbd18": {"type": "aws_security_group", "depends_on": [], "primary": {"id": "sg-0a6d2925236dcbd18", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:security-group/sg-0a6d2925236dcbd18", "description": "Security group for general resources in the ABLRHF compartment", "egress.#": "1", "egress.0.cidr_blocks.#": "1", "egress.0.cidr_blocks.0": "0.0.0.0/0", "egress.0.description": "Allow all outbound traffic", "egress.0.from_port": "0", "egress.0.ipv6_cidr_blocks.#": "0", "egress.0.prefix_list_ids.#": "0", "egress.0.protocol": "-1", "egress.0.security_groups.#": "0", "egress.0.self": "false", "egress.0.to_port": "0", "id": "sg-0a6d2925236dcbd18", "ingress.#": "1", "ingress.0.cidr_blocks.#": "1", "ingress.0.cidr_blocks.0": "*********/27", "ingress.0.description": "Allow all internal VPC traffic", "ingress.0.from_port": "0", "ingress.0.ipv6_cidr_blocks.#": "0", "ingress.0.prefix_list_ids.#": "0", "ingress.0.protocol": "-1", "ingress.0.security_groups.#": "0", "ingress.0.self": "false", "ingress.0.to_port": "0", "name": "ablrhf-general", "name_prefix": "", "owner_id": "046276255144", "tags.%": "9", "tags.AutoShutdown": "true", "tags.Compartment": "ABLRHF", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "ablrhf-general", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "9", "tags_all.AutoShutdown": "true", "tags_all.Compartment": "ABLRHF", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "ablrhf-general", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_id": "vpc-0e75036a2ad9e5b4f"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_security_group.tfer--ablrhf-vpce_sg-082d3fc2b26b1004e": {"type": "aws_security_group", "depends_on": [], "primary": {"id": "sg-082d3fc2b26b1004e", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:security-group/sg-082d3fc2b26b1004e", "description": "Security group for VPC endpoints in the ABLRHF compartment", "egress.#": "1", "egress.0.cidr_blocks.#": "1", "egress.0.cidr_blocks.0": "0.0.0.0/0", "egress.0.description": "Allow all outbound traffic", "egress.0.from_port": "0", "egress.0.ipv6_cidr_blocks.#": "0", "egress.0.prefix_list_ids.#": "0", "egress.0.protocol": "-1", "egress.0.security_groups.#": "0", "egress.0.self": "false", "egress.0.to_port": "0", "id": "sg-082d3fc2b26b1004e", "ingress.#": "1", "ingress.0.cidr_blocks.#": "1", "ingress.0.cidr_blocks.0": "*********/27", "ingress.0.description": "Allow HTTPS from VPC CIDR", "ingress.0.from_port": "443", "ingress.0.ipv6_cidr_blocks.#": "0", "ingress.0.prefix_list_ids.#": "0", "ingress.0.protocol": "tcp", "ingress.0.security_groups.#": "0", "ingress.0.self": "false", "ingress.0.to_port": "443", "name": "ablrhf-vpce", "name_prefix": "", "owner_id": "046276255144", "tags.%": "9", "tags.AutoShutdown": "true", "tags.Compartment": "ABLRHF", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "ablrhf-vpce", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "9", "tags_all.AutoShutdown": "true", "tags_all.Compartment": "ABLRHF", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "ablrhf-vpce", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_id": "vpc-0e75036a2ad9e5b4f"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_security_group.tfer--d-9667b2da13_controllers_sg-0f48a9bd414cfd781": {"type": "aws_security_group", "depends_on": [], "primary": {"id": "sg-0f48a9bd414cfd781", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:security-group/sg-0f48a9bd414cfd781", "description": "AWS created security group for d-9667b2da13 directory controllers", "egress.#": "1", "egress.0.cidr_blocks.#": "1", "egress.0.cidr_blocks.0": "0.0.0.0/0", "egress.0.description": "", "egress.0.from_port": "0", "egress.0.ipv6_cidr_blocks.#": "0", "egress.0.prefix_list_ids.#": "0", "egress.0.protocol": "-1", "egress.0.security_groups.#": "0", "egress.0.self": "false", "egress.0.to_port": "0", "id": "sg-0f48a9bd414cfd781", "ingress.#": "18", "ingress.0.cidr_blocks.#": "1", "ingress.0.cidr_blocks.0": "**********/26", "ingress.0.description": "", "ingress.0.from_port": "-1", "ingress.0.ipv6_cidr_blocks.#": "0", "ingress.0.prefix_list_ids.#": "0", "ingress.0.protocol": "icmp", "ingress.0.security_groups.#": "0", "ingress.0.self": "false", "ingress.0.to_port": "-1", "ingress.1.cidr_blocks.#": "1", "ingress.1.cidr_blocks.0": "**********/26", "ingress.1.description": "", "ingress.1.from_port": "1024", "ingress.1.ipv6_cidr_blocks.#": "0", "ingress.1.prefix_list_ids.#": "0", "ingress.1.protocol": "tcp", "ingress.1.security_groups.#": "0", "ingress.1.self": "false", "ingress.1.to_port": "65535", "ingress.10.cidr_blocks.#": "1", "ingress.10.cidr_blocks.0": "**********/26", "ingress.10.description": "", "ingress.10.from_port": "464", "ingress.10.ipv6_cidr_blocks.#": "0", "ingress.10.prefix_list_ids.#": "0", "ingress.10.protocol": "tcp", "ingress.10.security_groups.#": "0", "ingress.10.self": "false", "ingress.10.to_port": "464", "ingress.11.cidr_blocks.#": "1", "ingress.11.cidr_blocks.0": "**********/26", "ingress.11.description": "", "ingress.11.from_port": "464", "ingress.11.ipv6_cidr_blocks.#": "0", "ingress.11.prefix_list_ids.#": "0", "ingress.11.protocol": "udp", "ingress.11.security_groups.#": "0", "ingress.11.self": "false", "ingress.11.to_port": "464", "ingress.12.cidr_blocks.#": "1", "ingress.12.cidr_blocks.0": "**********/26", "ingress.12.description": "", "ingress.12.from_port": "53", "ingress.12.ipv6_cidr_blocks.#": "0", "ingress.12.prefix_list_ids.#": "0", "ingress.12.protocol": "tcp", "ingress.12.security_groups.#": "0", "ingress.12.self": "false", "ingress.12.to_port": "53", "ingress.13.cidr_blocks.#": "1", "ingress.13.cidr_blocks.0": "**********/26", "ingress.13.description": "", "ingress.13.from_port": "53", "ingress.13.ipv6_cidr_blocks.#": "0", "ingress.13.prefix_list_ids.#": "0", "ingress.13.protocol": "udp", "ingress.13.security_groups.#": "0", "ingress.13.self": "false", "ingress.13.to_port": "53", "ingress.14.cidr_blocks.#": "1", "ingress.14.cidr_blocks.0": "**********/26", "ingress.14.description": "", "ingress.14.from_port": "636", "ingress.14.ipv6_cidr_blocks.#": "0", "ingress.14.prefix_list_ids.#": "0", "ingress.14.protocol": "tcp", "ingress.14.security_groups.#": "0", "ingress.14.self": "false", "ingress.14.to_port": "636", "ingress.15.cidr_blocks.#": "1", "ingress.15.cidr_blocks.0": "**********/26", "ingress.15.description": "", "ingress.15.from_port": "88", "ingress.15.ipv6_cidr_blocks.#": "0", "ingress.15.prefix_list_ids.#": "0", "ingress.15.protocol": "tcp", "ingress.15.security_groups.#": "0", "ingress.15.self": "false", "ingress.15.to_port": "88", "ingress.16.cidr_blocks.#": "1", "ingress.16.cidr_blocks.0": "**********/26", "ingress.16.description": "", "ingress.16.from_port": "88", "ingress.16.ipv6_cidr_blocks.#": "0", "ingress.16.prefix_list_ids.#": "0", "ingress.16.protocol": "udp", "ingress.16.security_groups.#": "0", "ingress.16.self": "false", "ingress.16.to_port": "88", "ingress.17.cidr_blocks.#": "0", "ingress.17.description": "", "ingress.17.from_port": "0", "ingress.17.ipv6_cidr_blocks.#": "0", "ingress.17.prefix_list_ids.#": "0", "ingress.17.protocol": "-1", "ingress.17.security_groups.#": "0", "ingress.17.self": "true", "ingress.17.to_port": "0", "ingress.2.cidr_blocks.#": "1", "ingress.2.cidr_blocks.0": "**********/26", "ingress.2.description": "", "ingress.2.from_port": "123", "ingress.2.ipv6_cidr_blocks.#": "0", "ingress.2.prefix_list_ids.#": "0", "ingress.2.protocol": "udp", "ingress.2.security_groups.#": "0", "ingress.2.self": "false", "ingress.2.to_port": "123", "ingress.3.cidr_blocks.#": "1", "ingress.3.cidr_blocks.0": "**********/26", "ingress.3.description": "", "ingress.3.from_port": "135", "ingress.3.ipv6_cidr_blocks.#": "0", "ingress.3.prefix_list_ids.#": "0", "ingress.3.protocol": "tcp", "ingress.3.security_groups.#": "0", "ingress.3.self": "false", "ingress.3.to_port": "135", "ingress.4.cidr_blocks.#": "1", "ingress.4.cidr_blocks.0": "**********/26", "ingress.4.description": "", "ingress.4.from_port": "138", "ingress.4.ipv6_cidr_blocks.#": "0", "ingress.4.prefix_list_ids.#": "0", "ingress.4.protocol": "udp", "ingress.4.security_groups.#": "0", "ingress.4.self": "false", "ingress.4.to_port": "138", "ingress.5.cidr_blocks.#": "1", "ingress.5.cidr_blocks.0": "**********/26", "ingress.5.description": "", "ingress.5.from_port": "3268", "ingress.5.ipv6_cidr_blocks.#": "0", "ingress.5.prefix_list_ids.#": "0", "ingress.5.protocol": "tcp", "ingress.5.security_groups.#": "0", "ingress.5.self": "false", "ingress.5.to_port": "3269", "ingress.6.cidr_blocks.#": "1", "ingress.6.cidr_blocks.0": "**********/26", "ingress.6.description": "", "ingress.6.from_port": "389", "ingress.6.ipv6_cidr_blocks.#": "0", "ingress.6.prefix_list_ids.#": "0", "ingress.6.protocol": "tcp", "ingress.6.security_groups.#": "0", "ingress.6.self": "false", "ingress.6.to_port": "389", "ingress.7.cidr_blocks.#": "1", "ingress.7.cidr_blocks.0": "**********/26", "ingress.7.description": "", "ingress.7.from_port": "389", "ingress.7.ipv6_cidr_blocks.#": "0", "ingress.7.prefix_list_ids.#": "0", "ingress.7.protocol": "udp", "ingress.7.security_groups.#": "0", "ingress.7.self": "false", "ingress.7.to_port": "389", "ingress.8.cidr_blocks.#": "1", "ingress.8.cidr_blocks.0": "**********/26", "ingress.8.description": "", "ingress.8.from_port": "445", "ingress.8.ipv6_cidr_blocks.#": "0", "ingress.8.prefix_list_ids.#": "0", "ingress.8.protocol": "tcp", "ingress.8.security_groups.#": "0", "ingress.8.self": "false", "ingress.8.to_port": "445", "ingress.9.cidr_blocks.#": "1", "ingress.9.cidr_blocks.0": "**********/26", "ingress.9.description": "", "ingress.9.from_port": "445", "ingress.9.ipv6_cidr_blocks.#": "0", "ingress.9.prefix_list_ids.#": "0", "ingress.9.protocol": "udp", "ingress.9.security_groups.#": "0", "ingress.9.self": "false", "ingress.9.to_port": "445", "name": "d-9667b2da13_controllers", "name_prefix": "", "owner_id": "046276255144", "tags.%": "0", "tags_all.%": "0", "vpc_id": "vpc-05f1b20a6634c6a38"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_security_group.tfer--dam-agent-gateway-sg_sg-043af8ab2fe4ec50f": {"type": "aws_security_group", "depends_on": [], "primary": {"id": "sg-043af8ab2fe4ec50f", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:security-group/sg-043af8ab2fe4ec50f", "description": "Security group for DAM Agent Gateway", "egress.#": "1", "egress.0.cidr_blocks.#": "1", "egress.0.cidr_blocks.0": "0.0.0.0/0", "egress.0.description": "", "egress.0.from_port": "0", "egress.0.ipv6_cidr_blocks.#": "0", "egress.0.prefix_list_ids.#": "0", "egress.0.protocol": "-1", "egress.0.security_groups.#": "0", "egress.0.self": "false", "egress.0.to_port": "0", "id": "sg-043af8ab2fe4ec50f", "ingress.#": "5", "ingress.0.cidr_blocks.#": "1", "ingress.0.cidr_blocks.0": "**********/22", "ingress.0.description": "", "ingress.0.from_port": "22", "ingress.0.ipv6_cidr_blocks.#": "0", "ingress.0.prefix_list_ids.#": "0", "ingress.0.protocol": "tcp", "ingress.0.security_groups.#": "0", "ingress.0.self": "false", "ingress.0.to_port": "22", "ingress.1.cidr_blocks.#": "1", "ingress.1.cidr_blocks.0": "**********/22", "ingress.1.description": "", "ingress.1.from_port": "2812", "ingress.1.ipv6_cidr_blocks.#": "0", "ingress.1.prefix_list_ids.#": "0", "ingress.1.protocol": "tcp", "ingress.1.security_groups.#": "0", "ingress.1.self": "false", "ingress.1.to_port": "2812", "ingress.2.cidr_blocks.#": "1", "ingress.2.cidr_blocks.0": "**********/22", "ingress.2.description": "", "ingress.2.from_port": "443", "ingress.2.ipv6_cidr_blocks.#": "0", "ingress.2.prefix_list_ids.#": "0", "ingress.2.protocol": "tcp", "ingress.2.security_groups.#": "0", "ingress.2.self": "false", "ingress.2.to_port": "443", "ingress.3.cidr_blocks.#": "1", "ingress.3.cidr_blocks.0": "**********/22", "ingress.3.description": "", "ingress.3.from_port": "514", "ingress.3.ipv6_cidr_blocks.#": "0", "ingress.3.prefix_list_ids.#": "0", "ingress.3.protocol": "udp", "ingress.3.security_groups.#": "0", "ingress.3.self": "false", "ingress.3.to_port": "514", "ingress.4.cidr_blocks.#": "1", "ingress.4.cidr_blocks.0": "**********/22", "ingress.4.description": "", "ingress.4.from_port": "8083", "ingress.4.ipv6_cidr_blocks.#": "0", "ingress.4.prefix_list_ids.#": "0", "ingress.4.protocol": "tcp", "ingress.4.security_groups.#": "0", "ingress.4.self": "false", "ingress.4.to_port": "8083", "name": "dam-agent-gateway-sg", "name_prefix": "", "owner_id": "046276255144", "tags.%": "2", "tags.Component": "DAM", "tags.Name": "dam-agent-gateway-sg", "tags_all.%": "2", "tags_all.Component": "DAM", "tags_all.Name": "dam-agent-gateway-sg", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_security_group.tfer--dam-gateway-dev-az1-sg_sg-0ebab82f4aa7f2a90": {"type": "aws_security_group", "depends_on": [], "primary": {"id": "sg-0ebab82f4aa7f2a90", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:security-group/sg-0ebab82f4aa7f2a90", "description": "Security group for DAM Gateway in AZ1", "egress.#": "2", "egress.0.cidr_blocks.#": "1", "egress.0.cidr_blocks.0": "0.0.0.0/0", "egress.0.description": "", "egress.0.from_port": "0", "egress.0.ipv6_cidr_blocks.#": "0", "egress.0.prefix_list_ids.#": "0", "egress.0.protocol": "-1", "egress.0.security_groups.#": "0", "egress.0.self": "false", "egress.0.to_port": "0", "egress.1.cidr_blocks.#": "1", "egress.1.cidr_blocks.0": "0.0.0.0/0", "egress.1.description": "", "egress.1.from_port": "443", "egress.1.ipv6_cidr_blocks.#": "0", "egress.1.prefix_list_ids.#": "0", "egress.1.protocol": "tcp", "egress.1.security_groups.#": "0", "egress.1.self": "false", "egress.1.to_port": "443", "id": "sg-0ebab82f4aa7f2a90", "ingress.#": "2", "ingress.0.cidr_blocks.#": "1", "ingress.0.cidr_blocks.0": "10.0.0.0/8", "ingress.0.description": "", "ingress.0.from_port": "22", "ingress.0.ipv6_cidr_blocks.#": "0", "ingress.0.prefix_list_ids.#": "0", "ingress.0.protocol": "tcp", "ingress.0.security_groups.#": "0", "ingress.0.self": "false", "ingress.0.to_port": "22", "ingress.1.cidr_blocks.#": "3", "ingress.1.cidr_blocks.0": "**********/26", "ingress.1.cidr_blocks.1": "*********/24", "ingress.1.cidr_blocks.2": "***********/24", "ingress.1.description": "", "ingress.1.from_port": "8083", "ingress.1.ipv6_cidr_blocks.#": "0", "ingress.1.prefix_list_ids.#": "0", "ingress.1.protocol": "tcp", "ingress.1.security_groups.#": "0", "ingress.1.self": "false", "ingress.1.to_port": "8083", "name": "dam-gateway-dev-az1-sg", "name_prefix": "", "owner_id": "046276255144", "tags.%": "1", "tags.Name": "dam-gateway-dev-az1-sg", "tags_all.%": "1", "tags_all.Name": "dam-gateway-dev-az1-sg", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_security_group.tfer--dam-gateway-dev-az2-sg_sg-06546af93a8931ae6": {"type": "aws_security_group", "depends_on": [], "primary": {"id": "sg-06546af93a8931ae6", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:security-group/sg-06546af93a8931ae6", "description": "Security group for DAM Gateway in AZ2", "egress.#": "2", "egress.0.cidr_blocks.#": "1", "egress.0.cidr_blocks.0": "0.0.0.0/0", "egress.0.description": "", "egress.0.from_port": "0", "egress.0.ipv6_cidr_blocks.#": "0", "egress.0.prefix_list_ids.#": "0", "egress.0.protocol": "-1", "egress.0.security_groups.#": "0", "egress.0.self": "false", "egress.0.to_port": "0", "egress.1.cidr_blocks.#": "1", "egress.1.cidr_blocks.0": "0.0.0.0/0", "egress.1.description": "", "egress.1.from_port": "443", "egress.1.ipv6_cidr_blocks.#": "0", "egress.1.prefix_list_ids.#": "0", "egress.1.protocol": "tcp", "egress.1.security_groups.#": "0", "egress.1.self": "false", "egress.1.to_port": "443", "id": "sg-06546af93a8931ae6", "ingress.#": "2", "ingress.0.cidr_blocks.#": "1", "ingress.0.cidr_blocks.0": "10.0.0.0/8", "ingress.0.description": "", "ingress.0.from_port": "22", "ingress.0.ipv6_cidr_blocks.#": "0", "ingress.0.prefix_list_ids.#": "0", "ingress.0.protocol": "tcp", "ingress.0.security_groups.#": "0", "ingress.0.self": "false", "ingress.0.to_port": "22", "ingress.1.cidr_blocks.#": "3", "ingress.1.cidr_blocks.0": "**********/26", "ingress.1.cidr_blocks.1": "*********/24", "ingress.1.cidr_blocks.2": "***********/24", "ingress.1.description": "", "ingress.1.from_port": "8083", "ingress.1.ipv6_cidr_blocks.#": "0", "ingress.1.prefix_list_ids.#": "0", "ingress.1.protocol": "tcp", "ingress.1.security_groups.#": "0", "ingress.1.self": "false", "ingress.1.to_port": "8083", "name": "dam-gateway-dev-az2-sg", "name_prefix": "", "owner_id": "046276255144", "tags.%": "1", "tags.Name": "dam-gateway-dev-az2-sg", "tags_all.%": "1", "tags_all.Name": "dam-gateway-dev-az2-sg", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_security_group.tfer--default_sg-010c8f879a2498c96": {"type": "aws_security_group", "depends_on": [], "primary": {"id": "sg-010c8f879a2498c96", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:security-group/sg-010c8f879a2498c96", "description": "default VPC security group", "egress.#": "1", "egress.0.cidr_blocks.#": "1", "egress.0.cidr_blocks.0": "0.0.0.0/0", "egress.0.description": "", "egress.0.from_port": "0", "egress.0.ipv6_cidr_blocks.#": "0", "egress.0.prefix_list_ids.#": "0", "egress.0.protocol": "-1", "egress.0.security_groups.#": "0", "egress.0.self": "false", "egress.0.to_port": "0", "id": "sg-010c8f879a2498c96", "ingress.#": "1", "ingress.0.cidr_blocks.#": "0", "ingress.0.description": "", "ingress.0.from_port": "0", "ingress.0.ipv6_cidr_blocks.#": "0", "ingress.0.prefix_list_ids.#": "0", "ingress.0.protocol": "-1", "ingress.0.security_groups.#": "0", "ingress.0.self": "true", "ingress.0.to_port": "0", "name": "default", "name_prefix": "", "owner_id": "046276255144", "tags.%": "0", "tags_all.%": "0", "vpc_id": "vpc-0e75036a2ad9e5b4f"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_security_group.tfer--default_sg-011238e3ae52fd306": {"type": "aws_security_group", "depends_on": [], "primary": {"id": "sg-011238e3ae52fd306", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:security-group/sg-011238e3ae52fd306", "description": "default VPC security group", "egress.#": "1", "egress.0.cidr_blocks.#": "1", "egress.0.cidr_blocks.0": "0.0.0.0/0", "egress.0.description": "", "egress.0.from_port": "0", "egress.0.ipv6_cidr_blocks.#": "0", "egress.0.prefix_list_ids.#": "0", "egress.0.protocol": "-1", "egress.0.security_groups.#": "0", "egress.0.self": "false", "egress.0.to_port": "0", "id": "sg-011238e3ae52fd306", "ingress.#": "1", "ingress.0.cidr_blocks.#": "0", "ingress.0.description": "", "ingress.0.from_port": "0", "ingress.0.ipv6_cidr_blocks.#": "0", "ingress.0.prefix_list_ids.#": "0", "ingress.0.protocol": "-1", "ingress.0.security_groups.#": "0", "ingress.0.self": "true", "ingress.0.to_port": "0", "name": "default", "name_prefix": "", "owner_id": "046276255144", "tags.%": "0", "tags_all.%": "0", "vpc_id": "vpc-02282d8d4c9c7764c"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_security_group.tfer--default_sg-019036bd03d215313": {"type": "aws_security_group", "depends_on": [], "primary": {"id": "sg-019036bd03d215313", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:security-group/sg-019036bd03d215313", "description": "default VPC security group", "egress.#": "1", "egress.0.cidr_blocks.#": "1", "egress.0.cidr_blocks.0": "0.0.0.0/0", "egress.0.description": "", "egress.0.from_port": "0", "egress.0.ipv6_cidr_blocks.#": "0", "egress.0.prefix_list_ids.#": "0", "egress.0.protocol": "-1", "egress.0.security_groups.#": "0", "egress.0.self": "false", "egress.0.to_port": "0", "id": "sg-019036bd03d215313", "ingress.#": "1", "ingress.0.cidr_blocks.#": "0", "ingress.0.description": "", "ingress.0.from_port": "0", "ingress.0.ipv6_cidr_blocks.#": "0", "ingress.0.prefix_list_ids.#": "0", "ingress.0.protocol": "-1", "ingress.0.security_groups.#": "0", "ingress.0.self": "true", "ingress.0.to_port": "0", "name": "default", "name_prefix": "", "owner_id": "046276255144", "tags.%": "0", "tags_all.%": "0", "vpc_id": "vpc-05f1b20a6634c6a38"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_security_group.tfer--default_sg-0a2302a73783eb575": {"type": "aws_security_group", "depends_on": [], "primary": {"id": "sg-0a2302a73783eb575", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:security-group/sg-0a2302a73783eb575", "description": "default VPC security group", "egress.#": "1", "egress.0.cidr_blocks.#": "1", "egress.0.cidr_blocks.0": "0.0.0.0/0", "egress.0.description": "", "egress.0.from_port": "0", "egress.0.ipv6_cidr_blocks.#": "0", "egress.0.prefix_list_ids.#": "0", "egress.0.protocol": "-1", "egress.0.security_groups.#": "0", "egress.0.self": "false", "egress.0.to_port": "0", "id": "sg-0a2302a73783eb575", "ingress.#": "1", "ingress.0.cidr_blocks.#": "0", "ingress.0.description": "", "ingress.0.from_port": "0", "ingress.0.ipv6_cidr_blocks.#": "0", "ingress.0.prefix_list_ids.#": "0", "ingress.0.protocol": "-1", "ingress.0.security_groups.#": "0", "ingress.0.self": "true", "ingress.0.to_port": "0", "name": "default", "name_prefix": "", "owner_id": "046276255144", "tags.%": "0", "tags_all.%": "0", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_security_group.tfer--default_sg-0b4e61e6b8c88c982": {"type": "aws_security_group", "depends_on": [], "primary": {"id": "sg-0b4e61e6b8c88c982", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:security-group/sg-0b4e61e6b8c88c982", "description": "default VPC security group", "egress.#": "1", "egress.0.cidr_blocks.#": "1", "egress.0.cidr_blocks.0": "0.0.0.0/0", "egress.0.description": "", "egress.0.from_port": "0", "egress.0.ipv6_cidr_blocks.#": "0", "egress.0.prefix_list_ids.#": "0", "egress.0.protocol": "-1", "egress.0.security_groups.#": "0", "egress.0.self": "false", "egress.0.to_port": "0", "id": "sg-0b4e61e6b8c88c982", "ingress.#": "1", "ingress.0.cidr_blocks.#": "0", "ingress.0.description": "", "ingress.0.from_port": "0", "ingress.0.ipv6_cidr_blocks.#": "0", "ingress.0.prefix_list_ids.#": "0", "ingress.0.protocol": "-1", "ingress.0.security_groups.#": "0", "ingress.0.self": "true", "ingress.0.to_port": "0", "name": "default", "name_prefix": "", "owner_id": "046276255144", "tags.%": "0", "tags_all.%": "0", "vpc_id": "vpc-037f619602fa293a1"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_security_group.tfer--default_sg-0b9bba8ae21bf0bbd": {"type": "aws_security_group", "depends_on": [], "primary": {"id": "sg-0b9bba8ae21bf0bbd", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:security-group/sg-0b9bba8ae21bf0bbd", "description": "default VPC security group", "egress.#": "1", "egress.0.cidr_blocks.#": "1", "egress.0.cidr_blocks.0": "0.0.0.0/0", "egress.0.description": "", "egress.0.from_port": "0", "egress.0.ipv6_cidr_blocks.#": "0", "egress.0.prefix_list_ids.#": "0", "egress.0.protocol": "-1", "egress.0.security_groups.#": "0", "egress.0.self": "false", "egress.0.to_port": "0", "id": "sg-0b9bba8ae21bf0bbd", "ingress.#": "1", "ingress.0.cidr_blocks.#": "0", "ingress.0.description": "", "ingress.0.from_port": "0", "ingress.0.ipv6_cidr_blocks.#": "0", "ingress.0.prefix_list_ids.#": "0", "ingress.0.protocol": "-1", "ingress.0.security_groups.#": "0", "ingress.0.self": "true", "ingress.0.to_port": "0", "name": "default", "name_prefix": "", "owner_id": "046276255144", "tags.%": "0", "tags_all.%": "0", "vpc_id": "vpc-0ca0480cce82946b3"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_security_group.tfer--dev-dra-admin-server-sg_sg-0b67c6b7904908adf": {"type": "aws_security_group", "depends_on": [], "primary": {"id": "sg-0b67c6b7904908adf", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:security-group/sg-0b67c6b7904908adf", "description": "Security group for DRA Admin Server", "egress.#": "1", "egress.0.cidr_blocks.#": "1", "egress.0.cidr_blocks.0": "0.0.0.0/0", "egress.0.description": "Allow all outbound traffic", "egress.0.from_port": "0", "egress.0.ipv6_cidr_blocks.#": "0", "egress.0.prefix_list_ids.#": "0", "egress.0.protocol": "-1", "egress.0.security_groups.#": "0", "egress.0.self": "false", "egress.0.to_port": "0", "id": "sg-0b67c6b7904908adf", "ingress.#": "1", "ingress.0.cidr_blocks.#": "2", "ingress.0.cidr_blocks.0": "**********/22", "ingress.0.cidr_blocks.1": "**********/26", "ingress.0.description": "HTTPS access from management networks", "ingress.0.from_port": "443", "ingress.0.ipv6_cidr_blocks.#": "0", "ingress.0.prefix_list_ids.#": "0", "ingress.0.protocol": "tcp", "ingress.0.security_groups.#": "0", "ingress.0.self": "false", "ingress.0.to_port": "443", "name": "dev-dra-admin-server-sg", "name_prefix": "", "owner_id": "046276255144", "tags.%": "9", "tags.AutoShutdown": "true", "tags.Component": "DAM", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "dev-dra-admin-server-sg", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "9", "tags_all.AutoShutdown": "true", "tags_all.Component": "DAM", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "dev-dra-admin-server-sg", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_id": "vpc-05f1b20a6634c6a38"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_security_group.tfer--dev-dra-analytics-server-sg_sg-0d7508facd515c5ea": {"type": "aws_security_group", "depends_on": [], "primary": {"id": "sg-0d7508facd515c5ea", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:security-group/sg-0d7508facd515c5ea", "description": "Security group for DRA Analytics Server", "egress.#": "1", "egress.0.cidr_blocks.#": "1", "egress.0.cidr_blocks.0": "0.0.0.0/0", "egress.0.description": "Allow all outbound traffic", "egress.0.from_port": "0", "egress.0.ipv6_cidr_blocks.#": "0", "egress.0.prefix_list_ids.#": "0", "egress.0.protocol": "-1", "egress.0.security_groups.#": "0", "egress.0.self": "false", "egress.0.to_port": "0", "id": "sg-0d7508facd515c5ea", "ingress.#": "1", "ingress.0.cidr_blocks.#": "2", "ingress.0.cidr_blocks.0": "**********/22", "ingress.0.cidr_blocks.1": "**********/26", "ingress.0.description": "HTTPS access from management networks", "ingress.0.from_port": "443", "ingress.0.ipv6_cidr_blocks.#": "0", "ingress.0.prefix_list_ids.#": "0", "ingress.0.protocol": "tcp", "ingress.0.security_groups.#": "0", "ingress.0.self": "false", "ingress.0.to_port": "443", "name": "dev-dra-analytics-server-sg", "name_prefix": "", "owner_id": "046276255144", "tags.%": "9", "tags.AutoShutdown": "true", "tags.Component": "DAM", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "dev-dra-analytics-server-sg", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "9", "tags_all.AutoShutdown": "true", "tags_all.Component": "DAM", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "dev-dra-analytics-server-sg", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_id": "vpc-05f1b20a6634c6a38"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_security_group.tfer--dev-management-server-sg_sg-0f2fdd192120dbf17": {"type": "aws_security_group", "depends_on": [], "primary": {"id": "sg-0f2fdd192120dbf17", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:security-group/sg-0f2fdd192120dbf17", "description": "Security group for DAM Management Server (AVM150)", "egress.#": "1", "egress.0.cidr_blocks.#": "1", "egress.0.cidr_blocks.0": "0.0.0.0/0", "egress.0.description": "Allow all outbound traffic", "egress.0.from_port": "0", "egress.0.ipv6_cidr_blocks.#": "0", "egress.0.prefix_list_ids.#": "0", "egress.0.protocol": "-1", "egress.0.security_groups.#": "0", "egress.0.self": "false", "egress.0.to_port": "0", "id": "sg-0f2fdd192120dbf17", "ingress.#": "7", "ingress.0.cidr_blocks.#": "2", "ingress.0.cidr_blocks.0": "**********/22", "ingress.0.cidr_blocks.1": "**********/26", "ingress.0.description": "HTTPS access from management networks", "ingress.0.from_port": "443", "ingress.0.ipv6_cidr_blocks.#": "0", "ingress.0.prefix_list_ids.#": "0", "ingress.0.protocol": "tcp", "ingress.0.security_groups.#": "0", "ingress.0.self": "false", "ingress.0.to_port": "443", "ingress.1.cidr_blocks.#": "2", "ingress.1.cidr_blocks.0": "**********/22", "ingress.1.cidr_blocks.1": "**********/26", "ingress.1.description": "Monit access from management networks", "ingress.1.from_port": "2812", "ingress.1.ipv6_cidr_blocks.#": "0", "ingress.1.prefix_list_ids.#": "0", "ingress.1.protocol": "tcp", "ingress.1.security_groups.#": "0", "ingress.1.self": "false", "ingress.1.to_port": "2812", "ingress.2.cidr_blocks.#": "1", "ingress.2.cidr_blocks.0": "**********/26", "ingress.2.description": "Management port 8081 from DB subnet", "ingress.2.from_port": "8081", "ingress.2.ipv6_cidr_blocks.#": "0", "ingress.2.prefix_list_ids.#": "0", "ingress.2.protocol": "tcp", "ingress.2.security_groups.#": "0", "ingress.2.self": "false", "ingress.2.to_port": "8081", "ingress.3.cidr_blocks.#": "1", "ingress.3.cidr_blocks.0": "**********/26", "ingress.3.description": "Management port 8083 from DB subnet", "ingress.3.from_port": "8083", "ingress.3.ipv6_cidr_blocks.#": "0", "ingress.3.prefix_list_ids.#": "0", "ingress.3.protocol": "tcp", "ingress.3.security_groups.#": "0", "ingress.3.self": "false", "ingress.3.to_port": "8083", "ingress.4.cidr_blocks.#": "1", "ingress.4.cidr_blocks.0": "**********/26", "ingress.4.description": "Management port 8084 from DB subnet", "ingress.4.from_port": "8084", "ingress.4.ipv6_cidr_blocks.#": "0", "ingress.4.prefix_list_ids.#": "0", "ingress.4.protocol": "tcp", "ingress.4.security_groups.#": "0", "ingress.4.self": "false", "ingress.4.to_port": "8084", "ingress.5.cidr_blocks.#": "1", "ingress.5.cidr_blocks.0": "**********/26", "ingress.5.description": "Management port 8085 from DB subnet", "ingress.5.from_port": "8085", "ingress.5.ipv6_cidr_blocks.#": "0", "ingress.5.prefix_list_ids.#": "0", "ingress.5.protocol": "tcp", "ingress.5.security_groups.#": "0", "ingress.5.self": "false", "ingress.5.to_port": "8085", "ingress.6.cidr_blocks.#": "1", "ingress.6.cidr_blocks.0": "**********/26", "ingress.6.description": "Syslog from DB subnet", "ingress.6.from_port": "514", "ingress.6.ipv6_cidr_blocks.#": "0", "ingress.6.prefix_list_ids.#": "0", "ingress.6.protocol": "udp", "ingress.6.security_groups.#": "0", "ingress.6.self": "false", "ingress.6.to_port": "514", "name": "dev-management-server-sg", "name_prefix": "", "owner_id": "046276255144", "tags.%": "9", "tags.AutoShutdown": "true", "tags.Component": "DAM", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "dev-management-server-sg", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "9", "tags_all.AutoShutdown": "true", "tags_all.Component": "DAM", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "dev-management-server-sg", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_id": "vpc-05f1b20a6634c6a38"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_security_group.tfer--dra-agent-gateway-sg_sg-02794d7f8eaa42fea": {"type": "aws_security_group", "depends_on": [], "primary": {"id": "sg-02794d7f8eaa42fea", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:security-group/sg-02794d7f8eaa42fea", "description": "Security group for DRA Agent Gateway", "egress.#": "1", "egress.0.cidr_blocks.#": "1", "egress.0.cidr_blocks.0": "0.0.0.0/0", "egress.0.description": "", "egress.0.from_port": "0", "egress.0.ipv6_cidr_blocks.#": "0", "egress.0.prefix_list_ids.#": "0", "egress.0.protocol": "-1", "egress.0.security_groups.#": "0", "egress.0.self": "false", "egress.0.to_port": "0", "id": "sg-02794d7f8eaa42fea", "ingress.#": "5", "ingress.0.cidr_blocks.#": "1", "ingress.0.cidr_blocks.0": "**********/22", "ingress.0.description": "", "ingress.0.from_port": "22", "ingress.0.ipv6_cidr_blocks.#": "0", "ingress.0.prefix_list_ids.#": "0", "ingress.0.protocol": "tcp", "ingress.0.security_groups.#": "0", "ingress.0.self": "false", "ingress.0.to_port": "22", "ingress.1.cidr_blocks.#": "1", "ingress.1.cidr_blocks.0": "**********/22", "ingress.1.description": "", "ingress.1.from_port": "2812", "ingress.1.ipv6_cidr_blocks.#": "0", "ingress.1.prefix_list_ids.#": "0", "ingress.1.protocol": "tcp", "ingress.1.security_groups.#": "0", "ingress.1.self": "false", "ingress.1.to_port": "2812", "ingress.2.cidr_blocks.#": "1", "ingress.2.cidr_blocks.0": "**********/22", "ingress.2.description": "", "ingress.2.from_port": "443", "ingress.2.ipv6_cidr_blocks.#": "0", "ingress.2.prefix_list_ids.#": "0", "ingress.2.protocol": "tcp", "ingress.2.security_groups.#": "0", "ingress.2.self": "false", "ingress.2.to_port": "443", "ingress.3.cidr_blocks.#": "1", "ingress.3.cidr_blocks.0": "**********/22", "ingress.3.description": "", "ingress.3.from_port": "514", "ingress.3.ipv6_cidr_blocks.#": "0", "ingress.3.prefix_list_ids.#": "0", "ingress.3.protocol": "udp", "ingress.3.security_groups.#": "0", "ingress.3.self": "false", "ingress.3.to_port": "514", "ingress.4.cidr_blocks.#": "1", "ingress.4.cidr_blocks.0": "**********/22", "ingress.4.description": "", "ingress.4.from_port": "8083", "ingress.4.ipv6_cidr_blocks.#": "0", "ingress.4.prefix_list_ids.#": "0", "ingress.4.protocol": "tcp", "ingress.4.security_groups.#": "0", "ingress.4.self": "false", "ingress.4.to_port": "8083", "name": "dra-agent-gateway-sg", "name_prefix": "", "owner_id": "046276255144", "tags.%": "2", "tags.Component": "DAM", "tags.Name": "dra-agent-gateway-sg", "tags_all.%": "2", "tags_all.Component": "DAM", "tags_all.Name": "dra-agent-gateway-sg", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_security_group.tfer--dra-gateway-dev-az1-sg_sg-03ee4d0a2a324d311": {"type": "aws_security_group", "depends_on": [], "primary": {"id": "sg-03ee4d0a2a324d311", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:security-group/sg-03ee4d0a2a324d311", "description": "Security group for DRA Gateway in AZ1", "egress.#": "2", "egress.0.cidr_blocks.#": "1", "egress.0.cidr_blocks.0": "0.0.0.0/0", "egress.0.description": "", "egress.0.from_port": "0", "egress.0.ipv6_cidr_blocks.#": "0", "egress.0.prefix_list_ids.#": "0", "egress.0.protocol": "-1", "egress.0.security_groups.#": "0", "egress.0.self": "false", "egress.0.to_port": "0", "egress.1.cidr_blocks.#": "1", "egress.1.cidr_blocks.0": "0.0.0.0/0", "egress.1.description": "", "egress.1.from_port": "443", "egress.1.ipv6_cidr_blocks.#": "0", "egress.1.prefix_list_ids.#": "0", "egress.1.protocol": "tcp", "egress.1.security_groups.#": "0", "egress.1.self": "false", "egress.1.to_port": "443", "id": "sg-03ee4d0a2a324d311", "ingress.#": "2", "ingress.0.cidr_blocks.#": "1", "ingress.0.cidr_blocks.0": "10.0.0.0/8", "ingress.0.description": "", "ingress.0.from_port": "22", "ingress.0.ipv6_cidr_blocks.#": "0", "ingress.0.prefix_list_ids.#": "0", "ingress.0.protocol": "tcp", "ingress.0.security_groups.#": "0", "ingress.0.self": "false", "ingress.0.to_port": "22", "ingress.1.cidr_blocks.#": "3", "ingress.1.cidr_blocks.0": "**********/26", "ingress.1.cidr_blocks.1": "*********/24", "ingress.1.cidr_blocks.2": "***********/24", "ingress.1.description": "", "ingress.1.from_port": "8084", "ingress.1.ipv6_cidr_blocks.#": "0", "ingress.1.prefix_list_ids.#": "0", "ingress.1.protocol": "tcp", "ingress.1.security_groups.#": "0", "ingress.1.self": "false", "ingress.1.to_port": "8084", "name": "dra-gateway-dev-az1-sg", "name_prefix": "", "owner_id": "046276255144", "tags.%": "1", "tags.Name": "dra-gateway-dev-az1-sg", "tags_all.%": "1", "tags_all.Name": "dra-gateway-dev-az1-sg", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_security_group.tfer--dra-gateway-dev-az2-sg_sg-09b7938cd606d28b2": {"type": "aws_security_group", "depends_on": [], "primary": {"id": "sg-09b7938cd606d28b2", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:security-group/sg-09b7938cd606d28b2", "description": "Security group for DRA Gateway in AZ2", "egress.#": "2", "egress.0.cidr_blocks.#": "1", "egress.0.cidr_blocks.0": "0.0.0.0/0", "egress.0.description": "", "egress.0.from_port": "0", "egress.0.ipv6_cidr_blocks.#": "0", "egress.0.prefix_list_ids.#": "0", "egress.0.protocol": "-1", "egress.0.security_groups.#": "0", "egress.0.self": "false", "egress.0.to_port": "0", "egress.1.cidr_blocks.#": "1", "egress.1.cidr_blocks.0": "0.0.0.0/0", "egress.1.description": "", "egress.1.from_port": "443", "egress.1.ipv6_cidr_blocks.#": "0", "egress.1.prefix_list_ids.#": "0", "egress.1.protocol": "tcp", "egress.1.security_groups.#": "0", "egress.1.self": "false", "egress.1.to_port": "443", "id": "sg-09b7938cd606d28b2", "ingress.#": "2", "ingress.0.cidr_blocks.#": "1", "ingress.0.cidr_blocks.0": "10.0.0.0/8", "ingress.0.description": "", "ingress.0.from_port": "22", "ingress.0.ipv6_cidr_blocks.#": "0", "ingress.0.prefix_list_ids.#": "0", "ingress.0.protocol": "tcp", "ingress.0.security_groups.#": "0", "ingress.0.self": "false", "ingress.0.to_port": "22", "ingress.1.cidr_blocks.#": "3", "ingress.1.cidr_blocks.0": "**********/26", "ingress.1.cidr_blocks.1": "*********/24", "ingress.1.cidr_blocks.2": "***********/24", "ingress.1.description": "", "ingress.1.from_port": "8084", "ingress.1.ipv6_cidr_blocks.#": "0", "ingress.1.prefix_list_ids.#": "0", "ingress.1.protocol": "tcp", "ingress.1.security_groups.#": "0", "ingress.1.self": "false", "ingress.1.to_port": "8084", "name": "dra-gateway-dev-az2-sg", "name_prefix": "", "owner_id": "046276255144", "tags.%": "1", "tags.Name": "dra-gateway-dev-az2-sg", "tags_all.%": "1", "tags_all.Name": "dra-gateway-dev-az2-sg", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_security_group.tfer--eks-cluster-sg-infovault-dev-eks-cluster-v130-**********_sg-0950397297ecb203f": {"type": "aws_security_group", "depends_on": [], "primary": {"id": "sg-0950397297ecb203f", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:security-group/sg-0950397297ecb203f", "description": "EKS created security group applied to ENI that is attached to EKS Control Plane master nodes, as well as any managed workloads.", "egress.#": "1", "egress.0.cidr_blocks.#": "1", "egress.0.cidr_blocks.0": "0.0.0.0/0", "egress.0.description": "", "egress.0.from_port": "0", "egress.0.ipv6_cidr_blocks.#": "0", "egress.0.prefix_list_ids.#": "0", "egress.0.protocol": "-1", "egress.0.security_groups.#": "0", "egress.0.self": "false", "egress.0.to_port": "0", "id": "sg-0950397297ecb203f", "ingress.#": "12", "ingress.0.cidr_blocks.#": "2", "ingress.0.cidr_blocks.0": "0.0.0.0/0", "ingress.0.cidr_blocks.1": "**********/24", "ingress.0.description": "", "ingress.0.from_port": "443", "ingress.0.ipv6_cidr_blocks.#": "0", "ingress.0.prefix_list_ids.#": "0", "ingress.0.protocol": "tcp", "ingress.0.security_groups.#": "0", "ingress.0.self": "false", "ingress.0.to_port": "443", "ingress.1.cidr_blocks.#": "1", "ingress.1.cidr_blocks.0": "**********/26", "ingress.1.description": "HTTPS from Management VPC", "ingress.1.from_port": "443", "ingress.1.ipv6_cidr_blocks.#": "0", "ingress.1.prefix_list_ids.#": "0", "ingress.1.protocol": "tcp", "ingress.1.security_groups.#": "0", "ingress.1.self": "false", "ingress.1.to_port": "443", "ingress.10.cidr_blocks.#": "0", "ingress.10.description": "EKS Cluster", "ingress.10.from_port": "0", "ingress.10.ipv6_cidr_blocks.#": "0", "ingress.10.prefix_list_ids.#": "0", "ingress.10.protocol": "-1", "ingress.10.security_groups.#": "0", "ingress.10.self": "true", "ingress.10.to_port": "0", "ingress.11.cidr_blocks.#": "0", "ingress.11.description": "ToolingServer-Win", "ingress.11.from_port": "1433", "ingress.11.ipv6_cidr_blocks.#": "0", "ingress.11.prefix_list_ids.#": "0", "ingress.11.protocol": "tcp", "ingress.11.security_groups.#": "1", "ingress.11.security_groups.0": "sg-095117b1a793c2ce0", "ingress.11.self": "false", "ingress.11.to_port": "1433", "ingress.2.cidr_blocks.#": "1", "ingress.2.cidr_blocks.0": "**********/16", "ingress.2.description": "", "ingress.2.from_port": "10000", "ingress.2.ipv6_cidr_blocks.#": "0", "ingress.2.prefix_list_ids.#": "0", "ingress.2.protocol": "tcp", "ingress.2.security_groups.#": "0", "ingress.2.self": "false", "ingress.2.to_port": "10000", "ingress.3.cidr_blocks.#": "1", "ingress.3.cidr_blocks.0": "**********/16", "ingress.3.description": "", "ingress.3.from_port": "1111", "ingress.3.ipv6_cidr_blocks.#": "0", "ingress.3.prefix_list_ids.#": "0", "ingress.3.protocol": "tcp", "ingress.3.security_groups.#": "0", "ingress.3.self": "false", "ingress.3.to_port": "1111", "ingress.4.cidr_blocks.#": "1", "ingress.4.cidr_blocks.0": "**********/16", "ingress.4.description": "", "ingress.4.from_port": "6379", "ingress.4.ipv6_cidr_blocks.#": "0", "ingress.4.prefix_list_ids.#": "0", "ingress.4.protocol": "tcp", "ingress.4.security_groups.#": "0", "ingress.4.self": "false", "ingress.4.to_port": "6379", "ingress.5.cidr_blocks.#": "1", "ingress.5.cidr_blocks.0": "**********/24", "ingress.5.description": "", "ingress.5.from_port": "80", "ingress.5.ipv6_cidr_blocks.#": "0", "ingress.5.prefix_list_ids.#": "0", "ingress.5.protocol": "tcp", "ingress.5.security_groups.#": "0", "ingress.5.self": "false", "ingress.5.to_port": "80", "ingress.6.cidr_blocks.#": "2", "ingress.6.cidr_blocks.0": "**********/24", "ingress.6.cidr_blocks.1": "**********/24", "ingress.6.description": "", "ingress.6.from_port": "1433", "ingress.6.ipv6_cidr_blocks.#": "0", "ingress.6.prefix_list_ids.#": "0", "ingress.6.protocol": "tcp", "ingress.6.security_groups.#": "0", "ingress.6.self": "false", "ingress.6.to_port": "1433", "ingress.7.cidr_blocks.#": "1", "ingress.7.cidr_blocks.0": "*************/32", "ingress.7.description": "SSH access from user IP", "ingress.7.from_port": "22", "ingress.7.ipv6_cidr_blocks.#": "0", "ingress.7.prefix_list_ids.#": "0", "ingress.7.protocol": "tcp", "ingress.7.security_groups.#": "0", "ingress.7.self": "false", "ingress.7.to_port": "22", "ingress.8.cidr_blocks.#": "1", "ingress.8.cidr_blocks.0": "*************/32", "ingress.8.description": "SSH access from Candor office", "ingress.8.from_port": "22", "ingress.8.ipv6_cidr_blocks.#": "0", "ingress.8.prefix_list_ids.#": "0", "ingress.8.protocol": "tcp", "ingress.8.security_groups.#": "0", "ingress.8.self": "false", "ingress.8.to_port": "22", "ingress.9.cidr_blocks.#": "1", "ingress.9.cidr_blocks.0": "************/32", "ingress.9.description": "SSH access from B-19 office", "ingress.9.from_port": "22", "ingress.9.ipv6_cidr_blocks.#": "0", "ingress.9.prefix_list_ids.#": "0", "ingress.9.protocol": "tcp", "ingress.9.security_groups.#": "0", "ingress.9.self": "false", "ingress.9.to_port": "22", "name": "eks-cluster-sg-infovault-dev-eks-cluster-v130-**********", "name_prefix": "", "owner_id": "046276255144", "tags.%": "2", "tags.Name": "eks-cluster-sg-infovault-dev-eks-cluster-v130-**********", "tags.kubernetes.io/cluster/infovault-dev-eks-cluster-v130": "owned", "tags_all.%": "2", "tags_all.Name": "eks-cluster-sg-infovault-dev-eks-cluster-v130-**********", "tags_all.kubernetes.io/cluster/infovault-dev-eks-cluster-v130": "owned", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_security_group.tfer--gen-alb-internet-facing-sg_sg-0c08c4e2aabf88592": {"type": "aws_security_group", "depends_on": [], "primary": {"id": "sg-0c08c4e2aabf88592", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:security-group/sg-0c08c4e2aabf88592", "description": "Security group for internet-facing ALB", "egress.#": "6", "egress.0.cidr_blocks.#": "1", "egress.0.cidr_blocks.0": "0.0.0.0/0", "egress.0.description": "", "egress.0.from_port": "0", "egress.0.ipv6_cidr_blocks.#": "0", "egress.0.prefix_list_ids.#": "0", "egress.0.protocol": "-1", "egress.0.security_groups.#": "0", "egress.0.self": "false", "egress.0.to_port": "0", "egress.1.cidr_blocks.#": "1", "egress.1.cidr_blocks.0": "**********/24", "egress.1.description": "", "egress.1.from_port": "443", "egress.1.ipv6_cidr_blocks.#": "0", "egress.1.prefix_list_ids.#": "0", "egress.1.protocol": "tcp", "egress.1.security_groups.#": "0", "egress.1.self": "false", "egress.1.to_port": "443", "egress.2.cidr_blocks.#": "1", "egress.2.cidr_blocks.0": "**********/24", "egress.2.description": "", "egress.2.from_port": "80", "egress.2.ipv6_cidr_blocks.#": "0", "egress.2.prefix_list_ids.#": "0", "egress.2.protocol": "tcp", "egress.2.security_groups.#": "0", "egress.2.self": "false", "egress.2.to_port": "80", "egress.3.cidr_blocks.#": "0", "egress.3.description": "", "egress.3.from_port": "30402", "egress.3.ipv6_cidr_blocks.#": "0", "egress.3.prefix_list_ids.#": "0", "egress.3.protocol": "tcp", "egress.3.security_groups.#": "1", "egress.3.security_groups.0": "sg-0353836b1b373e27d", "egress.3.self": "false", "egress.3.to_port": "30402", "egress.4.cidr_blocks.#": "0", "egress.4.description": "", "egress.4.from_port": "31460", "egress.4.ipv6_cidr_blocks.#": "0", "egress.4.prefix_list_ids.#": "0", "egress.4.protocol": "tcp", "egress.4.security_groups.#": "1", "egress.4.security_groups.0": "sg-0353836b1b373e27d", "egress.4.self": "false", "egress.4.to_port": "31460", "egress.5.cidr_blocks.#": "0", "egress.5.description": "", "egress.5.from_port": "31950", "egress.5.ipv6_cidr_blocks.#": "0", "egress.5.prefix_list_ids.#": "0", "egress.5.protocol": "tcp", "egress.5.security_groups.#": "1", "egress.5.security_groups.0": "sg-0353836b1b373e27d", "egress.5.self": "false", "egress.5.to_port": "31950", "id": "sg-0c08c4e2aabf88592", "ingress.#": "2", "ingress.0.cidr_blocks.#": "1", "ingress.0.cidr_blocks.0": "0.0.0.0/0", "ingress.0.description": "", "ingress.0.from_port": "443", "ingress.0.ipv6_cidr_blocks.#": "0", "ingress.0.prefix_list_ids.#": "0", "ingress.0.protocol": "tcp", "ingress.0.security_groups.#": "0", "ingress.0.self": "false", "ingress.0.to_port": "443", "ingress.1.cidr_blocks.#": "1", "ingress.1.cidr_blocks.0": "0.0.0.0/0", "ingress.1.description": "", "ingress.1.from_port": "80", "ingress.1.ipv6_cidr_blocks.#": "0", "ingress.1.prefix_list_ids.#": "0", "ingress.1.protocol": "tcp", "ingress.1.security_groups.#": "0", "ingress.1.self": "false", "ingress.1.to_port": "80", "name": "gen-alb-internet-facing-sg", "name_prefix": "", "owner_id": "046276255144", "tags.%": "4", "tags.AutoShutdown": "true", "tags.Environment": "dev", "tags.Name": "gen-alb-internet-facing-sg", "tags.Project": "infovault", "tags_all.%": "4", "tags_all.AutoShutdown": "true", "tags_all.Environment": "dev", "tags_all.Name": "gen-alb-internet-facing-sg", "tags_all.Project": "infovault", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_security_group.tfer--infovault-ad-sg_sg-03f591c0a35d3af71": {"type": "aws_security_group", "depends_on": [], "primary": {"id": "sg-03f591c0a35d3af71", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:security-group/sg-03f591c0a35d3af71", "description": "Security group for Active Directory server", "egress.#": "1", "egress.0.cidr_blocks.#": "1", "egress.0.cidr_blocks.0": "0.0.0.0/0", "egress.0.description": "", "egress.0.from_port": "0", "egress.0.ipv6_cidr_blocks.#": "0", "egress.0.prefix_list_ids.#": "0", "egress.0.protocol": "-1", "egress.0.security_groups.#": "0", "egress.0.self": "false", "egress.0.to_port": "0", "id": "sg-03f591c0a35d3af71", "ingress.#": "11", "ingress.0.cidr_blocks.#": "1", "ingress.0.cidr_blocks.0": "**********/26", "ingress.0.description": "", "ingress.0.from_port": "3268", "ingress.0.ipv6_cidr_blocks.#": "0", "ingress.0.prefix_list_ids.#": "0", "ingress.0.protocol": "tcp", "ingress.0.security_groups.#": "0", "ingress.0.self": "false", "ingress.0.to_port": "3268", "ingress.1.cidr_blocks.#": "1", "ingress.1.cidr_blocks.0": "**********/26", "ingress.1.description": "", "ingress.1.from_port": "3269", "ingress.1.ipv6_cidr_blocks.#": "0", "ingress.1.prefix_list_ids.#": "0", "ingress.1.protocol": "tcp", "ingress.1.security_groups.#": "0", "ingress.1.self": "false", "ingress.1.to_port": "3269", "ingress.10.cidr_blocks.#": "1", "ingress.10.cidr_blocks.0": "**********/26", "ingress.10.description": "", "ingress.10.from_port": "88", "ingress.10.ipv6_cidr_blocks.#": "0", "ingress.10.prefix_list_ids.#": "0", "ingress.10.protocol": "udp", "ingress.10.security_groups.#": "0", "ingress.10.self": "false", "ingress.10.to_port": "88", "ingress.2.cidr_blocks.#": "1", "ingress.2.cidr_blocks.0": "**********/26", "ingress.2.description": "", "ingress.2.from_port": "3389", "ingress.2.ipv6_cidr_blocks.#": "0", "ingress.2.prefix_list_ids.#": "0", "ingress.2.protocol": "tcp", "ingress.2.security_groups.#": "0", "ingress.2.self": "false", "ingress.2.to_port": "3389", "ingress.3.cidr_blocks.#": "1", "ingress.3.cidr_blocks.0": "**********/26", "ingress.3.description": "", "ingress.3.from_port": "389", "ingress.3.ipv6_cidr_blocks.#": "0", "ingress.3.prefix_list_ids.#": "0", "ingress.3.protocol": "tcp", "ingress.3.security_groups.#": "0", "ingress.3.self": "false", "ingress.3.to_port": "389", "ingress.4.cidr_blocks.#": "1", "ingress.4.cidr_blocks.0": "**********/26", "ingress.4.description": "", "ingress.4.from_port": "53", "ingress.4.ipv6_cidr_blocks.#": "0", "ingress.4.prefix_list_ids.#": "0", "ingress.4.protocol": "tcp", "ingress.4.security_groups.#": "0", "ingress.4.self": "false", "ingress.4.to_port": "53", "ingress.5.cidr_blocks.#": "1", "ingress.5.cidr_blocks.0": "**********/26", "ingress.5.description": "", "ingress.5.from_port": "53", "ingress.5.ipv6_cidr_blocks.#": "0", "ingress.5.prefix_list_ids.#": "0", "ingress.5.protocol": "udp", "ingress.5.security_groups.#": "0", "ingress.5.self": "false", "ingress.5.to_port": "53", "ingress.6.cidr_blocks.#": "1", "ingress.6.cidr_blocks.0": "**********/26", "ingress.6.description": "", "ingress.6.from_port": "5985", "ingress.6.ipv6_cidr_blocks.#": "0", "ingress.6.prefix_list_ids.#": "0", "ingress.6.protocol": "tcp", "ingress.6.security_groups.#": "0", "ingress.6.self": "false", "ingress.6.to_port": "5985", "ingress.7.cidr_blocks.#": "1", "ingress.7.cidr_blocks.0": "**********/26", "ingress.7.description": "", "ingress.7.from_port": "5986", "ingress.7.ipv6_cidr_blocks.#": "0", "ingress.7.prefix_list_ids.#": "0", "ingress.7.protocol": "tcp", "ingress.7.security_groups.#": "0", "ingress.7.self": "false", "ingress.7.to_port": "5986", "ingress.8.cidr_blocks.#": "1", "ingress.8.cidr_blocks.0": "**********/26", "ingress.8.description": "", "ingress.8.from_port": "636", "ingress.8.ipv6_cidr_blocks.#": "0", "ingress.8.prefix_list_ids.#": "0", "ingress.8.protocol": "tcp", "ingress.8.security_groups.#": "0", "ingress.8.self": "false", "ingress.8.to_port": "636", "ingress.9.cidr_blocks.#": "1", "ingress.9.cidr_blocks.0": "**********/26", "ingress.9.description": "", "ingress.9.from_port": "88", "ingress.9.ipv6_cidr_blocks.#": "0", "ingress.9.prefix_list_ids.#": "0", "ingress.9.protocol": "tcp", "ingress.9.security_groups.#": "0", "ingress.9.self": "false", "ingress.9.to_port": "88", "name": "infovault-ad-sg", "name_prefix": "", "owner_id": "046276255144", "tags.%": "0", "tags_all.%": "0", "vpc_id": "vpc-05f1b20a6634c6a38"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_security_group.tfer--infovault-ad-tooling-sg_sg-0511f8964219f7537": {"type": "aws_security_group", "depends_on": [], "primary": {"id": "sg-0511f8964219f7537", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:security-group/sg-0511f8964219f7537", "description": "Security group for Windows tooling server in AD subnet", "egress.#": "2", "egress.0.cidr_blocks.#": "1", "egress.0.cidr_blocks.0": "0.0.0.0/0", "egress.0.description": "", "egress.0.from_port": "0", "egress.0.ipv6_cidr_blocks.#": "0", "egress.0.prefix_list_ids.#": "0", "egress.0.protocol": "-1", "egress.0.security_groups.#": "0", "egress.0.self": "false", "egress.0.to_port": "0", "egress.1.cidr_blocks.#": "1", "egress.1.cidr_blocks.0": "0.0.0.0/0", "egress.1.description": "", "egress.1.from_port": "443", "egress.1.ipv6_cidr_blocks.#": "0", "egress.1.prefix_list_ids.#": "0", "egress.1.protocol": "tcp", "egress.1.security_groups.#": "0", "egress.1.self": "false", "egress.1.to_port": "443", "id": "sg-0511f8964219f7537", "ingress.#": "4", "ingress.0.cidr_blocks.#": "1", "ingress.0.cidr_blocks.0": "**********/26", "ingress.0.description": "", "ingress.0.from_port": "-1", "ingress.0.ipv6_cidr_blocks.#": "0", "ingress.0.prefix_list_ids.#": "0", "ingress.0.protocol": "icmp", "ingress.0.security_groups.#": "0", "ingress.0.self": "false", "ingress.0.to_port": "-1", "ingress.1.cidr_blocks.#": "1", "ingress.1.cidr_blocks.0": "**********/26", "ingress.1.description": "", "ingress.1.from_port": "3389", "ingress.1.ipv6_cidr_blocks.#": "0", "ingress.1.prefix_list_ids.#": "0", "ingress.1.protocol": "tcp", "ingress.1.security_groups.#": "0", "ingress.1.self": "false", "ingress.1.to_port": "3389", "ingress.2.cidr_blocks.#": "1", "ingress.2.cidr_blocks.0": "**********/26", "ingress.2.description": "", "ingress.2.from_port": "5985", "ingress.2.ipv6_cidr_blocks.#": "0", "ingress.2.prefix_list_ids.#": "0", "ingress.2.protocol": "tcp", "ingress.2.security_groups.#": "0", "ingress.2.self": "false", "ingress.2.to_port": "5985", "ingress.3.cidr_blocks.#": "1", "ingress.3.cidr_blocks.0": "**********/26", "ingress.3.description": "", "ingress.3.from_port": "5986", "ingress.3.ipv6_cidr_blocks.#": "0", "ingress.3.prefix_list_ids.#": "0", "ingress.3.protocol": "tcp", "ingress.3.security_groups.#": "0", "ingress.3.self": "false", "ingress.3.to_port": "5986", "name": "infovault-ad-tooling-sg", "name_prefix": "", "owner_id": "046276255144", "tags.%": "0", "tags_all.%": "0", "vpc_id": "vpc-05f1b20a6634c6a38"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_security_group.tfer--infovault-alb-sg_sg-0d3cb3a1b9bea45d6": {"type": "aws_security_group", "depends_on": [], "primary": {"id": "sg-0d3cb3a1b9bea45d6", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:security-group/sg-0d3cb3a1b9bea45d6", "description": "Security group for InfoVault ALB", "egress.#": "1", "egress.0.cidr_blocks.#": "1", "egress.0.cidr_blocks.0": "0.0.0.0/0", "egress.0.description": "", "egress.0.from_port": "0", "egress.0.ipv6_cidr_blocks.#": "0", "egress.0.prefix_list_ids.#": "0", "egress.0.protocol": "-1", "egress.0.security_groups.#": "0", "egress.0.self": "false", "egress.0.to_port": "0", "id": "sg-0d3cb3a1b9bea45d6", "ingress.#": "4", "ingress.0.cidr_blocks.#": "2", "ingress.0.cidr_blocks.0": "**********/24", "ingress.0.cidr_blocks.1": "**********/24", "ingress.0.description": "", "ingress.0.from_port": "10000", "ingress.0.ipv6_cidr_blocks.#": "0", "ingress.0.prefix_list_ids.#": "0", "ingress.0.protocol": "tcp", "ingress.0.security_groups.#": "0", "ingress.0.self": "false", "ingress.0.to_port": "10000", "ingress.1.cidr_blocks.#": "2", "ingress.1.cidr_blocks.0": "**********/24", "ingress.1.cidr_blocks.1": "**********/24", "ingress.1.description": "", "ingress.1.from_port": "1111", "ingress.1.ipv6_cidr_blocks.#": "0", "ingress.1.prefix_list_ids.#": "0", "ingress.1.protocol": "tcp", "ingress.1.security_groups.#": "0", "ingress.1.self": "false", "ingress.1.to_port": "1111", "ingress.2.cidr_blocks.#": "2", "ingress.2.cidr_blocks.0": "**********/24", "ingress.2.cidr_blocks.1": "**********/24", "ingress.2.description": "", "ingress.2.from_port": "1433", "ingress.2.ipv6_cidr_blocks.#": "0", "ingress.2.prefix_list_ids.#": "0", "ingress.2.protocol": "tcp", "ingress.2.security_groups.#": "0", "ingress.2.self": "false", "ingress.2.to_port": "1433", "ingress.3.cidr_blocks.#": "2", "ingress.3.cidr_blocks.0": "**********/24", "ingress.3.cidr_blocks.1": "**********/24", "ingress.3.description": "", "ingress.3.from_port": "6379", "ingress.3.ipv6_cidr_blocks.#": "0", "ingress.3.prefix_list_ids.#": "0", "ingress.3.protocol": "tcp", "ingress.3.security_groups.#": "0", "ingress.3.self": "false", "ingress.3.to_port": "6379", "name": "infovault-alb-sg", "name_prefix": "", "owner_id": "046276255144", "tags.%": "0", "tags_all.%": "0", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_security_group.tfer--infovault-app-tier-sg_sg-095c31fa3b94c41ae": {"type": "aws_security_group", "depends_on": [], "primary": {"id": "sg-095c31fa3b94c41ae", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:security-group/sg-095c31fa3b94c41ae", "description": "Security group for InfoVault application tier", "egress.#": "2", "egress.0.cidr_blocks.#": "1", "egress.0.cidr_blocks.0": "0.0.0.0/0", "egress.0.description": "", "egress.0.from_port": "0", "egress.0.ipv6_cidr_blocks.#": "0", "egress.0.prefix_list_ids.#": "0", "egress.0.protocol": "-1", "egress.0.security_groups.#": "0", "egress.0.self": "false", "egress.0.to_port": "0", "egress.1.cidr_blocks.#": "1", "egress.1.cidr_blocks.0": "0.0.0.0/0", "egress.1.description": "", "egress.1.from_port": "443", "egress.1.ipv6_cidr_blocks.#": "0", "egress.1.prefix_list_ids.#": "0", "egress.1.protocol": "tcp", "egress.1.security_groups.#": "0", "egress.1.self": "false", "egress.1.to_port": "443", "id": "sg-095c31fa3b94c41ae", "ingress.#": "10", "ingress.0.cidr_blocks.#": "1", "ingress.0.cidr_blocks.0": "0.0.0.0/0", "ingress.0.description": "", "ingress.0.from_port": "443", "ingress.0.ipv6_cidr_blocks.#": "0", "ingress.0.prefix_list_ids.#": "0", "ingress.0.protocol": "tcp", "ingress.0.security_groups.#": "0", "ingress.0.self": "false", "ingress.0.to_port": "443", "ingress.1.cidr_blocks.#": "2", "ingress.1.cidr_blocks.0": "10.0.0.0/8", "ingress.1.cidr_blocks.1": "100.0.0.0/8", "ingress.1.description": "", "ingress.1.from_port": "111", "ingress.1.ipv6_cidr_blocks.#": "0", "ingress.1.prefix_list_ids.#": "0", "ingress.1.protocol": "tcp", "ingress.1.security_groups.#": "1", "ingress.1.security_groups.0": "sg-0353836b1b373e27d", "ingress.1.self": "false", "ingress.1.to_port": "111", "ingress.2.cidr_blocks.#": "2", "ingress.2.cidr_blocks.0": "10.0.0.0/8", "ingress.2.cidr_blocks.1": "100.0.0.0/8", "ingress.2.description": "", "ingress.2.from_port": "2049", "ingress.2.ipv6_cidr_blocks.#": "0", "ingress.2.prefix_list_ids.#": "0", "ingress.2.protocol": "tcp", "ingress.2.security_groups.#": "1", "ingress.2.security_groups.0": "sg-0353836b1b373e27d", "ingress.2.self": "false", "ingress.2.to_port": "2049", "ingress.3.cidr_blocks.#": "2", "ingress.3.cidr_blocks.0": "10.0.0.0/8", "ingress.3.cidr_blocks.1": "*************/32", "ingress.3.description": "", "ingress.3.from_port": "22", "ingress.3.ipv6_cidr_blocks.#": "0", "ingress.3.prefix_list_ids.#": "0", "ingress.3.protocol": "tcp", "ingress.3.security_groups.#": "0", "ingress.3.self": "false", "ingress.3.to_port": "22", "ingress.4.cidr_blocks.#": "1", "ingress.4.cidr_blocks.0": "**********/16", "ingress.4.description": "NFS TCP from EKS", "ingress.4.from_port": "2049", "ingress.4.ipv6_cidr_blocks.#": "0", "ingress.4.prefix_list_ids.#": "0", "ingress.4.protocol": "tcp", "ingress.4.security_groups.#": "0", "ingress.4.self": "false", "ingress.4.to_port": "2049", "ingress.5.cidr_blocks.#": "1", "ingress.5.cidr_blocks.0": "**********/16", "ingress.5.description": "NFS UDP from EKS", "ingress.5.from_port": "2049", "ingress.5.ipv6_cidr_blocks.#": "0", "ingress.5.prefix_list_ids.#": "0", "ingress.5.protocol": "udp", "ingress.5.security_groups.#": "0", "ingress.5.self": "false", "ingress.5.to_port": "2049", "ingress.6.cidr_blocks.#": "1", "ingress.6.cidr_blocks.0": "**********/16", "ingress.6.description": "RPC Portmapper TCP from EKS", "ingress.6.from_port": "111", "ingress.6.ipv6_cidr_blocks.#": "0", "ingress.6.prefix_list_ids.#": "0", "ingress.6.protocol": "tcp", "ingress.6.security_groups.#": "0", "ingress.6.self": "false", "ingress.6.to_port": "111", "ingress.7.cidr_blocks.#": "1", "ingress.7.cidr_blocks.0": "**********/16", "ingress.7.description": "RPC Portmapper UDP from EKS", "ingress.7.from_port": "111", "ingress.7.ipv6_cidr_blocks.#": "0", "ingress.7.prefix_list_ids.#": "0", "ingress.7.protocol": "udp", "ingress.7.security_groups.#": "0", "ingress.7.self": "false", "ingress.7.to_port": "111", "ingress.8.cidr_blocks.#": "1", "ingress.8.cidr_blocks.0": "*************/32", "ingress.8.description": "SSH access from Candor office", "ingress.8.from_port": "22", "ingress.8.ipv6_cidr_blocks.#": "0", "ingress.8.prefix_list_ids.#": "0", "ingress.8.protocol": "tcp", "ingress.8.security_groups.#": "0", "ingress.8.self": "false", "ingress.8.to_port": "22", "ingress.9.cidr_blocks.#": "1", "ingress.9.cidr_blocks.0": "************/32", "ingress.9.description": "SSH access from B-19 office", "ingress.9.from_port": "22", "ingress.9.ipv6_cidr_blocks.#": "0", "ingress.9.prefix_list_ids.#": "0", "ingress.9.protocol": "tcp", "ingress.9.security_groups.#": "0", "ingress.9.self": "false", "ingress.9.to_port": "22", "name": "infovault-app-tier-sg", "name_prefix": "", "owner_id": "046276255144", "tags.%": "4", "tags.AutoShutdown": "true", "tags.Environment": "dev", "tags.Name": "infovault-app-tier-sg", "tags.Project": "InfoVault", "tags_all.%": "4", "tags_all.AutoShutdown": "true", "tags_all.Environment": "dev", "tags_all.Name": "infovault-app-tier-sg", "tags_all.Project": "InfoVault", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_security_group.tfer--infovault-dev-alb-sg_sg-0b9856c92519afb2f": {"type": "aws_security_group", "depends_on": [], "primary": {"id": "sg-0b9856c92519afb2f", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:security-group/sg-0b9856c92519afb2f", "description": "Security group for Application Load Balancer", "egress.#": "1", "egress.0.cidr_blocks.#": "1", "egress.0.cidr_blocks.0": "0.0.0.0/0", "egress.0.description": "Allow all outbound traffic", "egress.0.from_port": "0", "egress.0.ipv6_cidr_blocks.#": "0", "egress.0.prefix_list_ids.#": "0", "egress.0.protocol": "-1", "egress.0.security_groups.#": "0", "egress.0.self": "false", "egress.0.to_port": "0", "id": "sg-0b9856c92519afb2f", "ingress.#": "2", "ingress.0.cidr_blocks.#": "1", "ingress.0.cidr_blocks.0": "0.0.0.0/0", "ingress.0.description": "HTTP from anywhere", "ingress.0.from_port": "80", "ingress.0.ipv6_cidr_blocks.#": "0", "ingress.0.prefix_list_ids.#": "0", "ingress.0.protocol": "tcp", "ingress.0.security_groups.#": "0", "ingress.0.self": "false", "ingress.0.to_port": "80", "ingress.1.cidr_blocks.#": "1", "ingress.1.cidr_blocks.0": "0.0.0.0/0", "ingress.1.description": "HTTPS from anywhere", "ingress.1.from_port": "443", "ingress.1.ipv6_cidr_blocks.#": "0", "ingress.1.prefix_list_ids.#": "0", "ingress.1.protocol": "tcp", "ingress.1.security_groups.#": "0", "ingress.1.self": "false", "ingress.1.to_port": "443", "name": "infovault-dev-alb-sg", "name_prefix": "", "owner_id": "046276255144", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "infovault-dev-alb-sg", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "infovault-dev-alb-sg", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_id": "vpc-0e75036a2ad9e5b4f"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_security_group.tfer--infovault-dev-alb-sg_sg-0c2c1a0a80463a69b": {"type": "aws_security_group", "depends_on": [], "primary": {"id": "sg-0c2c1a0a80463a69b", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:security-group/sg-0c2c1a0a80463a69b", "description": "Security group for Application Load Balancer", "egress.#": "1", "egress.0.cidr_blocks.#": "1", "egress.0.cidr_blocks.0": "0.0.0.0/0", "egress.0.description": "Allow all outbound traffic", "egress.0.from_port": "0", "egress.0.ipv6_cidr_blocks.#": "0", "egress.0.prefix_list_ids.#": "0", "egress.0.protocol": "-1", "egress.0.security_groups.#": "0", "egress.0.self": "false", "egress.0.to_port": "0", "id": "sg-0c2c1a0a80463a69b", "ingress.#": "2", "ingress.0.cidr_blocks.#": "1", "ingress.0.cidr_blocks.0": "0.0.0.0/0", "ingress.0.description": "HTTP from anywhere", "ingress.0.from_port": "80", "ingress.0.ipv6_cidr_blocks.#": "0", "ingress.0.prefix_list_ids.#": "0", "ingress.0.protocol": "tcp", "ingress.0.security_groups.#": "0", "ingress.0.self": "false", "ingress.0.to_port": "80", "ingress.1.cidr_blocks.#": "1", "ingress.1.cidr_blocks.0": "0.0.0.0/0", "ingress.1.description": "HTTPS from anywhere", "ingress.1.from_port": "443", "ingress.1.ipv6_cidr_blocks.#": "0", "ingress.1.prefix_list_ids.#": "0", "ingress.1.protocol": "tcp", "ingress.1.security_groups.#": "0", "ingress.1.self": "false", "ingress.1.to_port": "443", "name": "infovault-dev-alb-sg", "name_prefix": "", "owner_id": "046276255144", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "infovault-dev-alb-sg", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "infovault-dev-alb-sg", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_id": "vpc-037f619602fa293a1"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_security_group.tfer--infovault-dev-alb-sg_sg-0e4c62110e9593bfe": {"type": "aws_security_group", "depends_on": [], "primary": {"id": "sg-0e4c62110e9593bfe", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:security-group/sg-0e4c62110e9593bfe", "description": "Security group for Application Load Balancer", "egress.#": "1", "egress.0.cidr_blocks.#": "1", "egress.0.cidr_blocks.0": "0.0.0.0/0", "egress.0.description": "Allow all outbound traffic", "egress.0.from_port": "0", "egress.0.ipv6_cidr_blocks.#": "0", "egress.0.prefix_list_ids.#": "0", "egress.0.protocol": "-1", "egress.0.security_groups.#": "0", "egress.0.self": "false", "egress.0.to_port": "0", "id": "sg-0e4c62110e9593bfe", "ingress.#": "2", "ingress.0.cidr_blocks.#": "1", "ingress.0.cidr_blocks.0": "0.0.0.0/0", "ingress.0.description": "HTTP from anywhere", "ingress.0.from_port": "80", "ingress.0.ipv6_cidr_blocks.#": "0", "ingress.0.prefix_list_ids.#": "0", "ingress.0.protocol": "tcp", "ingress.0.security_groups.#": "0", "ingress.0.self": "false", "ingress.0.to_port": "80", "ingress.1.cidr_blocks.#": "1", "ingress.1.cidr_blocks.0": "0.0.0.0/0", "ingress.1.description": "HTTPS from anywhere", "ingress.1.from_port": "443", "ingress.1.ipv6_cidr_blocks.#": "0", "ingress.1.prefix_list_ids.#": "0", "ingress.1.protocol": "tcp", "ingress.1.security_groups.#": "0", "ingress.1.self": "false", "ingress.1.to_port": "443", "name": "infovault-dev-alb-sg", "name_prefix": "", "owner_id": "046276255144", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "infovault-dev-alb-sg", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "infovault-dev-alb-sg", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_id": "vpc-05f1b20a6634c6a38"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_security_group.tfer--infovault-dev-alb-sg_sg-0e921258047172c67": {"type": "aws_security_group", "depends_on": [], "primary": {"id": "sg-0e921258047172c67", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:security-group/sg-0e921258047172c67", "description": "Security group for Application Load Balancer", "egress.#": "1", "egress.0.cidr_blocks.#": "1", "egress.0.cidr_blocks.0": "0.0.0.0/0", "egress.0.description": "Allow all outbound traffic", "egress.0.from_port": "0", "egress.0.ipv6_cidr_blocks.#": "0", "egress.0.prefix_list_ids.#": "0", "egress.0.protocol": "-1", "egress.0.security_groups.#": "0", "egress.0.self": "false", "egress.0.to_port": "0", "id": "sg-0e921258047172c67", "ingress.#": "3", "ingress.0.cidr_blocks.#": "1", "ingress.0.cidr_blocks.0": "0.0.0.0/0", "ingress.0.description": "HTTP from anywhere", "ingress.0.from_port": "80", "ingress.0.ipv6_cidr_blocks.#": "0", "ingress.0.prefix_list_ids.#": "0", "ingress.0.protocol": "tcp", "ingress.0.security_groups.#": "0", "ingress.0.self": "false", "ingress.0.to_port": "80", "ingress.1.cidr_blocks.#": "1", "ingress.1.cidr_blocks.0": "0.0.0.0/0", "ingress.1.description": "HTTPS from anywhere", "ingress.1.from_port": "443", "ingress.1.ipv6_cidr_blocks.#": "0", "ingress.1.prefix_list_ids.#": "0", "ingress.1.protocol": "tcp", "ingress.1.security_groups.#": "0", "ingress.1.self": "false", "ingress.1.to_port": "443", "ingress.2.cidr_blocks.#": "1", "ingress.2.cidr_blocks.0": "**********/26", "ingress.2.description": "Allow HTTPS from management VPC", "ingress.2.from_port": "443", "ingress.2.ipv6_cidr_blocks.#": "0", "ingress.2.prefix_list_ids.#": "0", "ingress.2.protocol": "tcp", "ingress.2.security_groups.#": "0", "ingress.2.self": "false", "ingress.2.to_port": "443", "name": "infovault-dev-alb-sg", "name_prefix": "", "owner_id": "046276255144", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "infovault-dev-alb-sg", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "infovault-dev-alb-sg", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_security_group.tfer--infovault-dev-anfw-endpoint-sg_sg-0015e747db8058575": {"type": "aws_security_group", "depends_on": [], "primary": {"id": "sg-0015e747db8058575", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:security-group/sg-0015e747db8058575", "description": "Security group for AWS Network Firewall endpoints", "egress.#": "1", "egress.0.cidr_blocks.#": "1", "egress.0.cidr_blocks.0": "0.0.0.0/0", "egress.0.description": "Allow all outbound traffic", "egress.0.from_port": "0", "egress.0.ipv6_cidr_blocks.#": "0", "egress.0.prefix_list_ids.#": "0", "egress.0.protocol": "-1", "egress.0.security_groups.#": "0", "egress.0.self": "false", "egress.0.to_port": "0", "id": "sg-0015e747db8058575", "ingress.#": "1", "ingress.0.cidr_blocks.#": "1", "ingress.0.cidr_blocks.0": "*********/26", "ingress.0.description": "Allow all inbound traffic", "ingress.0.from_port": "0", "ingress.0.ipv6_cidr_blocks.#": "0", "ingress.0.prefix_list_ids.#": "0", "ingress.0.protocol": "-1", "ingress.0.security_groups.#": "0", "ingress.0.self": "false", "ingress.0.to_port": "0", "name": "infovault-dev-anfw-endpoint-sg", "name_prefix": "", "owner_id": "046276255144", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "infovault-dev-anfw-endpoint-sg", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "infovault-dev-anfw-endpoint-sg", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_security_group.tfer--infovault-dev-anfw-endpoint-sg_sg-02dba0def0203d5a0": {"type": "aws_security_group", "depends_on": [], "primary": {"id": "sg-02dba0def0203d5a0", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:security-group/sg-02dba0def0203d5a0", "description": "Security group for AWS Network Firewall endpoints", "egress.#": "1", "egress.0.cidr_blocks.#": "1", "egress.0.cidr_blocks.0": "0.0.0.0/0", "egress.0.description": "Allow all outbound traffic", "egress.0.from_port": "0", "egress.0.ipv6_cidr_blocks.#": "0", "egress.0.prefix_list_ids.#": "0", "egress.0.protocol": "-1", "egress.0.security_groups.#": "0", "egress.0.self": "false", "egress.0.to_port": "0", "id": "sg-02dba0def0203d5a0", "ingress.#": "1", "ingress.0.cidr_blocks.#": "1", "ingress.0.cidr_blocks.0": "**********/26", "ingress.0.description": "Allow all inbound traffic", "ingress.0.from_port": "0", "ingress.0.ipv6_cidr_blocks.#": "0", "ingress.0.prefix_list_ids.#": "0", "ingress.0.protocol": "-1", "ingress.0.security_groups.#": "0", "ingress.0.self": "false", "ingress.0.to_port": "0", "name": "infovault-dev-anfw-endpoint-sg", "name_prefix": "", "owner_id": "046276255144", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "infovault-dev-anfw-endpoint-sg", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "infovault-dev-anfw-endpoint-sg", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_id": "vpc-05f1b20a6634c6a38"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_security_group.tfer--infovault-dev-anfw-endpoint-sg_sg-038c81aff399e6abb": {"type": "aws_security_group", "depends_on": [], "primary": {"id": "sg-038c81aff399e6abb", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:security-group/sg-038c81aff399e6abb", "description": "Security group for AWS Network Firewall endpoints", "egress.#": "1", "egress.0.cidr_blocks.#": "1", "egress.0.cidr_blocks.0": "0.0.0.0/0", "egress.0.description": "Allow all outbound traffic", "egress.0.from_port": "0", "egress.0.ipv6_cidr_blocks.#": "0", "egress.0.prefix_list_ids.#": "0", "egress.0.protocol": "-1", "egress.0.security_groups.#": "0", "egress.0.self": "false", "egress.0.to_port": "0", "id": "sg-038c81aff399e6abb", "ingress.#": "1", "ingress.0.cidr_blocks.#": "1", "ingress.0.cidr_blocks.0": "*********/27", "ingress.0.description": "Allow all inbound traffic", "ingress.0.from_port": "0", "ingress.0.ipv6_cidr_blocks.#": "0", "ingress.0.prefix_list_ids.#": "0", "ingress.0.protocol": "-1", "ingress.0.security_groups.#": "0", "ingress.0.self": "false", "ingress.0.to_port": "0", "name": "infovault-dev-anfw-endpoint-sg", "name_prefix": "", "owner_id": "046276255144", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "infovault-dev-anfw-endpoint-sg", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "infovault-dev-anfw-endpoint-sg", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_id": "vpc-0e75036a2ad9e5b4f"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_security_group.tfer--infovault-dev-anfw-endpoint-sg_sg-0e2bbdad5eddfe99c": {"type": "aws_security_group", "depends_on": [], "primary": {"id": "sg-0e2bbdad5eddfe99c", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:security-group/sg-0e2bbdad5eddfe99c", "description": "Security group for AWS Network Firewall endpoints", "egress.#": "1", "egress.0.cidr_blocks.#": "1", "egress.0.cidr_blocks.0": "0.0.0.0/0", "egress.0.description": "Allow all outbound traffic", "egress.0.from_port": "0", "egress.0.ipv6_cidr_blocks.#": "0", "egress.0.prefix_list_ids.#": "0", "egress.0.protocol": "-1", "egress.0.security_groups.#": "0", "egress.0.self": "false", "egress.0.to_port": "0", "id": "sg-0e2bbdad5eddfe99c", "ingress.#": "1", "ingress.0.cidr_blocks.#": "1", "ingress.0.cidr_blocks.0": "**********/27", "ingress.0.description": "Allow all inbound traffic", "ingress.0.from_port": "0", "ingress.0.ipv6_cidr_blocks.#": "0", "ingress.0.prefix_list_ids.#": "0", "ingress.0.protocol": "-1", "ingress.0.security_groups.#": "0", "ingress.0.self": "false", "ingress.0.to_port": "0", "name": "infovault-dev-anfw-endpoint-sg", "name_prefix": "", "owner_id": "046276255144", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "dev-sg-net-anfw-endpoint", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "dev-sg-net-anfw-endpoint", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_id": "vpc-037f619602fa293a1"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_security_group.tfer--infovault-dev-gen-facing-compartment-alb-sg_sg-0acb991f6917ba443": {"type": "aws_security_group", "depends_on": [], "primary": {"id": "sg-0acb991f6917ba443", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:security-group/sg-0acb991f6917ba443", "description": "Allow specific traffic for ALB in infovault-dev-gen-facing-compartment", "egress.#": "1", "egress.0.cidr_blocks.#": "1", "egress.0.cidr_blocks.0": "0.0.0.0/0", "egress.0.description": "", "egress.0.from_port": "0", "egress.0.ipv6_cidr_blocks.#": "0", "egress.0.prefix_list_ids.#": "0", "egress.0.protocol": "-1", "egress.0.security_groups.#": "0", "egress.0.self": "false", "egress.0.to_port": "0", "id": "sg-0acb991f6917ba443", "ingress.#": "2", "ingress.0.cidr_blocks.#": "1", "ingress.0.cidr_blocks.0": "0.0.0.0/0", "ingress.0.description": "", "ingress.0.from_port": "443", "ingress.0.ipv6_cidr_blocks.#": "0", "ingress.0.prefix_list_ids.#": "0", "ingress.0.protocol": "tcp", "ingress.0.security_groups.#": "0", "ingress.0.self": "false", "ingress.0.to_port": "443", "ingress.1.cidr_blocks.#": "1", "ingress.1.cidr_blocks.0": "0.0.0.0/0", "ingress.1.description": "", "ingress.1.from_port": "80", "ingress.1.ipv6_cidr_blocks.#": "0", "ingress.1.prefix_list_ids.#": "0", "ingress.1.protocol": "tcp", "ingress.1.security_groups.#": "0", "ingress.1.self": "false", "ingress.1.to_port": "80", "name": "infovault-dev-gen-facing-compartment-alb-sg", "name_prefix": "", "owner_id": "046276255144", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "infovault-dev-gen-facing-compartment-alb-sg", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "infovault-dev-gen-facing-compartment-alb-sg", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_security_group.tfer--infovault-dev-gen-facing-compartment-squid-sg_sg-011e29a277dde9f4a": {"type": "aws_security_group", "depends_on": [], "primary": {"id": "sg-011e29a277dde9f4a", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:security-group/sg-011e29a277dde9f4a", "description": "Allow specific traffic for Squid Proxy in infovault-dev-gen-facing-compartment", "egress.#": "1", "egress.0.cidr_blocks.#": "1", "egress.0.cidr_blocks.0": "0.0.0.0/0", "egress.0.description": "", "egress.0.from_port": "0", "egress.0.ipv6_cidr_blocks.#": "0", "egress.0.prefix_list_ids.#": "0", "egress.0.protocol": "-1", "egress.0.security_groups.#": "0", "egress.0.self": "false", "egress.0.to_port": "0", "id": "sg-011e29a277dde9f4a", "ingress.#": "3", "ingress.0.cidr_blocks.#": "1", "ingress.0.cidr_blocks.0": "*********/26", "ingress.0.description": "Squid Proxy from GEN-facing VPC", "ingress.0.from_port": "3128", "ingress.0.ipv6_cidr_blocks.#": "0", "ingress.0.prefix_list_ids.#": "0", "ingress.0.protocol": "tcp", "ingress.0.security_groups.#": "0", "ingress.0.self": "false", "ingress.0.to_port": "3128", "ingress.1.cidr_blocks.#": "1", "ingress.1.cidr_blocks.0": "*********/27", "ingress.1.description": "Squid Proxy from ABLRHF VPC", "ingress.1.from_port": "3128", "ingress.1.ipv6_cidr_blocks.#": "0", "ingress.1.prefix_list_ids.#": "0", "ingress.1.protocol": "tcp", "ingress.1.security_groups.#": "0", "ingress.1.self": "false", "ingress.1.to_port": "3128", "ingress.2.cidr_blocks.#": "1", "ingress.2.cidr_blocks.0": "**********/22", "ingress.2.description": "Squid Proxy from App-DB VPC", "ingress.2.from_port": "3128", "ingress.2.ipv6_cidr_blocks.#": "0", "ingress.2.prefix_list_ids.#": "0", "ingress.2.protocol": "tcp", "ingress.2.security_groups.#": "0", "ingress.2.self": "false", "ingress.2.to_port": "3128", "name": "infovault-dev-gen-facing-compartment-squid-sg", "name_prefix": "", "owner_id": "046276255144", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "infovault-dev-gen-facing-compartment-squid-sg", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "infovault-dev-gen-facing-compartment-squid-sg", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_security_group.tfer--infovault-dev-gen-facing-compartment-stfp-sg_sg-06936b8418430a397": {"type": "aws_security_group", "depends_on": [], "primary": {"id": "sg-06936b8418430a397", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:security-group/sg-06936b8418430a397", "description": "Allow specific traffic for STFP Server in infovault-dev-gen-facing-compartment", "egress.#": "1", "egress.0.cidr_blocks.#": "1", "egress.0.cidr_blocks.0": "0.0.0.0/0", "egress.0.description": "", "egress.0.from_port": "0", "egress.0.ipv6_cidr_blocks.#": "0", "egress.0.prefix_list_ids.#": "0", "egress.0.protocol": "-1", "egress.0.security_groups.#": "0", "egress.0.self": "false", "egress.0.to_port": "0", "id": "sg-06936b8418430a397", "ingress.#": "1", "ingress.0.cidr_blocks.#": "0", "ingress.0.description": "Allow SFTP from ALB SG", "ingress.0.from_port": "22", "ingress.0.ipv6_cidr_blocks.#": "0", "ingress.0.prefix_list_ids.#": "0", "ingress.0.protocol": "tcp", "ingress.0.security_groups.#": "1", "ingress.0.security_groups.0": "sg-0acb991f6917ba443", "ingress.0.self": "false", "ingress.0.to_port": "22", "name": "infovault-dev-gen-facing-compartment-stfp-sg", "name_prefix": "", "owner_id": "046276255144", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "infovault-dev-gen-facing-compartment-stfp-sg", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "infovault-dev-gen-facing-compartment-stfp-sg", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_security_group.tfer--infovault-dev-gen-facing-compartment-vpce-sg_sg-066975c77a66a64d9": {"type": "aws_security_group", "depends_on": [], "primary": {"id": "sg-066975c77a66a64d9", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:security-group/sg-066975c77a66a64d9", "description": "Allow HTTPS for VPC Endpoints in infovault-dev-gen-facing-compartment", "egress.#": "1", "egress.0.cidr_blocks.#": "1", "egress.0.cidr_blocks.0": "0.0.0.0/0", "egress.0.description": "", "egress.0.from_port": "0", "egress.0.ipv6_cidr_blocks.#": "0", "egress.0.prefix_list_ids.#": "0", "egress.0.protocol": "-1", "egress.0.security_groups.#": "0", "egress.0.self": "false", "egress.0.to_port": "0", "id": "sg-066975c77a66a64d9", "ingress.#": "1", "ingress.0.cidr_blocks.#": "2", "ingress.0.cidr_blocks.0": "*********/26", "ingress.0.cidr_blocks.1": "**********/22", "ingress.0.description": "", "ingress.0.from_port": "443", "ingress.0.ipv6_cidr_blocks.#": "0", "ingress.0.prefix_list_ids.#": "0", "ingress.0.protocol": "tcp", "ingress.0.security_groups.#": "0", "ingress.0.self": "false", "ingress.0.to_port": "443", "name": "infovault-dev-gen-facing-compartment-vpce-sg", "name_prefix": "", "owner_id": "046276255144", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "infovault-dev-gen-facing-compartment-vpce-sg", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "infovault-dev-gen-facing-compartment-vpce-sg", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_security_group.tfer--infovault-dev-rds-sg_sg-02f66e75b6d635108": {"type": "aws_security_group", "depends_on": [], "primary": {"id": "sg-02f66e75b6d635108", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:security-group/sg-02f66e75b6d635108", "description": "Security group for RDS instances", "egress.#": "1", "egress.0.cidr_blocks.#": "1", "egress.0.cidr_blocks.0": "0.0.0.0/0", "egress.0.description": "Allow all outbound traffic", "egress.0.from_port": "0", "egress.0.ipv6_cidr_blocks.#": "0", "egress.0.prefix_list_ids.#": "0", "egress.0.protocol": "-1", "egress.0.security_groups.#": "0", "egress.0.self": "false", "egress.0.to_port": "0", "id": "sg-02f66e75b6d635108", "ingress.#": "0", "name": "infovault-dev-rds-sg", "name_prefix": "", "owner_id": "046276255144", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "infovault-dev-rds-sg", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "infovault-dev-rds-sg", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_security_group.tfer--infovault-dev-rds-sg_sg-09780ff2c4d7753e1": {"type": "aws_security_group", "depends_on": [], "primary": {"id": "sg-09780ff2c4d7753e1", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:security-group/sg-09780ff2c4d7753e1", "description": "Security group for RDS instances", "egress.#": "1", "egress.0.cidr_blocks.#": "1", "egress.0.cidr_blocks.0": "0.0.0.0/0", "egress.0.description": "Allow all outbound traffic", "egress.0.from_port": "0", "egress.0.ipv6_cidr_blocks.#": "0", "egress.0.prefix_list_ids.#": "0", "egress.0.protocol": "-1", "egress.0.security_groups.#": "0", "egress.0.self": "false", "egress.0.to_port": "0", "id": "sg-09780ff2c4d7753e1", "ingress.#": "0", "name": "infovault-dev-rds-sg", "name_prefix": "", "owner_id": "046276255144", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "infovault-dev-rds-sg", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "infovault-dev-rds-sg", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_id": "vpc-0e75036a2ad9e5b4f"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_security_group.tfer--infovault-dev-rds-sg_sg-0dd095db031d21cf3": {"type": "aws_security_group", "depends_on": [], "primary": {"id": "sg-0dd095db031d21cf3", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:security-group/sg-0dd095db031d21cf3", "description": "Security group for RDS instances", "egress.#": "1", "egress.0.cidr_blocks.#": "1", "egress.0.cidr_blocks.0": "0.0.0.0/0", "egress.0.description": "Allow all outbound traffic", "egress.0.from_port": "0", "egress.0.ipv6_cidr_blocks.#": "0", "egress.0.prefix_list_ids.#": "0", "egress.0.protocol": "-1", "egress.0.security_groups.#": "0", "egress.0.self": "false", "egress.0.to_port": "0", "id": "sg-0dd095db031d21cf3", "ingress.#": "0", "name": "infovault-dev-rds-sg", "name_prefix": "", "owner_id": "046276255144", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "infovault-dev-rds-sg", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "infovault-dev-rds-sg", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_id": "vpc-05f1b20a6634c6a38"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_security_group.tfer--infovault-dev-rds-sg_sg-0e904d365617c1e2d": {"type": "aws_security_group", "depends_on": [], "primary": {"id": "sg-0e904d365617c1e2d", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:security-group/sg-0e904d365617c1e2d", "description": "Security group for RDS instances", "egress.#": "1", "egress.0.cidr_blocks.#": "1", "egress.0.cidr_blocks.0": "0.0.0.0/0", "egress.0.description": "Allow all outbound traffic", "egress.0.from_port": "0", "egress.0.ipv6_cidr_blocks.#": "0", "egress.0.prefix_list_ids.#": "0", "egress.0.protocol": "-1", "egress.0.security_groups.#": "0", "egress.0.self": "false", "egress.0.to_port": "0", "id": "sg-0e904d365617c1e2d", "ingress.#": "0", "name": "infovault-dev-rds-sg", "name_prefix": "", "owner_id": "046276255144", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "infovault-dev-rds-sg", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "infovault-dev-rds-sg", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_id": "vpc-037f619602fa293a1"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_security_group.tfer--infovault-eks-cluster-sg_sg-0353836b1b373e27d": {"type": "aws_security_group", "depends_on": [], "primary": {"id": "sg-0353836b1b373e27d", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:security-group/sg-0353836b1b373e27d", "description": "Security group for EKS cluster control plane", "egress.#": "1", "egress.0.cidr_blocks.#": "1", "egress.0.cidr_blocks.0": "0.0.0.0/0", "egress.0.description": "", "egress.0.from_port": "0", "egress.0.ipv6_cidr_blocks.#": "0", "egress.0.prefix_list_ids.#": "0", "egress.0.protocol": "-1", "egress.0.security_groups.#": "0", "egress.0.self": "false", "egress.0.to_port": "0", "id": "sg-0353836b1b373e27d", "ingress.#": "20", "ingress.0.cidr_blocks.#": "1", "ingress.0.cidr_blocks.0": "0.0.0.0/0", "ingress.0.description": "", "ingress.0.from_port": "22", "ingress.0.ipv6_cidr_blocks.#": "0", "ingress.0.prefix_list_ids.#": "0", "ingress.0.protocol": "tcp", "ingress.0.security_groups.#": "0", "ingress.0.self": "false", "ingress.0.to_port": "22", "ingress.1.cidr_blocks.#": "1", "ingress.1.cidr_blocks.0": "**********/26", "ingress.1.description": "Allow NodePort access from management server", "ingress.1.from_port": "30000", "ingress.1.ipv6_cidr_blocks.#": "0", "ingress.1.prefix_list_ids.#": "0", "ingress.1.protocol": "tcp", "ingress.1.security_groups.#": "0", "ingress.1.self": "false", "ingress.1.to_port": "33000", "ingress.10.cidr_blocks.#": "0", "ingress.10.description": "", "ingress.10.from_port": "31460", "ingress.10.ipv6_cidr_blocks.#": "0", "ingress.10.prefix_list_ids.#": "0", "ingress.10.protocol": "tcp", "ingress.10.security_groups.#": "1", "ingress.10.security_groups.0": "sg-0c08c4e2aabf88592", "ingress.10.self": "false", "ingress.10.to_port": "31460", "ingress.11.cidr_blocks.#": "0", "ingress.11.description": "", "ingress.11.from_port": "31950", "ingress.11.ipv6_cidr_blocks.#": "0", "ingress.11.prefix_list_ids.#": "0", "ingress.11.protocol": "tcp", "ingress.11.security_groups.#": "1", "ingress.11.security_groups.0": "sg-0c08c4e2aabf88592", "ingress.11.self": "false", "ingress.11.to_port": "31950", "ingress.12.cidr_blocks.#": "0", "ingress.12.description": "Allow Automation Studio NodePort access from Windows management server", "ingress.12.from_port": "31798", "ingress.12.ipv6_cidr_blocks.#": "0", "ingress.12.prefix_list_ids.#": "0", "ingress.12.protocol": "tcp", "ingress.12.security_groups.#": "1", "ingress.12.security_groups.0": "sg-095117b1a793c2ce0", "ingress.12.self": "false", "ingress.12.to_port": "31798", "ingress.13.cidr_blocks.#": "0", "ingress.13.description": "Allow HTTP traffic from internet-facing ALB to Automation Studio application", "ingress.13.from_port": "31798", "ingress.13.ipv6_cidr_blocks.#": "0", "ingress.13.prefix_list_ids.#": "0", "ingress.13.protocol": "tcp", "ingress.13.security_groups.#": "1", "ingress.13.security_groups.0": "sg-0c08c4e2aabf88592", "ingress.13.self": "false", "ingress.13.to_port": "31798", "ingress.14.cidr_blocks.#": "0", "ingress.14.description": "Allow HTTP traffic from internet-facing ALB to RMS application", "ingress.14.from_port": "31697", "ingress.14.ipv6_cidr_blocks.#": "0", "ingress.14.prefix_list_ids.#": "0", "ingress.14.protocol": "tcp", "ingress.14.security_groups.#": "1", "ingress.14.security_groups.0": "sg-0c08c4e2aabf88592", "ingress.14.self": "false", "ingress.14.to_port": "31697", "ingress.15.cidr_blocks.#": "0", "ingress.15.description": "Allow HTTP traffic from internet-facing ALB to Workspace Studio application", "ingress.15.from_port": "31670", "ingress.15.ipv6_cidr_blocks.#": "0", "ingress.15.prefix_list_ids.#": "0", "ingress.15.protocol": "tcp", "ingress.15.security_groups.#": "1", "ingress.15.security_groups.0": "sg-0c08c4e2aabf88592", "ingress.15.self": "false", "ingress.15.to_port": "31670", "ingress.16.cidr_blocks.#": "0", "ingress.16.description": "Allow RMS Web NodePort access from Windows management server", "ingress.16.from_port": "31697", "ingress.16.ipv6_cidr_blocks.#": "0", "ingress.16.prefix_list_ids.#": "0", "ingress.16.protocol": "tcp", "ingress.16.security_groups.#": "1", "ingress.16.security_groups.0": "sg-095117b1a793c2ce0", "ingress.16.self": "false", "ingress.16.to_port": "31697", "ingress.17.cidr_blocks.#": "0", "ingress.17.description": "Allow Workspace Studio NodePort access from Windows management server", "ingress.17.from_port": "31670", "ingress.17.ipv6_cidr_blocks.#": "0", "ingress.17.prefix_list_ids.#": "0", "ingress.17.protocol": "tcp", "ingress.17.security_groups.#": "1", "ingress.17.security_groups.0": "sg-095117b1a793c2ce0", "ingress.17.self": "false", "ingress.17.to_port": "31670", "ingress.18.cidr_blocks.#": "0", "ingress.18.description": "ToolingServer-Win", "ingress.18.from_port": "443", "ingress.18.ipv6_cidr_blocks.#": "0", "ingress.18.prefix_list_ids.#": "0", "ingress.18.protocol": "tcp", "ingress.18.security_groups.#": "1", "ingress.18.security_groups.0": "sg-095117b1a793c2ce0", "ingress.18.self": "false", "ingress.18.to_port": "443", "ingress.19.cidr_blocks.#": "0", "ingress.19.description": "ToolingServer-Win", "ingress.19.from_port": "80", "ingress.19.ipv6_cidr_blocks.#": "0", "ingress.19.prefix_list_ids.#": "0", "ingress.19.protocol": "tcp", "ingress.19.security_groups.#": "1", "ingress.19.security_groups.0": "sg-095117b1a793c2ce0", "ingress.19.self": "false", "ingress.19.to_port": "80", "ingress.2.cidr_blocks.#": "1", "ingress.2.cidr_blocks.0": "**********/26", "ingress.2.description": "Allow access from management server", "ingress.2.from_port": "8080", "ingress.2.ipv6_cidr_blocks.#": "0", "ingress.2.prefix_list_ids.#": "0", "ingress.2.protocol": "tcp", "ingress.2.security_groups.#": "0", "ingress.2.self": "false", "ingress.2.to_port": "8080", "ingress.3.cidr_blocks.#": "1", "ingress.3.cidr_blocks.0": "**********/24", "ingress.3.description": "", "ingress.3.from_port": "443", "ingress.3.ipv6_cidr_blocks.#": "0", "ingress.3.prefix_list_ids.#": "0", "ingress.3.protocol": "tcp", "ingress.3.security_groups.#": "0", "ingress.3.self": "false", "ingress.3.to_port": "443", "ingress.4.cidr_blocks.#": "1", "ingress.4.cidr_blocks.0": "**********/24", "ingress.4.description": "", "ingress.4.from_port": "80", "ingress.4.ipv6_cidr_blocks.#": "0", "ingress.4.prefix_list_ids.#": "0", "ingress.4.protocol": "tcp", "ingress.4.security_groups.#": "0", "ingress.4.self": "false", "ingress.4.to_port": "80", "ingress.5.cidr_blocks.#": "2", "ingress.5.cidr_blocks.0": "**********/26", "ingress.5.cidr_blocks.1": "***********/26", "ingress.5.description": "", "ingress.5.from_port": "10000", "ingress.5.ipv6_cidr_blocks.#": "0", "ingress.5.prefix_list_ids.#": "0", "ingress.5.protocol": "tcp", "ingress.5.security_groups.#": "1", "ingress.5.security_groups.0": "sg-0d3cb3a1b9bea45d6", "ingress.5.self": "false", "ingress.5.to_port": "10000", "ingress.6.cidr_blocks.#": "2", "ingress.6.cidr_blocks.0": "**********/26", "ingress.6.cidr_blocks.1": "***********/26", "ingress.6.description": "", "ingress.6.from_port": "1111", "ingress.6.ipv6_cidr_blocks.#": "0", "ingress.6.prefix_list_ids.#": "0", "ingress.6.protocol": "tcp", "ingress.6.security_groups.#": "1", "ingress.6.security_groups.0": "sg-0d3cb3a1b9bea45d6", "ingress.6.self": "false", "ingress.6.to_port": "1111", "ingress.7.cidr_blocks.#": "2", "ingress.7.cidr_blocks.0": "**********/26", "ingress.7.cidr_blocks.1": "***********/26", "ingress.7.description": "", "ingress.7.from_port": "6379", "ingress.7.ipv6_cidr_blocks.#": "0", "ingress.7.prefix_list_ids.#": "0", "ingress.7.protocol": "tcp", "ingress.7.security_groups.#": "1", "ingress.7.security_groups.0": "sg-0d3cb3a1b9bea45d6", "ingress.7.self": "false", "ingress.7.to_port": "6379", "ingress.8.cidr_blocks.#": "0", "ingress.8.description": "", "ingress.8.from_port": "0", "ingress.8.ipv6_cidr_blocks.#": "0", "ingress.8.prefix_list_ids.#": "0", "ingress.8.protocol": "-1", "ingress.8.security_groups.#": "1", "ingress.8.security_groups.0": "sg-0950397297ecb203f", "ingress.8.self": "true", "ingress.8.to_port": "0", "ingress.9.cidr_blocks.#": "0", "ingress.9.description": "", "ingress.9.from_port": "30402", "ingress.9.ipv6_cidr_blocks.#": "0", "ingress.9.prefix_list_ids.#": "0", "ingress.9.protocol": "tcp", "ingress.9.security_groups.#": "1", "ingress.9.security_groups.0": "sg-0c08c4e2aabf88592", "ingress.9.self": "false", "ingress.9.to_port": "30402", "name": "infovault-eks-cluster-sg", "name_prefix": "", "owner_id": "046276255144", "tags.%": "5", "tags.AutoShutdown": "true", "tags.Environment": "dev", "tags.Name": "infovault-eks-cluster-sg", "tags.Owner": "<PERSON><PERSON>", "tags.Project": "InfoVault", "tags_all.%": "5", "tags_all.AutoShutdown": "true", "tags_all.Environment": "dev", "tags_all.Name": "infovault-eks-cluster-sg", "tags_all.Owner": "<PERSON><PERSON>", "tags_all.Project": "InfoVault", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_security_group.tfer--infovault-eks-node-sg_sg-05f09a0dc7ab8c53f": {"type": "aws_security_group", "depends_on": [], "primary": {"id": "sg-05f09a0dc7ab8c53f", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:security-group/sg-05f09a0dc7ab8c53f", "description": "Security group for EKS worker nodes", "egress.#": "1", "egress.0.cidr_blocks.#": "1", "egress.0.cidr_blocks.0": "0.0.0.0/0", "egress.0.description": "", "egress.0.from_port": "0", "egress.0.ipv6_cidr_blocks.#": "0", "egress.0.prefix_list_ids.#": "0", "egress.0.protocol": "-1", "egress.0.security_groups.#": "0", "egress.0.self": "false", "egress.0.to_port": "0", "id": "sg-05f09a0dc7ab8c53f", "ingress.#": "2", "ingress.0.cidr_blocks.#": "1", "ingress.0.cidr_blocks.0": "0.0.0.0/0", "ingress.0.description": "", "ingress.0.from_port": "22", "ingress.0.ipv6_cidr_blocks.#": "0", "ingress.0.prefix_list_ids.#": "0", "ingress.0.protocol": "tcp", "ingress.0.security_groups.#": "0", "ingress.0.self": "false", "ingress.0.to_port": "22", "ingress.1.cidr_blocks.#": "0", "ingress.1.description": "", "ingress.1.from_port": "0", "ingress.1.ipv6_cidr_blocks.#": "0", "ingress.1.prefix_list_ids.#": "0", "ingress.1.protocol": "-1", "ingress.1.security_groups.#": "1", "ingress.1.security_groups.0": "sg-0353836b1b373e27d", "ingress.1.self": "true", "ingress.1.to_port": "0", "name": "infovault-eks-node-sg", "name_prefix": "", "owner_id": "046276255144", "tags.%": "5", "tags.AutoShutdown": "true", "tags.Environment": "dev", "tags.Name": "infovault-eks-node-sg", "tags.Owner": "<PERSON><PERSON>", "tags.Project": "InfoVault", "tags_all.%": "5", "tags_all.AutoShutdown": "true", "tags_all.Environment": "dev", "tags_all.Name": "infovault-eks-node-sg", "tags_all.Owner": "<PERSON><PERSON>", "tags_all.Project": "InfoVault", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_security_group.tfer--infovault-mgmt-gitlab-sg_sg-00df94fe66c7464f5": {"type": "aws_security_group", "depends_on": [], "primary": {"id": "sg-00df94fe66c7464f5", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:security-group/sg-00df94fe66c7464f5", "description": "Security group for GitLab Runner", "egress.#": "3", "egress.0.cidr_blocks.#": "1", "egress.0.cidr_blocks.0": "0.0.0.0/0", "egress.0.description": "HTTP", "egress.0.from_port": "80", "egress.0.ipv6_cidr_blocks.#": "0", "egress.0.prefix_list_ids.#": "0", "egress.0.protocol": "tcp", "egress.0.security_groups.#": "0", "egress.0.self": "false", "egress.0.to_port": "80", "egress.1.cidr_blocks.#": "1", "egress.1.cidr_blocks.0": "0.0.0.0/0", "egress.1.description": "HTTPS", "egress.1.from_port": "443", "egress.1.ipv6_cidr_blocks.#": "0", "egress.1.prefix_list_ids.#": "0", "egress.1.protocol": "tcp", "egress.1.security_groups.#": "0", "egress.1.self": "false", "egress.1.to_port": "443", "egress.2.cidr_blocks.#": "1", "egress.2.cidr_blocks.0": "**********/27", "egress.2.description": "HTTP Proxy", "egress.2.from_port": "3128", "egress.2.ipv6_cidr_blocks.#": "0", "egress.2.prefix_list_ids.#": "0", "egress.2.protocol": "tcp", "egress.2.security_groups.#": "0", "egress.2.self": "false", "egress.2.to_port": "3128", "id": "sg-00df94fe66c7464f5", "ingress.#": "5", "ingress.0.cidr_blocks.#": "2", "ingress.0.cidr_blocks.0": "**********/28", "ingress.0.cidr_blocks.1": "**********/27", "ingress.0.description": "", "ingress.0.from_port": "80", "ingress.0.ipv6_cidr_blocks.#": "0", "ingress.0.prefix_list_ids.#": "0", "ingress.0.protocol": "tcp", "ingress.0.security_groups.#": "0", "ingress.0.self": "false", "ingress.0.to_port": "80", "ingress.1.cidr_blocks.#": "1", "ingress.1.cidr_blocks.0": "**********/28", "ingress.1.description": "ICMP from Management subnet", "ingress.1.from_port": "-1", "ingress.1.ipv6_cidr_blocks.#": "0", "ingress.1.prefix_list_ids.#": "0", "ingress.1.protocol": "icmp", "ingress.1.security_groups.#": "0", "ingress.1.self": "false", "ingress.1.to_port": "-1", "ingress.2.cidr_blocks.#": "1", "ingress.2.cidr_blocks.0": "**********/28", "ingress.2.description": "SSH from Management subnet", "ingress.2.from_port": "22", "ingress.2.ipv6_cidr_blocks.#": "0", "ingress.2.prefix_list_ids.#": "0", "ingress.2.protocol": "tcp", "ingress.2.security_groups.#": "0", "ingress.2.self": "false", "ingress.2.to_port": "22", "ingress.3.cidr_blocks.#": "1", "ingress.3.cidr_blocks.0": "**********/22", "ingress.3.description": "HTTPS from App-DB VPC", "ingress.3.from_port": "443", "ingress.3.ipv6_cidr_blocks.#": "0", "ingress.3.prefix_list_ids.#": "0", "ingress.3.protocol": "tcp", "ingress.3.security_groups.#": "0", "ingress.3.self": "false", "ingress.3.to_port": "443", "ingress.4.cidr_blocks.#": "0", "ingress.4.description": "", "ingress.4.from_port": "443", "ingress.4.ipv6_cidr_blocks.#": "0", "ingress.4.prefix_list_ids.#": "0", "ingress.4.protocol": "tcp", "ingress.4.security_groups.#": "0", "ingress.4.self": "true", "ingress.4.to_port": "443", "name": "infovault-mgmt-gitlab-sg", "name_prefix": "", "owner_id": "046276255144", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "infovault-mgmt-gitlab-sg", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "infovault-mgmt-gitlab-sg", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_id": "vpc-05f1b20a6634c6a38"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_security_group.tfer--infovault-mgmt-servers-sg_sg-095117b1a793c2ce0": {"type": "aws_security_group", "depends_on": [], "primary": {"id": "sg-095117b1a793c2ce0", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:security-group/sg-095117b1a793c2ce0", "description": "Security group for Management servers", "egress.#": "7", "egress.0.cidr_blocks.#": "1", "egress.0.cidr_blocks.0": "0.0.0.0/0", "egress.0.description": "HTTP", "egress.0.from_port": "80", "egress.0.ipv6_cidr_blocks.#": "0", "egress.0.prefix_list_ids.#": "0", "egress.0.protocol": "tcp", "egress.0.security_groups.#": "0", "egress.0.self": "false", "egress.0.to_port": "80", "egress.1.cidr_blocks.#": "1", "egress.1.cidr_blocks.0": "0.0.0.0/0", "egress.1.description": "HTTPS", "egress.1.from_port": "443", "egress.1.ipv6_cidr_blocks.#": "0", "egress.1.prefix_list_ids.#": "0", "egress.1.protocol": "tcp", "egress.1.security_groups.#": "0", "egress.1.self": "false", "egress.1.to_port": "443", "egress.2.cidr_blocks.#": "1", "egress.2.cidr_blocks.0": "*********/26", "egress.2.description": "HTTPS to Gen Facing VPC Primary CIDR", "egress.2.from_port": "443", "egress.2.ipv6_cidr_blocks.#": "0", "egress.2.prefix_list_ids.#": "0", "egress.2.protocol": "tcp", "egress.2.security_groups.#": "0", "egress.2.self": "false", "egress.2.to_port": "443", "egress.3.cidr_blocks.#": "1", "egress.3.cidr_blocks.0": "**********/22", "egress.3.description": "Allow access to pod port", "egress.3.from_port": "8080", "egress.3.ipv6_cidr_blocks.#": "0", "egress.3.prefix_list_ids.#": "0", "egress.3.protocol": "tcp", "egress.3.security_groups.#": "0", "egress.3.self": "false", "egress.3.to_port": "8080", "egress.4.cidr_blocks.#": "1", "egress.4.cidr_blocks.0": "**********/22", "egress.4.description": "Allow outbound access to EKS NodePorts", "egress.4.from_port": "30000", "egress.4.ipv6_cidr_blocks.#": "0", "egress.4.prefix_list_ids.#": "0", "egress.4.protocol": "tcp", "egress.4.security_groups.#": "0", "egress.4.self": "false", "egress.4.to_port": "33000", "egress.5.cidr_blocks.#": "1", "egress.5.cidr_blocks.0": "**********/22", "egress.5.description": "HTTPS to Gen Facing VPC Secondary CIDR", "egress.5.from_port": "443", "egress.5.ipv6_cidr_blocks.#": "0", "egress.5.prefix_list_ids.#": "0", "egress.5.protocol": "tcp", "egress.5.security_groups.#": "0", "egress.5.self": "false", "egress.5.to_port": "443", "egress.6.cidr_blocks.#": "1", "egress.6.cidr_blocks.0": "**********/27", "egress.6.description": "HTTP Proxy", "egress.6.from_port": "3128", "egress.6.ipv6_cidr_blocks.#": "0", "egress.6.prefix_list_ids.#": "0", "egress.6.protocol": "tcp", "egress.6.security_groups.#": "0", "egress.6.self": "false", "egress.6.to_port": "3128", "id": "sg-095117b1a793c2ce0", "ingress.#": "5", "ingress.0.cidr_blocks.#": "1", "ingress.0.cidr_blocks.0": "**********/26", "ingress.0.description": "", "ingress.0.from_port": "5985", "ingress.0.ipv6_cidr_blocks.#": "0", "ingress.0.prefix_list_ids.#": "0", "ingress.0.protocol": "tcp", "ingress.0.security_groups.#": "0", "ingress.0.self": "false", "ingress.0.to_port": "5985", "ingress.1.cidr_blocks.#": "1", "ingress.1.cidr_blocks.0": "**********/26", "ingress.1.description": "", "ingress.1.from_port": "5986", "ingress.1.ipv6_cidr_blocks.#": "0", "ingress.1.prefix_list_ids.#": "0", "ingress.1.protocol": "tcp", "ingress.1.security_groups.#": "0", "ingress.1.self": "false", "ingress.1.to_port": "5986", "ingress.2.cidr_blocks.#": "1", "ingress.2.cidr_blocks.0": "**********/22", "ingress.2.description": "ICMP from App-DB VPC", "ingress.2.from_port": "-1", "ingress.2.ipv6_cidr_blocks.#": "0", "ingress.2.prefix_list_ids.#": "0", "ingress.2.protocol": "icmp", "ingress.2.security_groups.#": "0", "ingress.2.self": "false", "ingress.2.to_port": "-1", "ingress.3.cidr_blocks.#": "1", "ingress.3.cidr_blocks.0": "**********/22", "ingress.3.description": "RDP from App-DB VPC", "ingress.3.from_port": "3389", "ingress.3.ipv6_cidr_blocks.#": "0", "ingress.3.prefix_list_ids.#": "0", "ingress.3.protocol": "tcp", "ingress.3.security_groups.#": "0", "ingress.3.self": "false", "ingress.3.to_port": "3389", "ingress.4.cidr_blocks.#": "1", "ingress.4.cidr_blocks.0": "**********/22", "ingress.4.description": "SSH from App-DB VPC", "ingress.4.from_port": "22", "ingress.4.ipv6_cidr_blocks.#": "0", "ingress.4.prefix_list_ids.#": "0", "ingress.4.protocol": "tcp", "ingress.4.security_groups.#": "0", "ingress.4.self": "false", "ingress.4.to_port": "22", "name": "infovault-mgmt-servers-sg", "name_prefix": "", "owner_id": "046276255144", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "infovault-mgmt-servers-sg", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "infovault-mgmt-servers-sg", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_id": "vpc-05f1b20a6634c6a38"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_security_group.tfer--infovault-mgmt-vpce-sg_sg-0d28b5ada1bb60c9a": {"type": "aws_security_group", "depends_on": [], "primary": {"id": "sg-0d28b5ada1bb60c9a", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:security-group/sg-0d28b5ada1bb60c9a", "description": "Security group for VPC Endpoints", "egress.#": "1", "egress.0.cidr_blocks.#": "1", "egress.0.cidr_blocks.0": "0.0.0.0/0", "egress.0.description": "HTTPS", "egress.0.from_port": "443", "egress.0.ipv6_cidr_blocks.#": "0", "egress.0.prefix_list_ids.#": "0", "egress.0.protocol": "tcp", "egress.0.security_groups.#": "0", "egress.0.self": "false", "egress.0.to_port": "443", "id": "sg-0d28b5ada1bb60c9a", "ingress.#": "2", "ingress.0.cidr_blocks.#": "1", "ingress.0.cidr_blocks.0": "**********/28", "ingress.0.description": "HTTPS from Management subnet", "ingress.0.from_port": "443", "ingress.0.ipv6_cidr_blocks.#": "0", "ingress.0.prefix_list_ids.#": "0", "ingress.0.protocol": "tcp", "ingress.0.security_groups.#": "0", "ingress.0.self": "false", "ingress.0.to_port": "443", "ingress.1.cidr_blocks.#": "1", "ingress.1.cidr_blocks.0": "***********/28", "ingress.1.description": "HTTPS From AD Client Subnet", "ingress.1.from_port": "443", "ingress.1.ipv6_cidr_blocks.#": "0", "ingress.1.prefix_list_ids.#": "0", "ingress.1.protocol": "tcp", "ingress.1.security_groups.#": "0", "ingress.1.self": "false", "ingress.1.to_port": "443", "name": "infovault-mgmt-vpce-sg", "name_prefix": "", "owner_id": "046276255144", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "infovault-mgmt-vpce-sg", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "infovault-mgmt-vpce-sg", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_id": "vpc-05f1b20a6634c6a38"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_security_group.tfer--net-squid-sg_sg-00b66d34fb63637f3": {"type": "aws_security_group", "depends_on": [], "primary": {"id": "sg-00b66d34fb63637f3", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:security-group/sg-00b66d34fb63637f3", "description": "Security group for Squid proxy", "egress.#": "2", "egress.0.cidr_blocks.#": "1", "egress.0.cidr_blocks.0": "0.0.0.0/0", "egress.0.description": "Allow HTTP outbound traffic", "egress.0.from_port": "80", "egress.0.ipv6_cidr_blocks.#": "0", "egress.0.prefix_list_ids.#": "0", "egress.0.protocol": "tcp", "egress.0.security_groups.#": "0", "egress.0.self": "false", "egress.0.to_port": "80", "egress.1.cidr_blocks.#": "1", "egress.1.cidr_blocks.0": "0.0.0.0/0", "egress.1.description": "Allow HTTPS outbound traffic", "egress.1.from_port": "443", "egress.1.ipv6_cidr_blocks.#": "0", "egress.1.prefix_list_ids.#": "0", "egress.1.protocol": "tcp", "egress.1.security_groups.#": "0", "egress.1.self": "false", "egress.1.to_port": "443", "id": "sg-00b66d34fb63637f3", "ingress.#": "3", "ingress.0.cidr_blocks.#": "1", "ingress.0.cidr_blocks.0": "*********/22", "ingress.0.description": "Allow HTTP proxy from App-DB VPC", "ingress.0.from_port": "3128", "ingress.0.ipv6_cidr_blocks.#": "0", "ingress.0.prefix_list_ids.#": "0", "ingress.0.protocol": "tcp", "ingress.0.security_groups.#": "0", "ingress.0.self": "false", "ingress.0.to_port": "3128", "ingress.1.cidr_blocks.#": "1", "ingress.1.cidr_blocks.0": "**********/27", "ingress.1.description": "Allow HTTP proxy from Patching VPC", "ingress.1.from_port": "3128", "ingress.1.ipv6_cidr_blocks.#": "0", "ingress.1.prefix_list_ids.#": "0", "ingress.1.protocol": "tcp", "ingress.1.security_groups.#": "0", "ingress.1.self": "false", "ingress.1.to_port": "3128", "ingress.2.cidr_blocks.#": "1", "ingress.2.cidr_blocks.0": "**********/26", "ingress.2.description": "Allow HTTP proxy from Management VPC", "ingress.2.from_port": "3128", "ingress.2.ipv6_cidr_blocks.#": "0", "ingress.2.prefix_list_ids.#": "0", "ingress.2.protocol": "tcp", "ingress.2.security_groups.#": "0", "ingress.2.self": "false", "ingress.2.to_port": "3128", "name": "net-squid-sg", "name_prefix": "", "owner_id": "046276255144", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "dev-net-squid-sg", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "dev-net-squid-sg", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_id": "vpc-037f619602fa293a1"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_security_group.tfer--net-squid-sg_sg-01c71a601fbce5d26": {"type": "aws_security_group", "depends_on": [], "primary": {"id": "sg-01c71a601fbce5d26", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:security-group/sg-01c71a601fbce5d26", "description": "Security group for Squid proxy", "egress.#": "2", "egress.0.cidr_blocks.#": "1", "egress.0.cidr_blocks.0": "0.0.0.0/0", "egress.0.description": "Allow HTTP outbound traffic", "egress.0.from_port": "80", "egress.0.ipv6_cidr_blocks.#": "0", "egress.0.prefix_list_ids.#": "0", "egress.0.protocol": "tcp", "egress.0.security_groups.#": "0", "egress.0.self": "false", "egress.0.to_port": "80", "egress.1.cidr_blocks.#": "1", "egress.1.cidr_blocks.0": "0.0.0.0/0", "egress.1.description": "Allow HTTPS outbound traffic", "egress.1.from_port": "443", "egress.1.ipv6_cidr_blocks.#": "0", "egress.1.prefix_list_ids.#": "0", "egress.1.protocol": "tcp", "egress.1.security_groups.#": "0", "egress.1.self": "false", "egress.1.to_port": "443", "id": "sg-01c71a601fbce5d26", "ingress.#": "3", "ingress.0.cidr_blocks.#": "1", "ingress.0.cidr_blocks.0": "*********/22", "ingress.0.description": "Allow HTTP proxy from App-DB VPC", "ingress.0.from_port": "3128", "ingress.0.ipv6_cidr_blocks.#": "0", "ingress.0.prefix_list_ids.#": "0", "ingress.0.protocol": "tcp", "ingress.0.security_groups.#": "0", "ingress.0.self": "false", "ingress.0.to_port": "3128", "ingress.1.cidr_blocks.#": "1", "ingress.1.cidr_blocks.0": "**********/27", "ingress.1.description": "Allow HTTP proxy from Patching VPC", "ingress.1.from_port": "3128", "ingress.1.ipv6_cidr_blocks.#": "0", "ingress.1.prefix_list_ids.#": "0", "ingress.1.protocol": "tcp", "ingress.1.security_groups.#": "0", "ingress.1.self": "false", "ingress.1.to_port": "3128", "ingress.2.cidr_blocks.#": "1", "ingress.2.cidr_blocks.0": "**********/26", "ingress.2.description": "Allow HTTP proxy from Management VPC", "ingress.2.from_port": "3128", "ingress.2.ipv6_cidr_blocks.#": "0", "ingress.2.prefix_list_ids.#": "0", "ingress.2.protocol": "tcp", "ingress.2.security_groups.#": "0", "ingress.2.self": "false", "ingress.2.to_port": "3128", "name": "net-squid-sg", "name_prefix": "", "owner_id": "046276255144", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "dev-net-squid-sg", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "dev-net-squid-sg", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_id": "vpc-0e75036a2ad9e5b4f"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_security_group.tfer--net-squid-sg_sg-0906e124ed285bd7e": {"type": "aws_security_group", "depends_on": [], "primary": {"id": "sg-0906e124ed285bd7e", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:security-group/sg-0906e124ed285bd7e", "description": "Security group for Squid proxy", "egress.#": "2", "egress.0.cidr_blocks.#": "1", "egress.0.cidr_blocks.0": "0.0.0.0/0", "egress.0.description": "Allow HTTP outbound traffic", "egress.0.from_port": "80", "egress.0.ipv6_cidr_blocks.#": "0", "egress.0.prefix_list_ids.#": "0", "egress.0.protocol": "tcp", "egress.0.security_groups.#": "0", "egress.0.self": "false", "egress.0.to_port": "80", "egress.1.cidr_blocks.#": "1", "egress.1.cidr_blocks.0": "0.0.0.0/0", "egress.1.description": "Allow HTTPS outbound traffic", "egress.1.from_port": "443", "egress.1.ipv6_cidr_blocks.#": "0", "egress.1.prefix_list_ids.#": "0", "egress.1.protocol": "tcp", "egress.1.security_groups.#": "0", "egress.1.self": "false", "egress.1.to_port": "443", "id": "sg-0906e124ed285bd7e", "ingress.#": "3", "ingress.0.cidr_blocks.#": "1", "ingress.0.cidr_blocks.0": "*********/22", "ingress.0.description": "Allow HTTP proxy from App-DB VPC", "ingress.0.from_port": "3128", "ingress.0.ipv6_cidr_blocks.#": "0", "ingress.0.prefix_list_ids.#": "0", "ingress.0.protocol": "tcp", "ingress.0.security_groups.#": "0", "ingress.0.self": "false", "ingress.0.to_port": "3128", "ingress.1.cidr_blocks.#": "1", "ingress.1.cidr_blocks.0": "**********/27", "ingress.1.description": "Allow HTTP proxy from Patching VPC", "ingress.1.from_port": "3128", "ingress.1.ipv6_cidr_blocks.#": "0", "ingress.1.prefix_list_ids.#": "0", "ingress.1.protocol": "tcp", "ingress.1.security_groups.#": "0", "ingress.1.self": "false", "ingress.1.to_port": "3128", "ingress.2.cidr_blocks.#": "1", "ingress.2.cidr_blocks.0": "**********/26", "ingress.2.description": "Allow HTTP proxy from Management VPC", "ingress.2.from_port": "3128", "ingress.2.ipv6_cidr_blocks.#": "0", "ingress.2.prefix_list_ids.#": "0", "ingress.2.protocol": "tcp", "ingress.2.security_groups.#": "0", "ingress.2.self": "false", "ingress.2.to_port": "3128", "name": "net-squid-sg", "name_prefix": "", "owner_id": "046276255144", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "dev-net-squid-sg", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "dev-net-squid-sg", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_security_group.tfer--net-squid-sg_sg-097db697cb7a5f558": {"type": "aws_security_group", "depends_on": [], "primary": {"id": "sg-097db697cb7a5f558", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:security-group/sg-097db697cb7a5f558", "description": "Security group for Squid proxy", "egress.#": "2", "egress.0.cidr_blocks.#": "1", "egress.0.cidr_blocks.0": "0.0.0.0/0", "egress.0.description": "Allow HTTP outbound traffic", "egress.0.from_port": "80", "egress.0.ipv6_cidr_blocks.#": "0", "egress.0.prefix_list_ids.#": "0", "egress.0.protocol": "tcp", "egress.0.security_groups.#": "0", "egress.0.self": "false", "egress.0.to_port": "80", "egress.1.cidr_blocks.#": "1", "egress.1.cidr_blocks.0": "0.0.0.0/0", "egress.1.description": "Allow HTTPS outbound traffic", "egress.1.from_port": "443", "egress.1.ipv6_cidr_blocks.#": "0", "egress.1.prefix_list_ids.#": "0", "egress.1.protocol": "tcp", "egress.1.security_groups.#": "0", "egress.1.self": "false", "egress.1.to_port": "443", "id": "sg-097db697cb7a5f558", "ingress.#": "3", "ingress.0.cidr_blocks.#": "1", "ingress.0.cidr_blocks.0": "*********/22", "ingress.0.description": "Allow HTTP proxy from App-DB VPC", "ingress.0.from_port": "3128", "ingress.0.ipv6_cidr_blocks.#": "0", "ingress.0.prefix_list_ids.#": "0", "ingress.0.protocol": "tcp", "ingress.0.security_groups.#": "0", "ingress.0.self": "false", "ingress.0.to_port": "3128", "ingress.1.cidr_blocks.#": "1", "ingress.1.cidr_blocks.0": "**********/27", "ingress.1.description": "Allow HTTP proxy from Patching VPC", "ingress.1.from_port": "3128", "ingress.1.ipv6_cidr_blocks.#": "0", "ingress.1.prefix_list_ids.#": "0", "ingress.1.protocol": "tcp", "ingress.1.security_groups.#": "0", "ingress.1.self": "false", "ingress.1.to_port": "3128", "ingress.2.cidr_blocks.#": "1", "ingress.2.cidr_blocks.0": "**********/26", "ingress.2.description": "Allow HTTP proxy from Management VPC", "ingress.2.from_port": "3128", "ingress.2.ipv6_cidr_blocks.#": "0", "ingress.2.prefix_list_ids.#": "0", "ingress.2.protocol": "tcp", "ingress.2.security_groups.#": "0", "ingress.2.self": "false", "ingress.2.to_port": "3128", "name": "net-squid-sg", "name_prefix": "", "owner_id": "046276255144", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "dev-net-squid-sg", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "dev-net-squid-sg", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_id": "vpc-05f1b20a6634c6a38"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_security_group.tfer--patching-rhel-repo-sg_sg-01f0c371bd11aa6f1": {"type": "aws_security_group", "depends_on": [], "primary": {"id": "sg-01f0c371bd11aa6f1", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:security-group/sg-01f0c371bd11aa6f1", "description": "Security group for RHEL Repo server in Patching Compartment", "egress.#": "1", "egress.0.cidr_blocks.#": "1", "egress.0.cidr_blocks.0": "0.0.0.0/0", "egress.0.description": "Allow all outbound traffic", "egress.0.from_port": "0", "egress.0.ipv6_cidr_blocks.#": "0", "egress.0.prefix_list_ids.#": "0", "egress.0.protocol": "-1", "egress.0.security_groups.#": "0", "egress.0.self": "false", "egress.0.to_port": "0", "id": "sg-01f0c371bd11aa6f1", "ingress.#": "2", "ingress.0.cidr_blocks.#": "1", "ingress.0.cidr_blocks.0": "**********/27", "ingress.0.description": "Placeholder - Allow HTTP from VPC (for clients - adjust as needed)", "ingress.0.from_port": "80", "ingress.0.ipv6_cidr_blocks.#": "0", "ingress.0.prefix_list_ids.#": "0", "ingress.0.protocol": "tcp", "ingress.0.security_groups.#": "0", "ingress.0.self": "false", "ingress.0.to_port": "80", "ingress.1.cidr_blocks.#": "1", "ingress.1.cidr_blocks.0": "**********/27", "ingress.1.description": "Placeholder - Allow HTTPS from VPC (for clients - adjust as needed)", "ingress.1.from_port": "443", "ingress.1.ipv6_cidr_blocks.#": "0", "ingress.1.prefix_list_ids.#": "0", "ingress.1.protocol": "tcp", "ingress.1.security_groups.#": "0", "ingress.1.self": "false", "ingress.1.to_port": "443", "name": "patching-rhel-repo-sg", "name_prefix": "", "owner_id": "046276255144", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "sg-patching-rhel-repo", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "sg-patching-rhel-repo", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_id": "vpc-0ca0480cce82946b3"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_security_group.tfer--patching-vpce-sg_sg-0870c466d0db940c4": {"type": "aws_security_group", "depends_on": [], "primary": {"id": "sg-0870c466d0db940c4", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:security-group/sg-0870c466d0db940c4", "description": "Security group for VPC Endpoints in Patching Compartment", "egress.#": "1", "egress.0.cidr_blocks.#": "1", "egress.0.cidr_blocks.0": "0.0.0.0/0", "egress.0.description": "Allow all outbound traffic", "egress.0.from_port": "0", "egress.0.ipv6_cidr_blocks.#": "0", "egress.0.prefix_list_ids.#": "0", "egress.0.protocol": "-1", "egress.0.security_groups.#": "0", "egress.0.self": "false", "egress.0.to_port": "0", "id": "sg-0870c466d0db940c4", "ingress.#": "1", "ingress.0.cidr_blocks.#": "1", "ingress.0.cidr_blocks.0": "**********/27", "ingress.0.description": "Allow HTTPS from within the VPC for VPC Endpoints", "ingress.0.from_port": "443", "ingress.0.ipv6_cidr_blocks.#": "0", "ingress.0.prefix_list_ids.#": "0", "ingress.0.protocol": "tcp", "ingress.0.security_groups.#": "0", "ingress.0.self": "false", "ingress.0.to_port": "443", "name": "patching-vpce-sg", "name_prefix": "", "owner_id": "046276255144", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "sg-patching-vpce", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "sg-patching-vpce", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_id": "vpc-0ca0480cce82946b3"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_security_group.tfer--patching-wsus-sg_sg-04080d467ff2ca6c4": {"type": "aws_security_group", "depends_on": [], "primary": {"id": "sg-04080d467ff2ca6c4", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:security-group/sg-04080d467ff2ca6c4", "description": "Security group for WSUS server in Patching Compartment", "egress.#": "1", "egress.0.cidr_blocks.#": "1", "egress.0.cidr_blocks.0": "0.0.0.0/0", "egress.0.description": "Allow all outbound traffic", "egress.0.from_port": "0", "egress.0.ipv6_cidr_blocks.#": "0", "egress.0.prefix_list_ids.#": "0", "egress.0.protocol": "-1", "egress.0.security_groups.#": "0", "egress.0.self": "false", "egress.0.to_port": "0", "id": "sg-04080d467ff2ca6c4", "ingress.#": "2", "ingress.0.cidr_blocks.#": "1", "ingress.0.cidr_blocks.0": "**********/27", "ingress.0.description": "Placeholder - Allow HTTP from VPC (for clients - adjust as needed)", "ingress.0.from_port": "80", "ingress.0.ipv6_cidr_blocks.#": "0", "ingress.0.prefix_list_ids.#": "0", "ingress.0.protocol": "tcp", "ingress.0.security_groups.#": "0", "ingress.0.self": "false", "ingress.0.to_port": "80", "ingress.1.cidr_blocks.#": "1", "ingress.1.cidr_blocks.0": "**********/27", "ingress.1.description": "Placeholder - Allow HTTPS from VPC (for clients - adjust as needed)", "ingress.1.from_port": "443", "ingress.1.ipv6_cidr_blocks.#": "0", "ingress.1.prefix_list_ids.#": "0", "ingress.1.protocol": "tcp", "ingress.1.security_groups.#": "0", "ingress.1.self": "false", "ingress.1.to_port": "443", "name": "patching-wsus-sg", "name_prefix": "", "owner_id": "046276255144", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "sg-patching-wsus", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "sg-patching-wsus", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_id": "vpc-0ca0480cce82946b3"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_security_group.tfer--poc-host-naga_sg-054a1946b939e0eca": {"type": "aws_security_group", "depends_on": [], "primary": {"id": "sg-054a1946b939e0eca", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:security-group/sg-054a1946b939e0eca", "description": "launch-wizard-1 created 2025-06-06T23:05:30.621Z", "egress.#": "1", "egress.0.cidr_blocks.#": "1", "egress.0.cidr_blocks.0": "0.0.0.0/0", "egress.0.description": "", "egress.0.from_port": "0", "egress.0.ipv6_cidr_blocks.#": "0", "egress.0.prefix_list_ids.#": "0", "egress.0.protocol": "-1", "egress.0.security_groups.#": "0", "egress.0.self": "false", "egress.0.to_port": "0", "id": "sg-054a1946b939e0eca", "ingress.#": "2", "ingress.0.cidr_blocks.#": "1", "ingress.0.cidr_blocks.0": "0.0.0.0/0", "ingress.0.description": "", "ingress.0.from_port": "22", "ingress.0.ipv6_cidr_blocks.#": "0", "ingress.0.prefix_list_ids.#": "0", "ingress.0.protocol": "tcp", "ingress.0.security_groups.#": "0", "ingress.0.self": "false", "ingress.0.to_port": "22", "ingress.1.cidr_blocks.#": "1", "ingress.1.cidr_blocks.0": "0.0.0.0/0", "ingress.1.description": "", "ingress.1.from_port": "443", "ingress.1.ipv6_cidr_blocks.#": "0", "ingress.1.prefix_list_ids.#": "0", "ingress.1.protocol": "tcp", "ingress.1.security_groups.#": "0", "ingress.1.self": "false", "ingress.1.to_port": "443", "name": "poc-host-naga", "name_prefix": "", "owner_id": "046276255144", "tags.%": "0", "tags_all.%": "0", "vpc_id": "vpc-037f619602fa293a1"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_security_group.tfer--sgrp-dev-cam-srvr-01_sg-029c5f3f1ddf2855e": {"type": "aws_security_group", "depends_on": [], "primary": {"id": "sg-029c5f3f1ddf2855e", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:security-group/sg-029c5f3f1ddf2855e", "description": "sgrp-dev-cam-srvr-01", "egress.#": "1", "egress.0.cidr_blocks.#": "1", "egress.0.cidr_blocks.0": "0.0.0.0/0", "egress.0.description": "", "egress.0.from_port": "0", "egress.0.ipv6_cidr_blocks.#": "0", "egress.0.prefix_list_ids.#": "0", "egress.0.protocol": "-1", "egress.0.security_groups.#": "0", "egress.0.self": "false", "egress.0.to_port": "0", "id": "sg-029c5f3f1ddf2855e", "ingress.#": "1", "ingress.0.cidr_blocks.#": "1", "ingress.0.cidr_blocks.0": "0.0.0.0/0", "ingress.0.description": "", "ingress.0.from_port": "22", "ingress.0.ipv6_cidr_blocks.#": "0", "ingress.0.prefix_list_ids.#": "0", "ingress.0.protocol": "tcp", "ingress.0.security_groups.#": "0", "ingress.0.self": "false", "ingress.0.to_port": "22", "name": "sgrp-dev-cam-srvr-01", "name_prefix": "", "owner_id": "046276255144", "tags.%": "0", "tags_all.%": "0", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_security_group.tfer--sgrp-dev-smtp-srvr-01_sg-0764227cd7e054e0a": {"type": "aws_security_group", "depends_on": [], "primary": {"id": "sg-0764227cd7e054e0a", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:security-group/sg-0764227cd7e054e0a", "description": "sgrp-dev-smtp-srvr-01", "egress.#": "1", "egress.0.cidr_blocks.#": "1", "egress.0.cidr_blocks.0": "0.0.0.0/0", "egress.0.description": "", "egress.0.from_port": "0", "egress.0.ipv6_cidr_blocks.#": "0", "egress.0.prefix_list_ids.#": "0", "egress.0.protocol": "-1", "egress.0.security_groups.#": "0", "egress.0.self": "false", "egress.0.to_port": "0", "id": "sg-0764227cd7e054e0a", "ingress.#": "1", "ingress.0.cidr_blocks.#": "1", "ingress.0.cidr_blocks.0": "0.0.0.0/0", "ingress.0.description": "", "ingress.0.from_port": "22", "ingress.0.ipv6_cidr_blocks.#": "0", "ingress.0.prefix_list_ids.#": "0", "ingress.0.protocol": "tcp", "ingress.0.security_groups.#": "0", "ingress.0.self": "false", "ingress.0.to_port": "22", "name": "sgrp-dev-smtp-srvr-01", "name_prefix": "", "owner_id": "046276255144", "tags.%": "0", "tags_all.%": "0", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_security_group.tfer--sgrp-dev-squid-pxy-iz-01_sg-023130e17836a50d3": {"type": "aws_security_group", "depends_on": [], "primary": {"id": "sg-023130e17836a50d3", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:security-group/sg-023130e17836a50d3", "description": "sgrp-dev-squid-pxy-iz-01", "egress.#": "1", "egress.0.cidr_blocks.#": "1", "egress.0.cidr_blocks.0": "0.0.0.0/0", "egress.0.description": "", "egress.0.from_port": "0", "egress.0.ipv6_cidr_blocks.#": "0", "egress.0.prefix_list_ids.#": "0", "egress.0.protocol": "-1", "egress.0.security_groups.#": "0", "egress.0.self": "false", "egress.0.to_port": "0", "id": "sg-023130e17836a50d3", "ingress.#": "1", "ingress.0.cidr_blocks.#": "1", "ingress.0.cidr_blocks.0": "0.0.0.0/0", "ingress.0.description": "", "ingress.0.from_port": "22", "ingress.0.ipv6_cidr_blocks.#": "0", "ingress.0.prefix_list_ids.#": "0", "ingress.0.protocol": "tcp", "ingress.0.security_groups.#": "0", "ingress.0.self": "false", "ingress.0.to_port": "22", "name": "sgrp-dev-squid-pxy-iz-01", "name_prefix": "", "owner_id": "046276255144", "tags.%": "0", "tags_all.%": "0", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}}, "depends_on": []}]}