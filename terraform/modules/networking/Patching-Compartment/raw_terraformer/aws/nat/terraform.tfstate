{"version": 3, "terraform_version": "0.12.31", "serial": 1, "lineage": "af1d6455-2be6-1fc9-bc97-bd3603182f2d", "modules": [{"path": ["root"], "outputs": {"aws_nat_gateway_tfer--nat-0431bfe8618055bd8_id": {"sensitive": false, "type": "string", "value": "nat-0431bfe8618055bd8"}, "aws_nat_gateway_tfer--nat-0faca993cb72fc7d6_id": {"sensitive": false, "type": "string", "value": "nat-0faca993cb72fc7d6"}}, "resources": {"aws_nat_gateway.tfer--nat-0431bfe8618055bd8": {"type": "aws_nat_gateway", "depends_on": [], "primary": {"id": "nat-0431bfe8618055bd8", "attributes": {"allocation_id": "eipalloc-073d1259e09e3694d", "association_id": "eipassoc-064917cfbdbb30519", "connectivity_type": "public", "id": "nat-0431bfe8618055bd8", "network_interface_id": "eni-066838eeb94b2abd3", "private_ip": "**********", "public_ip": "**************", "secondary_allocation_ids.#": "0", "secondary_private_ip_address_count": "0", "secondary_private_ip_addresses.#": "0", "subnet_id": "subnet-043dd23a18b80a280", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "infovault-dev-gen-facing-compartment-nat-gateway", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "infovault-dev-gen-facing-compartment-nat-gateway", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_nat_gateway.tfer--nat-0faca993cb72fc7d6": {"type": "aws_nat_gateway", "depends_on": [], "primary": {"id": "nat-0faca993cb72fc7d6", "attributes": {"allocation_id": "eipalloc-05b293b1c50d5ff91", "association_id": "eipassoc-002f0d8b9d050a68f", "connectivity_type": "public", "id": "nat-0faca993cb72fc7d6", "network_interface_id": "eni-0d6e441784201ed85", "private_ip": "**********", "public_ip": "*************", "secondary_allocation_ids.#": "0", "secondary_private_ip_address_count": "0", "secondary_private_ip_addresses.#": "0", "subnet_id": "subnet-01e871c49dc4b9b1f", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "dev-net-natgw-az1", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "dev-net-natgw-az1", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}}, "depends_on": []}]}