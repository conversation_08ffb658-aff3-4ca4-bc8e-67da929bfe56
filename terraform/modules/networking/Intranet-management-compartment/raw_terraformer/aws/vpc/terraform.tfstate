{"version": 3, "terraform_version": "0.12.31", "serial": 1, "lineage": "3aea7854-545d-99bb-6f9c-e0abc5c4ac3f", "modules": [{"path": ["root"], "outputs": {"aws_vpc_tfer--vpc-05f1b20a6634c6a38_id": {"sensitive": false, "type": "string", "value": "vpc-05f1b20a6634c6a38"}}, "resources": {"aws_vpc.tfer--vpc-05f1b20a6634c6a38": {"type": "aws_vpc", "depends_on": [], "primary": {"id": "vpc-05f1b20a6634c6a38", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:vpc/vpc-05f1b20a6634c6a38", "assign_generated_ipv6_cidr_block": "false", "cidr_block": "**********/26", "default_network_acl_id": "acl-030537d8bab318bd1", "default_route_table_id": "rtb-02a850249ccfbe80b", "default_security_group_id": "sg-019036bd03d215313", "dhcp_options_id": "dopt-0651689ceffd5fc0c", "enable_dns_hostnames": "true", "enable_dns_support": "true", "enable_network_address_usage_metrics": "false", "id": "vpc-05f1b20a6634c6a38", "instance_tenancy": "default", "ipv6_association_id": "", "ipv6_cidr_block": "", "ipv6_cidr_block_network_border_group": "", "ipv6_ipam_pool_id": "", "ipv6_netmask_length": "0", "main_route_table_id": "rtb-02a850249ccfbe80b", "owner_id": "046276255144", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "infovalut-dev-Intranet-management-compartment", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "infovalut-dev-Intranet-management-compartment", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}}, "depends_on": []}]}