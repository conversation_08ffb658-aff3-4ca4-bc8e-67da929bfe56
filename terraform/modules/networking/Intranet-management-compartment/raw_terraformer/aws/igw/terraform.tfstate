{"version": 3, "terraform_version": "0.12.31", "serial": 1, "lineage": "6bcb4c8d-7378-a0ff-4820-b4a19b1c4d2c", "modules": [{"path": ["root"], "outputs": {"aws_internet_gateway_tfer--igw-00a312b81674ba618_id": {"sensitive": false, "type": "string", "value": "igw-00a312b81674ba618"}, "aws_internet_gateway_tfer--igw-05beaf9de3b1e3902_id": {"sensitive": false, "type": "string", "value": "igw-05beaf9de3b1e3902"}, "aws_internet_gateway_tfer--igw-066231fa2c240e55d_id": {"sensitive": false, "type": "string", "value": "igw-066231fa2c240e55d"}, "aws_internet_gateway_tfer--igw-0b197ebf6057fbe90_id": {"sensitive": false, "type": "string", "value": "igw-0b197ebf6057fbe90"}}, "resources": {"aws_internet_gateway.tfer--igw-00a312b81674ba618": {"type": "aws_internet_gateway", "depends_on": [], "primary": {"id": "igw-00a312b81674ba618", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:internet-gateway/igw-00a312b81674ba618", "id": "igw-00a312b81674ba618", "owner_id": "046276255144", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "dev-aps1-igw-network-main", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "dev-aps1-igw-network-main", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_id": "vpc-037f619602fa293a1"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_internet_gateway.tfer--igw-05beaf9de3b1e3902": {"type": "aws_internet_gateway", "depends_on": [], "primary": {"id": "igw-05beaf9de3b1e3902", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:internet-gateway/igw-05beaf9de3b1e3902", "id": "igw-05beaf9de3b1e3902", "owner_id": "046276255144", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "infovault-dev-igw", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "infovault-dev-igw", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_internet_gateway.tfer--igw-066231fa2c240e55d": {"type": "aws_internet_gateway", "depends_on": [], "primary": {"id": "igw-066231fa2c240e55d", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:internet-gateway/igw-066231fa2c240e55d", "id": "igw-066231fa2c240e55d", "owner_id": "046276255144", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "infovault-dev-igw", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "infovault-dev-igw", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_id": "vpc-05f1b20a6634c6a38"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_internet_gateway.tfer--igw-0b197ebf6057fbe90": {"type": "aws_internet_gateway", "depends_on": [], "primary": {"id": "igw-0b197ebf6057fbe90", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:internet-gateway/igw-0b197ebf6057fbe90", "id": "igw-0b197ebf6057fbe90", "owner_id": "046276255144", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "infovault-dev-igw", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "infovault-dev-igw", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_id": "vpc-0e75036a2ad9e5b4f"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}}, "depends_on": []}]}