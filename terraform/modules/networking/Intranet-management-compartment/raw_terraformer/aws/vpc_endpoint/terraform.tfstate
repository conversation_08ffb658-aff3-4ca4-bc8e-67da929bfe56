{"version": 3, "terraform_version": "0.12.31", "serial": 1, "lineage": "76ef9c20-d449-6aea-7816-1023b8d0a840", "modules": [{"path": ["root"], "outputs": {"aws_vpc_endpoint_tfer--vpce-000d3536c71bcfc70_id": {"sensitive": false, "type": "string", "value": "vpce-000d3536c71bcfc70"}, "aws_vpc_endpoint_tfer--vpce-00634b26f7e325402_id": {"sensitive": false, "type": "string", "value": "vpce-00634b26f7e325402"}, "aws_vpc_endpoint_tfer--vpce-025b48dac964f67b4_id": {"sensitive": false, "type": "string", "value": "vpce-025b48dac964f67b4"}, "aws_vpc_endpoint_tfer--vpce-03557a8cac26c8917_id": {"sensitive": false, "type": "string", "value": "vpce-03557a8cac26c8917"}, "aws_vpc_endpoint_tfer--vpce-0366281d6822bac46_id": {"sensitive": false, "type": "string", "value": "vpce-0366281d6822bac46"}, "aws_vpc_endpoint_tfer--vpce-04c5af7d63bb323fb_id": {"sensitive": false, "type": "string", "value": "vpce-04c5af7d63bb323fb"}, "aws_vpc_endpoint_tfer--vpce-04d5a7ce83abf07b5_id": {"sensitive": false, "type": "string", "value": "vpce-04d5a7ce83abf07b5"}, "aws_vpc_endpoint_tfer--vpce-0538ad39973dc4aae_id": {"sensitive": false, "type": "string", "value": "vpce-0538ad39973dc4aae"}, "aws_vpc_endpoint_tfer--vpce-0657101f2b067804d_id": {"sensitive": false, "type": "string", "value": "vpce-0657101f2b067804d"}, "aws_vpc_endpoint_tfer--vpce-06c55fec3359d3a98_id": {"sensitive": false, "type": "string", "value": "vpce-06c55fec3359d3a98"}, "aws_vpc_endpoint_tfer--vpce-084596619ed02fac1_id": {"sensitive": false, "type": "string", "value": "vpce-084596619ed02fac1"}, "aws_vpc_endpoint_tfer--vpce-08c51d0d6582b0aa6_id": {"sensitive": false, "type": "string", "value": "vpce-08c51d0d6582b0aa6"}, "aws_vpc_endpoint_tfer--vpce-094e78f395ad5f6f7_id": {"sensitive": false, "type": "string", "value": "vpce-094e78f395ad5f6f7"}, "aws_vpc_endpoint_tfer--vpce-099452683bdb0a349_id": {"sensitive": false, "type": "string", "value": "vpce-099452683bdb0a349"}, "aws_vpc_endpoint_tfer--vpce-0a2d395de8c81f0f9_id": {"sensitive": false, "type": "string", "value": "vpce-0a2d395de8c81f0f9"}, "aws_vpc_endpoint_tfer--vpce-0aab6fc33b265ed8d_id": {"sensitive": false, "type": "string", "value": "vpce-0aab6fc33b265ed8d"}, "aws_vpc_endpoint_tfer--vpce-0ac5502093940b08f_id": {"sensitive": false, "type": "string", "value": "vpce-0ac5502093940b08f"}, "aws_vpc_endpoint_tfer--vpce-0bf3a106d192c71cd_id": {"sensitive": false, "type": "string", "value": "vpce-0bf3a106d192c71cd"}, "aws_vpc_endpoint_tfer--vpce-0c5ff88c30a3e428d_id": {"sensitive": false, "type": "string", "value": "vpce-0c5ff88c30a3e428d"}, "aws_vpc_endpoint_tfer--vpce-0c7798e909bd72ff8_id": {"sensitive": false, "type": "string", "value": "vpce-0c7798e909bd72ff8"}, "aws_vpc_endpoint_tfer--vpce-0cb9adf21ebb65e3b_id": {"sensitive": false, "type": "string", "value": "vpce-0cb9adf21ebb65e3b"}, "aws_vpc_endpoint_tfer--vpce-0cd55ff1d8ac0f61f_id": {"sensitive": false, "type": "string", "value": "vpce-0cd55ff1d8ac0f61f"}, "aws_vpc_endpoint_tfer--vpce-0ce3dc776f7700c04_id": {"sensitive": false, "type": "string", "value": "vpce-0ce3dc776f7700c04"}, "aws_vpc_endpoint_tfer--vpce-0d5ebafd2bcf055bf_id": {"sensitive": false, "type": "string", "value": "vpce-0d5ebafd2bcf055bf"}, "aws_vpc_endpoint_tfer--vpce-0ece27853f422d3a6_id": {"sensitive": false, "type": "string", "value": "vpce-0ece27853f422d3a6"}, "aws_vpc_endpoint_tfer--vpce-0ed0b1bdb378589bc_id": {"sensitive": false, "type": "string", "value": "vpce-0ed0b1bdb378589bc"}, "aws_vpc_endpoint_tfer--vpce-0f0b9eaa555525680_id": {"sensitive": false, "type": "string", "value": "vpce-0f0b9eaa555525680"}, "aws_vpc_endpoint_tfer--vpce-0f629db35d2992350_id": {"sensitive": false, "type": "string", "value": "vpce-0f629db35d2992350"}, "aws_vpc_endpoint_tfer--vpce-0fe5ae48246ae8a9d_id": {"sensitive": false, "type": "string", "value": "vpce-0fe5ae48246ae8a9d"}}, "resources": {"aws_vpc_endpoint.tfer--vpce-000d3536c71bcfc70": {"type": "aws_vpc_endpoint", "depends_on": [], "primary": {"id": "vpce-000d3536c71bcfc70", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:************:vpc-endpoint/vpce-000d3536c71bcfc70", "cidr_blocks.#": "0", "dns_entry.#": "3", "dns_entry.0.dns_name": "vpce-000d3536c71bcfc70-0rlebbkz.ssmmessages.ap-southeast-1.vpce.amazonaws.com", "dns_entry.0.hosted_zone_id": "Z18LLCSTV4NVNL", "dns_entry.1.dns_name": "vpce-000d3536c71bcfc70-0rlebbkz-ap-southeast-1a.ssmmessages.ap-southeast-1.vpce.amazonaws.com", "dns_entry.1.hosted_zone_id": "Z18LLCSTV4NVNL", "dns_entry.2.dns_name": "ssmmessages.ap-southeast-1.amazonaws.com", "dns_entry.2.hosted_zone_id": "Z076813824NZHU6AI7AJV", "dns_options.#": "1", "dns_options.0.dns_record_ip_type": "ipv4", "dns_options.0.private_dns_only_for_inbound_resolver_endpoint": "false", "id": "vpce-000d3536c71bcfc70", "ip_address_type": "ipv4", "network_interface_ids.#": "1", "network_interface_ids.0": "eni-09e6836ed6735715e", "owner_id": "************", "policy": "{\"Statement\":[{\"Action\":\"*\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"*\"}]}", "private_dns_enabled": "true", "requester_managed": "false", "resource_configuration_arn": "", "route_table_ids.#": "0", "security_group_ids.#": "1", "security_group_ids.0": "sg-0870c466d0db940c4", "service_name": "com.amazonaws.ap-southeast-1.ssmmessages", "service_network_arn": "", "service_region": "ap-southeast-1", "state": "available", "subnet_configuration.#": "1", "subnet_configuration.0.ipv4": "**********", "subnet_configuration.0.ipv6": "", "subnet_configuration.0.subnet_id": "subnet-0dacd0ef604204014", "subnet_ids.#": "1", "subnet_ids.0": "subnet-0dacd0ef604204014", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "vpce-infovault-dev-Patching-Compartment-ssmmessages", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "vpce-infovault-dev-Patching-Compartment-ssmmessages", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_endpoint_type": "Interface", "vpc_id": "vpc-0ca0480cce82946b3"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_vpc_endpoint.tfer--vpce-00634b26f7e325402": {"type": "aws_vpc_endpoint", "depends_on": [], "primary": {"id": "vpce-00634b26f7e325402", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:************:vpc-endpoint/vpce-00634b26f7e325402", "cidr_blocks.#": "0", "dns_entry.#": "5", "dns_entry.0.dns_name": "vpce-00634b26f7e325402-u4355s3r.api.ecr.ap-southeast-1.vpce.amazonaws.com", "dns_entry.0.hosted_zone_id": "Z18LLCSTV4NVNL", "dns_entry.1.dns_name": "vpce-00634b26f7e325402-u4355s3r-ap-southeast-1a.api.ecr.ap-southeast-1.vpce.amazonaws.com", "dns_entry.1.hosted_zone_id": "Z18LLCSTV4NVNL", "dns_entry.2.dns_name": "vpce-00634b26f7e325402-u4355s3r-ap-southeast-1b.api.ecr.ap-southeast-1.vpce.amazonaws.com", "dns_entry.2.hosted_zone_id": "Z18LLCSTV4NVNL", "dns_entry.3.dns_name": "api.ecr.ap-southeast-1.amazonaws.com", "dns_entry.3.hosted_zone_id": "Z01276833014YUMQZ9UZ2", "dns_entry.4.dns_name": "ecr.ap-southeast-1.api.aws", "dns_entry.4.hosted_zone_id": "Z01180681EIT7AKRNZKV0", "dns_options.#": "1", "dns_options.0.dns_record_ip_type": "ipv4", "dns_options.0.private_dns_only_for_inbound_resolver_endpoint": "false", "id": "vpce-00634b26f7e325402", "ip_address_type": "ipv4", "network_interface_ids.#": "2", "network_interface_ids.0": "eni-09379042c909d9d32", "network_interface_ids.1": "eni-0ffcf9a54002de3ca", "owner_id": "************", "policy": "{\"Statement\":[{\"Action\":\"*\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"*\"}]}", "private_dns_enabled": "true", "requester_managed": "false", "resource_configuration_arn": "", "route_table_ids.#": "0", "security_group_ids.#": "1", "security_group_ids.0": "sg-05f09a0dc7ab8c53f", "service_name": "com.amazonaws.ap-southeast-1.ecr.api", "service_network_arn": "", "service_region": "ap-southeast-1", "state": "available", "subnet_configuration.#": "2", "subnet_configuration.0.ipv4": "***********", "subnet_configuration.0.ipv6": "", "subnet_configuration.0.subnet_id": "subnet-023cafbedbb31d13e", "subnet_configuration.1.ipv4": "***********", "subnet_configuration.1.ipv6": "", "subnet_configuration.1.subnet_id": "subnet-015e4b2acb87091d9", "subnet_ids.#": "2", "subnet_ids.0": "subnet-015e4b2acb87091d9", "subnet_ids.1": "subnet-023cafbedbb31d13e", "tags.%": "0", "tags_all.%": "0", "vpc_endpoint_type": "Interface", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_vpc_endpoint.tfer--vpce-025b48dac964f67b4": {"type": "aws_vpc_endpoint", "depends_on": [], "primary": {"id": "vpce-025b48dac964f67b4", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:************:vpc-endpoint/vpce-025b48dac964f67b4", "cidr_blocks.#": "0", "dns_entry.#": "3", "dns_entry.0.dns_name": "vpce-025b48dac964f67b4-hjr3bv62.ec2messages.ap-southeast-1.vpce.amazonaws.com", "dns_entry.0.hosted_zone_id": "Z18LLCSTV4NVNL", "dns_entry.1.dns_name": "vpce-025b48dac964f67b4-hjr3bv62-ap-southeast-1a.ec2messages.ap-southeast-1.vpce.amazonaws.com", "dns_entry.1.hosted_zone_id": "Z18LLCSTV4NVNL", "dns_entry.2.dns_name": "ec2messages.ap-southeast-1.amazonaws.com", "dns_entry.2.hosted_zone_id": "Z06697242ZG6OR58VKTRK", "dns_options.#": "1", "dns_options.0.dns_record_ip_type": "ipv4", "dns_options.0.private_dns_only_for_inbound_resolver_endpoint": "false", "id": "vpce-025b48dac964f67b4", "ip_address_type": "ipv4", "network_interface_ids.#": "1", "network_interface_ids.0": "eni-0e4892cdad21d3d27", "owner_id": "************", "policy": "{\"Statement\":[{\"Action\":\"*\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"*\"}]}", "private_dns_enabled": "true", "requester_managed": "false", "resource_configuration_arn": "", "route_table_ids.#": "0", "security_group_ids.#": "1", "security_group_ids.0": "sg-066975c77a66a64d9", "service_name": "com.amazonaws.ap-southeast-1.ec2messages", "service_network_arn": "", "service_region": "ap-southeast-1", "state": "available", "subnet_configuration.#": "1", "subnet_configuration.0.ipv4": "**********", "subnet_configuration.0.ipv6": "", "subnet_configuration.0.subnet_id": "subnet-0a1802eb99e96cbed", "subnet_ids.#": "1", "subnet_ids.0": "subnet-0a1802eb99e96cbed", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "infovault-dev-gen-facing-compartment-ec2messages-endpoint", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "infovault-dev-gen-facing-compartment-ec2messages-endpoint", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_endpoint_type": "Interface", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_vpc_endpoint.tfer--vpce-03557a8cac26c8917": {"type": "aws_vpc_endpoint", "depends_on": [], "primary": {"id": "vpce-03557a8cac26c8917", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:************:vpc-endpoint/vpce-03557a8cac26c8917", "cidr_blocks.#": "0", "dns_entry.#": "3", "dns_entry.0.dns_name": "vpce-03557a8cac26c8917-ymjrdyqv.ec2messages.ap-southeast-1.vpce.amazonaws.com", "dns_entry.0.hosted_zone_id": "Z18LLCSTV4NVNL", "dns_entry.1.dns_name": "vpce-03557a8cac26c8917-ymjrdyqv-ap-southeast-1a.ec2messages.ap-southeast-1.vpce.amazonaws.com", "dns_entry.1.hosted_zone_id": "Z18LLCSTV4NVNL", "dns_entry.2.dns_name": "ec2messages.ap-southeast-1.amazonaws.com", "dns_entry.2.hosted_zone_id": "Z04220243LZQYUS7J7MGK", "dns_options.#": "1", "dns_options.0.dns_record_ip_type": "ipv4", "dns_options.0.private_dns_only_for_inbound_resolver_endpoint": "false", "id": "vpce-03557a8cac26c8917", "ip_address_type": "ipv4", "network_interface_ids.#": "1", "network_interface_ids.0": "eni-01cda4f27be0d17ff", "owner_id": "************", "policy": "{\"Statement\":[{\"Action\":\"*\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"*\"}]}", "private_dns_enabled": "true", "requester_managed": "false", "resource_configuration_arn": "", "route_table_ids.#": "0", "security_group_ids.#": "1", "security_group_ids.0": "sg-0870c466d0db940c4", "service_name": "com.amazonaws.ap-southeast-1.ec2messages", "service_network_arn": "", "service_region": "ap-southeast-1", "state": "available", "subnet_configuration.#": "1", "subnet_configuration.0.ipv4": "***********", "subnet_configuration.0.ipv6": "", "subnet_configuration.0.subnet_id": "subnet-0dacd0ef604204014", "subnet_ids.#": "1", "subnet_ids.0": "subnet-0dacd0ef604204014", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "vpce-infovault-dev-Patching-Compartment-ec2messages", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "vpce-infovault-dev-Patching-Compartment-ec2messages", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_endpoint_type": "Interface", "vpc_id": "vpc-0ca0480cce82946b3"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_vpc_endpoint.tfer--vpce-0366281d6822bac46": {"type": "aws_vpc_endpoint", "depends_on": [], "primary": {"id": "vpce-0366281d6822bac46", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:************:vpc-endpoint/vpce-0366281d6822bac46", "cidr_blocks.#": "0", "dns_entry.#": "3", "dns_entry.0.dns_name": "vpce-0366281d6822bac46-l6dojhv0.ssm.ap-southeast-1.vpce.amazonaws.com", "dns_entry.0.hosted_zone_id": "Z18LLCSTV4NVNL", "dns_entry.1.dns_name": "vpce-0366281d6822bac46-l6dojhv0-ap-southeast-1a.ssm.ap-southeast-1.vpce.amazonaws.com", "dns_entry.1.hosted_zone_id": "Z18LLCSTV4NVNL", "dns_entry.2.dns_name": "ssm.ap-southeast-1.amazonaws.com", "dns_entry.2.hosted_zone_id": "Z08411663AXIYXTNDUJFU", "dns_options.#": "1", "dns_options.0.dns_record_ip_type": "ipv4", "dns_options.0.private_dns_only_for_inbound_resolver_endpoint": "false", "id": "vpce-0366281d6822bac46", "ip_address_type": "ipv4", "network_interface_ids.#": "1", "network_interface_ids.0": "eni-08593c53b89a60ee4", "owner_id": "************", "policy": "{\"Statement\":[{\"Action\":\"*\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"*\"}]}", "private_dns_enabled": "true", "requester_managed": "false", "resource_configuration_arn": "", "route_table_ids.#": "0", "security_group_ids.#": "1", "security_group_ids.0": "sg-082d3fc2b26b1004e", "service_name": "com.amazonaws.ap-southeast-1.ssm", "service_network_arn": "", "service_region": "ap-southeast-1", "state": "available", "subnet_configuration.#": "1", "subnet_configuration.0.ipv4": "*********", "subnet_configuration.0.ipv6": "", "subnet_configuration.0.subnet_id": "subnet-0c6066202240c38be", "subnet_ids.#": "1", "subnet_ids.0": "subnet-0c6066202240c38be", "tags.%": "9", "tags.AutoShutdown": "true", "tags.Compartment": "ABLRHF", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "infovault-dev-ablrhf-ssm-endpoint", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "9", "tags_all.AutoShutdown": "true", "tags_all.Compartment": "ABLRHF", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "infovault-dev-ablrhf-ssm-endpoint", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_endpoint_type": "Interface", "vpc_id": "vpc-0e75036a2ad9e5b4f"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_vpc_endpoint.tfer--vpce-04c5af7d63bb323fb": {"type": "aws_vpc_endpoint", "depends_on": [], "primary": {"id": "vpce-04c5af7d63bb323fb", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:************:vpc-endpoint/vpce-04c5af7d63bb323fb", "cidr_blocks.#": "0", "dns_entry.#": "6", "dns_entry.0.dns_name": "vpce-04c5af7d63bb323fb-gnx4mkw7.dkr.ecr.ap-southeast-1.vpce.amazonaws.com", "dns_entry.0.hosted_zone_id": "Z18LLCSTV4NVNL", "dns_entry.1.dns_name": "vpce-04c5af7d63bb323fb-gnx4mkw7-ap-southeast-1a.dkr.ecr.ap-southeast-1.vpce.amazonaws.com", "dns_entry.1.hosted_zone_id": "Z18LLCSTV4NVNL", "dns_entry.2.dns_name": "dkr.ecr.ap-southeast-1.amazonaws.com", "dns_entry.2.hosted_zone_id": "Z07052355B2BNTEJIWIV", "dns_entry.3.dns_name": "*.dkr.ecr.ap-southeast-1.amazonaws.com", "dns_entry.3.hosted_zone_id": "Z07052355B2BNTEJIWIV", "dns_entry.4.dns_name": "dkr-ecr.ap-southeast-1.on.aws", "dns_entry.4.hosted_zone_id": "Z07768742OXEJZ2T7XL4U", "dns_entry.5.dns_name": "*.dkr-ecr.ap-southeast-1.on.aws", "dns_entry.5.hosted_zone_id": "Z07768742OXEJZ2T7XL4U", "dns_options.#": "1", "dns_options.0.dns_record_ip_type": "ipv4", "dns_options.0.private_dns_only_for_inbound_resolver_endpoint": "false", "id": "vpce-04c5af7d63bb323fb", "ip_address_type": "ipv4", "network_interface_ids.#": "1", "network_interface_ids.0": "eni-096d098e8a09d429b", "owner_id": "************", "policy": "{\"Statement\":[{\"Action\":\"*\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"*\"}]}", "private_dns_enabled": "true", "requester_managed": "false", "resource_configuration_arn": "", "route_table_ids.#": "0", "security_group_ids.#": "1", "security_group_ids.0": "sg-00df94fe66c7464f5", "service_name": "com.amazonaws.ap-southeast-1.ecr.dkr", "service_network_arn": "", "service_region": "ap-southeast-1", "state": "available", "subnet_configuration.#": "1", "subnet_configuration.0.ipv4": "***********", "subnet_configuration.0.ipv6": "", "subnet_configuration.0.subnet_id": "subnet-07ab4b059d69e4044", "subnet_ids.#": "1", "subnet_ids.0": "subnet-07ab4b059d69e4044", "tags.%": "0", "tags_all.%": "0", "vpc_endpoint_type": "Interface", "vpc_id": "vpc-05f1b20a6634c6a38"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_vpc_endpoint.tfer--vpce-04d5a7ce83abf07b5": {"type": "aws_vpc_endpoint", "depends_on": [], "primary": {"id": "vpce-04d5a7ce83abf07b5", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:************:vpc-endpoint/vpce-04d5a7ce83abf07b5", "cidr_blocks.#": "0", "dns_entry.#": "4", "dns_entry.0.dns_name": "vpce-04d5a7ce83abf07b5-0jjodktv.ssmmessages.ap-southeast-1.vpce.amazonaws.com", "dns_entry.0.hosted_zone_id": "Z18LLCSTV4NVNL", "dns_entry.1.dns_name": "vpce-04d5a7ce83abf07b5-0jjodktv-ap-southeast-1a.ssmmessages.ap-southeast-1.vpce.amazonaws.com", "dns_entry.1.hosted_zone_id": "Z18LLCSTV4NVNL", "dns_entry.2.dns_name": "vpce-04d5a7ce83abf07b5-0jjodktv-ap-southeast-1b.ssmmessages.ap-southeast-1.vpce.amazonaws.com", "dns_entry.2.hosted_zone_id": "Z18LLCSTV4NVNL", "dns_entry.3.dns_name": "ssmmessages.ap-southeast-1.amazonaws.com", "dns_entry.3.hosted_zone_id": "Z09606202BH7LBYG3RCKG", "dns_options.#": "1", "dns_options.0.dns_record_ip_type": "ipv4", "dns_options.0.private_dns_only_for_inbound_resolver_endpoint": "false", "id": "vpce-04d5a7ce83abf07b5", "ip_address_type": "ipv4", "network_interface_ids.#": "2", "network_interface_ids.0": "eni-07b95a8cc49b899ea", "network_interface_ids.1": "eni-0a181a6d7acba6b00", "owner_id": "************", "policy": "{\"Statement\":[{\"Action\":\"*\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"*\"}]}", "private_dns_enabled": "true", "requester_managed": "false", "resource_configuration_arn": "", "route_table_ids.#": "0", "security_group_ids.#": "1", "security_group_ids.0": "sg-0d28b5ada1bb60c9a", "service_name": "com.amazonaws.ap-southeast-1.ssmmessages", "service_network_arn": "", "service_region": "ap-southeast-1", "state": "available", "subnet_configuration.#": "2", "subnet_configuration.0.ipv4": "***********", "subnet_configuration.0.ipv6": "", "subnet_configuration.0.subnet_id": "subnet-04910ced717a5584a", "subnet_configuration.1.ipv4": "***********", "subnet_configuration.1.ipv6": "", "subnet_configuration.1.subnet_id": "subnet-0ebbc016c2fc6b3db", "subnet_ids.#": "2", "subnet_ids.0": "subnet-04910ced717a5584a", "subnet_ids.1": "subnet-0ebbc016c2fc6b3db", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "infovault-dev-ssmmessages-endpoint", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "infovault-dev-ssmmessages-endpoint", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_endpoint_type": "Interface", "vpc_id": "vpc-05f1b20a6634c6a38"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_vpc_endpoint.tfer--vpce-0538ad39973dc4aae": {"type": "aws_vpc_endpoint", "depends_on": [], "primary": {"id": "vpce-0538ad39973dc4aae", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:************:vpc-endpoint/vpce-0538ad39973dc4aae", "cidr_blocks.#": "0", "dns_entry.#": "2", "dns_entry.0.dns_name": "vpce-0538ad39973dc4aae-go30kx7c.ec2messages.ap-southeast-1.vpce.amazonaws.com", "dns_entry.0.hosted_zone_id": "Z18LLCSTV4NVNL", "dns_entry.1.dns_name": "vpce-0538ad39973dc4aae-go30kx7c-ap-southeast-1a.ec2messages.ap-southeast-1.vpce.amazonaws.com", "dns_entry.1.hosted_zone_id": "Z18LLCSTV4NVNL", "dns_options.#": "1", "dns_options.0.dns_record_ip_type": "ipv4", "dns_options.0.private_dns_only_for_inbound_resolver_endpoint": "false", "id": "vpce-0538ad39973dc4aae", "ip_address_type": "ipv4", "network_interface_ids.#": "1", "network_interface_ids.0": "eni-07765172bcd661056", "owner_id": "************", "policy": "{\"Statement\":[{\"Action\":\"*\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"*\"}]}", "private_dns_enabled": "false", "requester_managed": "false", "resource_configuration_arn": "", "route_table_ids.#": "0", "security_group_ids.#": "1", "security_group_ids.0": "sg-066975c77a66a64d9", "service_name": "com.amazonaws.ap-southeast-1.ec2messages", "service_network_arn": "", "service_region": "ap-southeast-1", "state": "available", "subnet_configuration.#": "1", "subnet_configuration.0.ipv4": "**********", "subnet_configuration.0.ipv6": "", "subnet_configuration.0.subnet_id": "subnet-0a1802eb99e96cbed", "subnet_ids.#": "1", "subnet_ids.0": "subnet-0a1802eb99e96cbed", "tags.%": "0", "tags_all.%": "0", "vpc_endpoint_type": "Interface", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_vpc_endpoint.tfer--vpce-0657101f2b067804d": {"type": "aws_vpc_endpoint", "depends_on": [], "primary": {"id": "vpce-0657101f2b067804d", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:************:vpc-endpoint/vpce-0657101f2b067804d", "cidr_blocks.#": "10", "cidr_blocks.0": "***********/21", "cidr_blocks.1": "*********/23", "cidr_blocks.2": "*********/22", "cidr_blocks.3": "***********/22", "cidr_blocks.4": "************/22", "cidr_blocks.5": "************/21", "cidr_blocks.6": "************/21", "cidr_blocks.7": "**********/21", "cidr_blocks.8": "************/22", "cidr_blocks.9": "************/22", "dns_entry.#": "0", "dns_options.#": "0", "id": "vpce-0657101f2b067804d", "ip_address_type": "", "network_interface_ids.#": "0", "owner_id": "************", "policy": "{\"Statement\":[{\"Action\":\"*\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"*\"}],\"Version\":\"2008-10-17\"}", "prefix_list_id": "pl-6fa54006", "private_dns_enabled": "false", "requester_managed": "false", "resource_configuration_arn": "", "route_table_ids.#": "2", "route_table_ids.0": "rtb-089ed5e648707e2d9", "route_table_ids.1": "rtb-0aa2547a225d317bd", "security_group_ids.#": "0", "service_name": "com.amazonaws.ap-southeast-1.s3", "service_network_arn": "", "service_region": "ap-southeast-1", "state": "available", "subnet_configuration.#": "0", "subnet_ids.#": "0", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "infovault-dev-s3-endpoint", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "infovault-dev-s3-endpoint", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_endpoint_type": "Gateway", "vpc_id": "vpc-0e75036a2ad9e5b4f"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_vpc_endpoint.tfer--vpce-06c55fec3359d3a98": {"type": "aws_vpc_endpoint", "depends_on": [], "primary": {"id": "vpce-06c55fec3359d3a98", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:************:vpc-endpoint/vpce-06c55fec3359d3a98", "cidr_blocks.#": "10", "cidr_blocks.0": "***********/21", "cidr_blocks.1": "*********/23", "cidr_blocks.2": "*********/22", "cidr_blocks.3": "***********/22", "cidr_blocks.4": "************/22", "cidr_blocks.5": "************/21", "cidr_blocks.6": "************/21", "cidr_blocks.7": "**********/21", "cidr_blocks.8": "************/22", "cidr_blocks.9": "************/22", "dns_entry.#": "0", "dns_options.#": "0", "id": "vpce-06c55fec3359d3a98", "ip_address_type": "", "network_interface_ids.#": "0", "owner_id": "************", "policy": "{\"Statement\":[{\"Action\":\"*\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"*\"}],\"Version\":\"2008-10-17\"}", "prefix_list_id": "pl-6fa54006", "private_dns_enabled": "false", "requester_managed": "false", "resource_configuration_arn": "", "route_table_ids.#": "12", "route_table_ids.0": "rtb-015ae5889ca4aaadd", "route_table_ids.1": "rtb-038d861f618d80392", "route_table_ids.10": "rtb-0e47b2c134f9ec024", "route_table_ids.11": "rtb-0f6307e8c214b6a71", "route_table_ids.2": "rtb-04dea60d5aeca9def", "route_table_ids.3": "rtb-06ca622706fb0676d", "route_table_ids.4": "rtb-0795b2e3d3e3f88ed", "route_table_ids.5": "rtb-08edcd5b7304aa2aa", "route_table_ids.6": "rtb-098958cd03a46c219", "route_table_ids.7": "rtb-0a3ed8d3c391e4cca", "route_table_ids.8": "rtb-0c7da122446383cc5", "route_table_ids.9": "rtb-0d508af52f5965f6a", "security_group_ids.#": "0", "service_name": "com.amazonaws.ap-southeast-1.s3", "service_network_arn": "", "service_region": "ap-southeast-1", "state": "available", "subnet_configuration.#": "0", "subnet_ids.#": "0", "tags.%": "0", "tags_all.%": "0", "vpc_endpoint_type": "Gateway", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_vpc_endpoint.tfer--vpce-084596619ed02fac1": {"type": "aws_vpc_endpoint", "depends_on": [], "primary": {"id": "vpce-084596619ed02fac1", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:************:vpc-endpoint/vpce-084596619ed02fac1", "cidr_blocks.#": "0", "dns_entry.#": "3", "dns_entry.0.dns_name": "vpce-084596619ed02fac1-32dg6brq.ssm.ap-southeast-1.vpce.amazonaws.com", "dns_entry.0.hosted_zone_id": "Z18LLCSTV4NVNL", "dns_entry.1.dns_name": "vpce-084596619ed02fac1-32dg6brq-ap-southeast-1a.ssm.ap-southeast-1.vpce.amazonaws.com", "dns_entry.1.hosted_zone_id": "Z18LLCSTV4NVNL", "dns_entry.2.dns_name": "ssm.ap-southeast-1.amazonaws.com", "dns_entry.2.hosted_zone_id": "Z032663332VEI7BB5W97J", "dns_options.#": "1", "dns_options.0.dns_record_ip_type": "ipv4", "dns_options.0.private_dns_only_for_inbound_resolver_endpoint": "false", "id": "vpce-084596619ed02fac1", "ip_address_type": "ipv4", "network_interface_ids.#": "1", "network_interface_ids.0": "eni-020e308911f0f67e6", "owner_id": "************", "policy": "{\"Statement\":[{\"Action\":\"*\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"*\"}]}", "private_dns_enabled": "true", "requester_managed": "false", "resource_configuration_arn": "", "route_table_ids.#": "0", "security_group_ids.#": "1", "security_group_ids.0": "sg-0870c466d0db940c4", "service_name": "com.amazonaws.ap-southeast-1.ssm", "service_network_arn": "", "service_region": "ap-southeast-1", "state": "available", "subnet_configuration.#": "1", "subnet_configuration.0.ipv4": "**********", "subnet_configuration.0.ipv6": "", "subnet_configuration.0.subnet_id": "subnet-0dacd0ef604204014", "subnet_ids.#": "1", "subnet_ids.0": "subnet-0dacd0ef604204014", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "vpce-infovault-dev-Patching-Compartment-ssm", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "vpce-infovault-dev-Patching-Compartment-ssm", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_endpoint_type": "Interface", "vpc_id": "vpc-0ca0480cce82946b3"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_vpc_endpoint.tfer--vpce-08c51d0d6582b0aa6": {"type": "aws_vpc_endpoint", "depends_on": [], "primary": {"id": "vpce-08c51d0d6582b0aa6", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:************:vpc-endpoint/vpce-08c51d0d6582b0aa6", "cidr_blocks.#": "0", "dns_entry.#": "3", "dns_entry.0.dns_name": "vpce-08c51d0d6582b0aa6-nkmh3gvv.ssmmessages.ap-southeast-1.vpce.amazonaws.com", "dns_entry.0.hosted_zone_id": "Z18LLCSTV4NVNL", "dns_entry.1.dns_name": "vpce-08c51d0d6582b0aa6-nkmh3gvv-ap-southeast-1a.ssmmessages.ap-southeast-1.vpce.amazonaws.com", "dns_entry.1.hosted_zone_id": "Z18LLCSTV4NVNL", "dns_entry.2.dns_name": "ssmmessages.ap-southeast-1.amazonaws.com", "dns_entry.2.hosted_zone_id": "Z0394374H9F0A42OOAWD", "dns_options.#": "1", "dns_options.0.dns_record_ip_type": "ipv4", "dns_options.0.private_dns_only_for_inbound_resolver_endpoint": "false", "id": "vpce-08c51d0d6582b0aa6", "ip_address_type": "ipv4", "network_interface_ids.#": "1", "network_interface_ids.0": "eni-0a08f25491f59d58e", "owner_id": "************", "policy": "{\"Statement\":[{\"Action\":\"*\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"*\"}]}", "private_dns_enabled": "true", "requester_managed": "false", "resource_configuration_arn": "", "route_table_ids.#": "0", "security_group_ids.#": "1", "security_group_ids.0": "sg-066975c77a66a64d9", "service_name": "com.amazonaws.ap-southeast-1.ssmmessages", "service_network_arn": "", "service_region": "ap-southeast-1", "state": "available", "subnet_configuration.#": "1", "subnet_configuration.0.ipv4": "*********2", "subnet_configuration.0.ipv6": "", "subnet_configuration.0.subnet_id": "subnet-0a1802eb99e96cbed", "subnet_ids.#": "1", "subnet_ids.0": "subnet-0a1802eb99e96cbed", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "infovault-dev-gen-facing-compartment-ssmmessages-endpoint", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "infovault-dev-gen-facing-compartment-ssmmessages-endpoint", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_endpoint_type": "Interface", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_vpc_endpoint.tfer--vpce-094e78f395ad5f6f7": {"type": "aws_vpc_endpoint", "depends_on": [], "primary": {"id": "vpce-094e78f395ad5f6f7", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:************:vpc-endpoint/vpce-094e78f395ad5f6f7", "cidr_blocks.#": "0", "dns_entry.#": "3", "dns_entry.0.dns_name": "vpce-094e78f395ad5f6f7-pad4emri.ssm.ap-southeast-1.vpce.amazonaws.com", "dns_entry.0.hosted_zone_id": "Z18LLCSTV4NVNL", "dns_entry.1.dns_name": "vpce-094e78f395ad5f6f7-pad4emri-ap-southeast-1a.ssm.ap-southeast-1.vpce.amazonaws.com", "dns_entry.1.hosted_zone_id": "Z18LLCSTV4NVNL", "dns_entry.2.dns_name": "ssm.ap-southeast-1.amazonaws.com", "dns_entry.2.hosted_zone_id": "Z03970741YEHJZMF6M0I7", "dns_options.#": "1", "dns_options.0.dns_record_ip_type": "ipv4", "dns_options.0.private_dns_only_for_inbound_resolver_endpoint": "false", "id": "vpce-094e78f395ad5f6f7", "ip_address_type": "ipv4", "network_interface_ids.#": "1", "network_interface_ids.0": "eni-0e961cb5275d963d7", "owner_id": "************", "policy": "{\"Statement\":[{\"Action\":\"*\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"*\"}]}", "private_dns_enabled": "true", "requester_managed": "false", "resource_configuration_arn": "", "route_table_ids.#": "0", "security_group_ids.#": "1", "security_group_ids.0": "sg-066975c77a66a64d9", "service_name": "com.amazonaws.ap-southeast-1.ssm", "service_network_arn": "", "service_region": "ap-southeast-1", "state": "available", "subnet_configuration.#": "1", "subnet_configuration.0.ipv4": "*********0", "subnet_configuration.0.ipv6": "", "subnet_configuration.0.subnet_id": "subnet-0a1802eb99e96cbed", "subnet_ids.#": "1", "subnet_ids.0": "subnet-0a1802eb99e96cbed", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "infovault-dev-gen-facing-compartment-ssm-endpoint", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "infovault-dev-gen-facing-compartment-ssm-endpoint", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_endpoint_type": "Interface", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_vpc_endpoint.tfer--vpce-099452683bdb0a349": {"type": "aws_vpc_endpoint", "depends_on": [], "primary": {"id": "vpce-099452683bdb0a349", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:************:vpc-endpoint/vpce-099452683bdb0a349", "cidr_blocks.#": "0", "dns_entry.#": "7", "dns_entry.0.dns_name": "vpce-099452683bdb0a349-sqm1y3ha.dkr.ecr.ap-southeast-1.vpce.amazonaws.com", "dns_entry.0.hosted_zone_id": "Z18LLCSTV4NVNL", "dns_entry.1.dns_name": "vpce-099452683bdb0a349-sqm1y3ha-ap-southeast-1b.dkr.ecr.ap-southeast-1.vpce.amazonaws.com", "dns_entry.1.hosted_zone_id": "Z18LLCSTV4NVNL", "dns_entry.2.dns_name": "vpce-099452683bdb0a349-sqm1y3ha-ap-southeast-1a.dkr.ecr.ap-southeast-1.vpce.amazonaws.com", "dns_entry.2.hosted_zone_id": "Z18LLCSTV4NVNL", "dns_entry.3.dns_name": "dkr.ecr.ap-southeast-1.amazonaws.com", "dns_entry.3.hosted_zone_id": "Z013769261T1OV52HHW1", "dns_entry.4.dns_name": "*.dkr.ecr.ap-southeast-1.amazonaws.com", "dns_entry.4.hosted_zone_id": "Z013769261T1OV52HHW1", "dns_entry.5.dns_name": "dkr-ecr.ap-southeast-1.on.aws", "dns_entry.5.hosted_zone_id": "Z021909853XYIGURWSWE", "dns_entry.6.dns_name": "*.dkr-ecr.ap-southeast-1.on.aws", "dns_entry.6.hosted_zone_id": "Z021909853XYIGURWSWE", "dns_options.#": "1", "dns_options.0.dns_record_ip_type": "ipv4", "dns_options.0.private_dns_only_for_inbound_resolver_endpoint": "false", "id": "vpce-099452683bdb0a349", "ip_address_type": "ipv4", "network_interface_ids.#": "2", "network_interface_ids.0": "eni-02c628803f0bb3480", "network_interface_ids.1": "eni-030e4634c1ca971d9", "owner_id": "************", "policy": "{\"Statement\":[{\"Action\":\"*\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"*\"}]}", "private_dns_enabled": "true", "requester_managed": "false", "resource_configuration_arn": "", "route_table_ids.#": "0", "security_group_ids.#": "1", "security_group_ids.0": "sg-05f09a0dc7ab8c53f", "service_name": "com.amazonaws.ap-southeast-1.ecr.dkr", "service_network_arn": "", "service_region": "ap-southeast-1", "state": "available", "subnet_configuration.#": "2", "subnet_configuration.0.ipv4": "***********", "subnet_configuration.0.ipv6": "", "subnet_configuration.0.subnet_id": "subnet-023cafbedbb31d13e", "subnet_configuration.1.ipv4": "************", "subnet_configuration.1.ipv6": "", "subnet_configuration.1.subnet_id": "subnet-015e4b2acb87091d9", "subnet_ids.#": "2", "subnet_ids.0": "subnet-015e4b2acb87091d9", "subnet_ids.1": "subnet-023cafbedbb31d13e", "tags.%": "0", "tags_all.%": "0", "vpc_endpoint_type": "Interface", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_vpc_endpoint.tfer--vpce-0a2d395de8c81f0f9": {"type": "aws_vpc_endpoint", "depends_on": [], "primary": {"id": "vpce-0a2d395de8c81f0f9", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:************:vpc-endpoint/vpce-0a2d395de8c81f0f9", "cidr_blocks.#": "10", "cidr_blocks.0": "***********/21", "cidr_blocks.1": "*********/23", "cidr_blocks.2": "*********/22", "cidr_blocks.3": "***********/22", "cidr_blocks.4": "************/22", "cidr_blocks.5": "************/21", "cidr_blocks.6": "************/21", "cidr_blocks.7": "**********/21", "cidr_blocks.8": "************/22", "cidr_blocks.9": "************/22", "dns_entry.#": "0", "dns_options.#": "0", "id": "vpce-0a2d395de8c81f0f9", "ip_address_type": "", "network_interface_ids.#": "0", "owner_id": "************", "policy": "{\"Statement\":[{\"Action\":\"*\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"*\"}],\"Version\":\"2008-10-17\"}", "prefix_list_id": "pl-6fa54006", "private_dns_enabled": "false", "requester_managed": "false", "resource_configuration_arn": "", "route_table_ids.#": "2", "route_table_ids.0": "rtb-0445ba29313210ac5", "route_table_ids.1": "rtb-05df1c4be4e435f04", "security_group_ids.#": "0", "service_name": "com.amazonaws.ap-southeast-1.s3", "service_network_arn": "", "service_region": "ap-southeast-1", "state": "available", "subnet_configuration.#": "0", "subnet_ids.#": "0", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "infovault-dev-s3-endpoint", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "infovault-dev-s3-endpoint", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_endpoint_type": "Gateway", "vpc_id": "vpc-05f1b20a6634c6a38"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_vpc_endpoint.tfer--vpce-0aab6fc33b265ed8d": {"type": "aws_vpc_endpoint", "depends_on": [], "primary": {"id": "vpce-0aab6fc33b265ed8d", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:************:vpc-endpoint/vpce-0aab6fc33b265ed8d", "cidr_blocks.#": "0", "dns_entry.#": "0", "dns_options.#": "0", "id": "vpce-0aab6fc33b265ed8d", "ip_address_type": "ipv4", "network_interface_ids.#": "1", "network_interface_ids.0": "eni-0488dabede95ced8b", "owner_id": "************", "policy": "", "private_dns_enabled": "false", "requester_managed": "true", "resource_configuration_arn": "", "route_table_ids.#": "0", "security_group_ids.#": "0", "service_name": "com.amazonaws.vpce.ap-southeast-1.vpce-svc-00e0c13e3d8260a6f", "service_network_arn": "", "service_region": "ap-southeast-1", "state": "available", "subnet_configuration.#": "1", "subnet_configuration.0.ipv4": "***********", "subnet_configuration.0.ipv6": "", "subnet_configuration.0.subnet_id": "subnet-0aec855b39a7be02e", "subnet_ids.#": "1", "subnet_ids.0": "subnet-0aec855b39a7be02e", "tags.%": "3", "tags.AWSNetworkFirewallManaged": "true", "tags.Firewall": "arn:aws:network-firewall:ap-southeast-1:************:firewall/dev-net-anfw", "tags.Name": "dev-net-anfw (ap-southeast-1a)", "tags_all.%": "3", "tags_all.AWSNetworkFirewallManaged": "true", "tags_all.Firewall": "arn:aws:network-firewall:ap-southeast-1:************:firewall/dev-net-anfw", "tags_all.Name": "dev-net-anfw (ap-southeast-1a)", "vpc_endpoint_type": "GatewayLoadBalancer", "vpc_id": "vpc-037f619602fa293a1"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_vpc_endpoint.tfer--vpce-0ac5502093940b08f": {"type": "aws_vpc_endpoint", "depends_on": [], "primary": {"id": "vpce-0ac5502093940b08f", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:************:vpc-endpoint/vpce-0ac5502093940b08f", "cidr_blocks.#": "0", "dns_entry.#": "3", "dns_entry.0.dns_name": "vpce-0ac5502093940b08f-e4mq69gf.ssmmessages.ap-southeast-1.vpce.amazonaws.com", "dns_entry.0.hosted_zone_id": "Z18LLCSTV4NVNL", "dns_entry.1.dns_name": "vpce-0ac5502093940b08f-e4mq69gf-ap-southeast-1a.ssmmessages.ap-southeast-1.vpce.amazonaws.com", "dns_entry.1.hosted_zone_id": "Z18LLCSTV4NVNL", "dns_entry.2.dns_name": "ssmmessages.ap-southeast-1.amazonaws.com", "dns_entry.2.hosted_zone_id": "Z10302562IMRWJHIBJ590", "dns_options.#": "1", "dns_options.0.dns_record_ip_type": "ipv4", "dns_options.0.private_dns_only_for_inbound_resolver_endpoint": "false", "id": "vpce-0ac5502093940b08f", "ip_address_type": "ipv4", "network_interface_ids.#": "1", "network_interface_ids.0": "eni-0f51e5d7d6ccb7f1b", "owner_id": "************", "policy": "{\"Statement\":[{\"Action\":\"*\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"*\"}]}", "private_dns_enabled": "true", "requester_managed": "false", "resource_configuration_arn": "", "route_table_ids.#": "0", "security_group_ids.#": "1", "security_group_ids.0": "sg-082d3fc2b26b1004e", "service_name": "com.amazonaws.ap-southeast-1.ssmmessages", "service_network_arn": "", "service_region": "ap-southeast-1", "state": "available", "subnet_configuration.#": "1", "subnet_configuration.0.ipv4": "*********", "subnet_configuration.0.ipv6": "", "subnet_configuration.0.subnet_id": "subnet-0c6066202240c38be", "subnet_ids.#": "1", "subnet_ids.0": "subnet-0c6066202240c38be", "tags.%": "9", "tags.AutoShutdown": "true", "tags.Compartment": "ABLRHF", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "infovault-dev-ablrhf-ssmmessages-endpoint", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "9", "tags_all.AutoShutdown": "true", "tags_all.Compartment": "ABLRHF", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "infovault-dev-ablrhf-ssmmessages-endpoint", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_endpoint_type": "Interface", "vpc_id": "vpc-0e75036a2ad9e5b4f"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_vpc_endpoint.tfer--vpce-0bf3a106d192c71cd": {"type": "aws_vpc_endpoint", "depends_on": [], "primary": {"id": "vpce-0bf3a106d192c71cd", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:************:vpc-endpoint/vpce-0bf3a106d192c71cd", "cidr_blocks.#": "0", "dns_entry.#": "3", "dns_entry.0.dns_name": "vpce-0bf3a106d192c71cd-4x1auark.guardduty-data.ap-southeast-1.vpce.amazonaws.com", "dns_entry.0.hosted_zone_id": "Z18LLCSTV4NVNL", "dns_entry.1.dns_name": "vpce-0bf3a106d192c71cd-4x1auark-ap-southeast-1a.guardduty-data.ap-southeast-1.vpce.amazonaws.com", "dns_entry.1.hosted_zone_id": "Z18LLCSTV4NVNL", "dns_entry.2.dns_name": "guardduty-data.ap-southeast-1.amazonaws.com", "dns_entry.2.hosted_zone_id": "Z10223226JQD0ZDJPPBZ", "dns_options.#": "1", "dns_options.0.dns_record_ip_type": "ipv4", "dns_options.0.private_dns_only_for_inbound_resolver_endpoint": "false", "id": "vpce-0bf3a106d192c71cd", "ip_address_type": "ipv4", "network_interface_ids.#": "1", "network_interface_ids.0": "eni-04f8f5201882ec24e", "owner_id": "************", "policy": "{\"Statement\":[{\"Action\":\"*\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"*\"},{\"Action\":\"*\",\"Condition\":{\"StringNotEquals\":{\"aws:PrincipalAccount\":\"************\"}},\"Effect\":\"<PERSON><PERSON>\",\"Principal\":\"*\",\"Resource\":\"*\"}],\"Version\":\"2012-10-17\"}", "private_dns_enabled": "true", "requester_managed": "false", "resource_configuration_arn": "", "route_table_ids.#": "0", "security_group_ids.#": "1", "security_group_ids.0": "sg-0455dd83ca1b155d3", "service_name": "com.amazonaws.ap-southeast-1.guardduty-data", "service_network_arn": "", "service_region": "ap-southeast-1", "state": "available", "subnet_configuration.#": "1", "subnet_configuration.0.ipv4": "***********", "subnet_configuration.0.ipv6": "", "subnet_configuration.0.subnet_id": "subnet-01e871c49dc4b9b1f", "subnet_ids.#": "1", "subnet_ids.0": "subnet-01e871c49dc4b9b1f", "tags.%": "1", "tags.GuardDutyManaged": "true", "tags_all.%": "1", "tags_all.GuardDutyManaged": "true", "vpc_endpoint_type": "Interface", "vpc_id": "vpc-037f619602fa293a1"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_vpc_endpoint.tfer--vpce-0c5ff88c30a3e428d": {"type": "aws_vpc_endpoint", "depends_on": [], "primary": {"id": "vpce-0c5ff88c30a3e428d", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:************:vpc-endpoint/vpce-0c5ff88c30a3e428d", "cidr_blocks.#": "0", "dns_entry.#": "4", "dns_entry.0.dns_name": "vpce-0c5ff88c30a3e428d-cujmm4wy.guardduty-data.ap-southeast-1.vpce.amazonaws.com", "dns_entry.0.hosted_zone_id": "Z18LLCSTV4NVNL", "dns_entry.1.dns_name": "vpce-0c5ff88c30a3e428d-cujmm4wy-ap-southeast-1a.guardduty-data.ap-southeast-1.vpce.amazonaws.com", "dns_entry.1.hosted_zone_id": "Z18LLCSTV4NVNL", "dns_entry.2.dns_name": "vpce-0c5ff88c30a3e428d-cujmm4wy-ap-southeast-1b.guardduty-data.ap-southeast-1.vpce.amazonaws.com", "dns_entry.2.hosted_zone_id": "Z18LLCSTV4NVNL", "dns_entry.3.dns_name": "guardduty-data.ap-southeast-1.amazonaws.com", "dns_entry.3.hosted_zone_id": "Z0593057374CC0YA3IZQH", "dns_options.#": "1", "dns_options.0.dns_record_ip_type": "ipv4", "dns_options.0.private_dns_only_for_inbound_resolver_endpoint": "false", "id": "vpce-0c5ff88c30a3e428d", "ip_address_type": "ipv4", "network_interface_ids.#": "2", "network_interface_ids.0": "eni-05708b1c080c287b1", "network_interface_ids.1": "eni-0a4a1d4b782759c8d", "owner_id": "************", "policy": "{\"Statement\":[{\"Action\":\"*\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"*\"},{\"Action\":\"*\",\"Condition\":{\"StringNotEquals\":{\"aws:PrincipalAccount\":\"************\"}},\"Effect\":\"<PERSON><PERSON>\",\"Principal\":\"*\",\"Resource\":\"*\"}],\"Version\":\"2012-10-17\"}", "private_dns_enabled": "true", "requester_managed": "false", "resource_configuration_arn": "", "route_table_ids.#": "0", "security_group_ids.#": "1", "security_group_ids.0": "sg-065d1e24d43126489", "service_name": "com.amazonaws.ap-southeast-1.guardduty-data", "service_network_arn": "", "service_region": "ap-southeast-1", "state": "available", "subnet_configuration.#": "2", "subnet_configuration.0.ipv4": "***********", "subnet_configuration.0.ipv6": "", "subnet_configuration.0.subnet_id": "subnet-07ab4b059d69e4044", "subnet_configuration.1.ipv4": "***********", "subnet_configuration.1.ipv6": "", "subnet_configuration.1.subnet_id": "subnet-04910ced717a5584a", "subnet_ids.#": "2", "subnet_ids.0": "subnet-04910ced717a5584a", "subnet_ids.1": "subnet-07ab4b059d69e4044", "tags.%": "1", "tags.GuardDutyManaged": "true", "tags_all.%": "1", "tags_all.GuardDutyManaged": "true", "vpc_endpoint_type": "Interface", "vpc_id": "vpc-05f1b20a6634c6a38"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_vpc_endpoint.tfer--vpce-0c7798e909bd72ff8": {"type": "aws_vpc_endpoint", "depends_on": [], "primary": {"id": "vpce-0c7798e909bd72ff8", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:************:vpc-endpoint/vpce-0c7798e909bd72ff8", "cidr_blocks.#": "0", "dns_entry.#": "5", "dns_entry.0.dns_name": "vpce-0c7798e909bd72ff8-fsqoefpq.eks.ap-southeast-1.vpce.amazonaws.com", "dns_entry.0.hosted_zone_id": "Z18LLCSTV4NVNL", "dns_entry.1.dns_name": "vpce-0c7798e909bd72ff8-fsqoefpq-ap-southeast-1b.eks.ap-southeast-1.vpce.amazonaws.com", "dns_entry.1.hosted_zone_id": "Z18LLCSTV4NVNL", "dns_entry.2.dns_name": "vpce-0c7798e909bd72ff8-fsqoefpq-ap-southeast-1a.eks.ap-southeast-1.vpce.amazonaws.com", "dns_entry.2.hosted_zone_id": "Z18LLCSTV4NVNL", "dns_entry.3.dns_name": "eks.ap-southeast-1.amazonaws.com", "dns_entry.3.hosted_zone_id": "Z0219466S42L3UUOTQAO", "dns_entry.4.dns_name": "eks.ap-southeast-1.api.aws", "dns_entry.4.hosted_zone_id": "Z01168493QU5BLC5P4LLU", "dns_options.#": "1", "dns_options.0.dns_record_ip_type": "ipv4", "dns_options.0.private_dns_only_for_inbound_resolver_endpoint": "false", "id": "vpce-0c7798e909bd72ff8", "ip_address_type": "ipv4", "network_interface_ids.#": "2", "network_interface_ids.0": "eni-0c4e492f96b742a6f", "network_interface_ids.1": "eni-0edd3b7c8064ca22e", "owner_id": "************", "policy": "{\"Statement\":[{\"Action\":\"*\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"*\"}]}", "private_dns_enabled": "true", "requester_managed": "false", "resource_configuration_arn": "", "route_table_ids.#": "0", "security_group_ids.#": "1", "security_group_ids.0": "sg-05f09a0dc7ab8c53f", "service_name": "com.amazonaws.ap-southeast-1.eks", "service_network_arn": "", "service_region": "ap-southeast-1", "state": "available", "subnet_configuration.#": "2", "subnet_configuration.0.ipv4": "***********", "subnet_configuration.0.ipv6": "", "subnet_configuration.0.subnet_id": "subnet-023cafbedbb31d13e", "subnet_configuration.1.ipv4": "************", "subnet_configuration.1.ipv6": "", "subnet_configuration.1.subnet_id": "subnet-015e4b2acb87091d9", "subnet_ids.#": "2", "subnet_ids.0": "subnet-015e4b2acb87091d9", "subnet_ids.1": "subnet-023cafbedbb31d13e", "tags.%": "0", "tags_all.%": "0", "vpc_endpoint_type": "Interface", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_vpc_endpoint.tfer--vpce-0cb9adf21ebb65e3b": {"type": "aws_vpc_endpoint", "depends_on": [], "primary": {"id": "vpce-0cb9adf21ebb65e3b", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:************:vpc-endpoint/vpce-0cb9adf21ebb65e3b", "cidr_blocks.#": "10", "cidr_blocks.0": "***********/21", "cidr_blocks.1": "*********/23", "cidr_blocks.2": "*********/22", "cidr_blocks.3": "***********/22", "cidr_blocks.4": "************/22", "cidr_blocks.5": "************/21", "cidr_blocks.6": "************/21", "cidr_blocks.7": "**********/21", "cidr_blocks.8": "************/22", "cidr_blocks.9": "************/22", "dns_entry.#": "0", "dns_options.#": "0", "id": "vpce-0cb9adf21ebb65e3b", "ip_address_type": "", "network_interface_ids.#": "0", "owner_id": "************", "policy": "{\"Statement\":[{\"Action\":\"*\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"*\"}],\"Version\":\"2012-10-17\"}", "prefix_list_id": "pl-6fa54006", "private_dns_enabled": "false", "requester_managed": "false", "resource_configuration_arn": "", "route_table_ids.#": "3", "route_table_ids.0": "rtb-02a9859d16aa6e04b", "route_table_ids.1": "rtb-048f93d202df7cd40", "route_table_ids.2": "rtb-09e7342b3a3bc5507", "security_group_ids.#": "0", "service_name": "com.amazonaws.ap-southeast-1.s3", "service_network_arn": "", "service_region": "ap-southeast-1", "state": "available", "subnet_configuration.#": "0", "subnet_ids.#": "0", "tags.%": "0", "tags_all.%": "0", "vpc_endpoint_type": "Gateway", "vpc_id": "vpc-02282d8d4c9c7764c"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_vpc_endpoint.tfer--vpce-0cd55ff1d8ac0f61f": {"type": "aws_vpc_endpoint", "depends_on": [], "primary": {"id": "vpce-0cd55ff1d8ac0f61f", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:************:vpc-endpoint/vpce-0cd55ff1d8ac0f61f", "cidr_blocks.#": "0", "dns_entry.#": "4", "dns_entry.0.dns_name": "vpce-0cd55ff1d8ac0f61f-k1dyqlo6.ssm.ap-southeast-1.vpce.amazonaws.com", "dns_entry.0.hosted_zone_id": "Z18LLCSTV4NVNL", "dns_entry.1.dns_name": "vpce-0cd55ff1d8ac0f61f-k1dyqlo6-ap-southeast-1a.ssm.ap-southeast-1.vpce.amazonaws.com", "dns_entry.1.hosted_zone_id": "Z18LLCSTV4NVNL", "dns_entry.2.dns_name": "vpce-0cd55ff1d8ac0f61f-k1dyqlo6-ap-southeast-1b.ssm.ap-southeast-1.vpce.amazonaws.com", "dns_entry.2.hosted_zone_id": "Z18LLCSTV4NVNL", "dns_entry.3.dns_name": "ssm.ap-southeast-1.amazonaws.com", "dns_entry.3.hosted_zone_id": "Z0958828HIJGSZW8YGY7", "dns_options.#": "1", "dns_options.0.dns_record_ip_type": "ipv4", "dns_options.0.private_dns_only_for_inbound_resolver_endpoint": "false", "id": "vpce-0cd55ff1d8ac0f61f", "ip_address_type": "ipv4", "network_interface_ids.#": "2", "network_interface_ids.0": "eni-0000fc2c82b0ad41e", "network_interface_ids.1": "eni-0196ea3e075caabb4", "owner_id": "************", "policy": "{\"Statement\":[{\"Action\":\"*\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"*\"}]}", "private_dns_enabled": "true", "requester_managed": "false", "resource_configuration_arn": "", "route_table_ids.#": "0", "security_group_ids.#": "1", "security_group_ids.0": "sg-0d28b5ada1bb60c9a", "service_name": "com.amazonaws.ap-southeast-1.ssm", "service_network_arn": "", "service_region": "ap-southeast-1", "state": "available", "subnet_configuration.#": "2", "subnet_configuration.0.ipv4": "***********", "subnet_configuration.0.ipv6": "", "subnet_configuration.0.subnet_id": "subnet-04910ced717a5584a", "subnet_configuration.1.ipv4": "***********", "subnet_configuration.1.ipv6": "", "subnet_configuration.1.subnet_id": "subnet-0ebbc016c2fc6b3db", "subnet_ids.#": "2", "subnet_ids.0": "subnet-04910ced717a5584a", "subnet_ids.1": "subnet-0ebbc016c2fc6b3db", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "infovault-dev-ssm-endpoint", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "infovault-dev-ssm-endpoint", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_endpoint_type": "Interface", "vpc_id": "vpc-05f1b20a6634c6a38"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_vpc_endpoint.tfer--vpce-0ce3dc776f7700c04": {"type": "aws_vpc_endpoint", "depends_on": [], "primary": {"id": "vpce-0ce3dc776f7700c04", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:************:vpc-endpoint/vpce-0ce3dc776f7700c04", "cidr_blocks.#": "0", "dns_entry.#": "4", "dns_entry.0.dns_name": "vpce-0ce3dc776f7700c04-tb1cyntf.ec2messages.ap-southeast-1.vpce.amazonaws.com", "dns_entry.0.hosted_zone_id": "Z18LLCSTV4NVNL", "dns_entry.1.dns_name": "vpce-0ce3dc776f7700c04-tb1cyntf-ap-southeast-1b.ec2messages.ap-southeast-1.vpce.amazonaws.com", "dns_entry.1.hosted_zone_id": "Z18LLCSTV4NVNL", "dns_entry.2.dns_name": "vpce-0ce3dc776f7700c04-tb1cyntf-ap-southeast-1a.ec2messages.ap-southeast-1.vpce.amazonaws.com", "dns_entry.2.hosted_zone_id": "Z18LLCSTV4NVNL", "dns_entry.3.dns_name": "ec2messages.ap-southeast-1.amazonaws.com", "dns_entry.3.hosted_zone_id": "Z09597411QP5Y7JY82NQN", "dns_options.#": "1", "dns_options.0.dns_record_ip_type": "ipv4", "dns_options.0.private_dns_only_for_inbound_resolver_endpoint": "false", "id": "vpce-0ce3dc776f7700c04", "ip_address_type": "ipv4", "network_interface_ids.#": "2", "network_interface_ids.0": "eni-0b0c10f4ff924ca83", "network_interface_ids.1": "eni-0f4f360a445263ef1", "owner_id": "************", "policy": "{\"Statement\":[{\"Action\":\"*\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"*\"}]}", "private_dns_enabled": "true", "requester_managed": "false", "resource_configuration_arn": "", "route_table_ids.#": "0", "security_group_ids.#": "1", "security_group_ids.0": "sg-0d28b5ada1bb60c9a", "service_name": "com.amazonaws.ap-southeast-1.ec2messages", "service_network_arn": "", "service_region": "ap-southeast-1", "state": "available", "subnet_configuration.#": "2", "subnet_configuration.0.ipv4": "***********", "subnet_configuration.0.ipv6": "", "subnet_configuration.0.subnet_id": "subnet-04910ced717a5584a", "subnet_configuration.1.ipv4": "***********", "subnet_configuration.1.ipv6": "", "subnet_configuration.1.subnet_id": "subnet-0ebbc016c2fc6b3db", "subnet_ids.#": "2", "subnet_ids.0": "subnet-04910ced717a5584a", "subnet_ids.1": "subnet-0ebbc016c2fc6b3db", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "infovault-dev-ec2messages-endpoint", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "infovault-dev-ec2messages-endpoint", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_endpoint_type": "Interface", "vpc_id": "vpc-05f1b20a6634c6a38"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_vpc_endpoint.tfer--vpce-0d5ebafd2bcf055bf": {"type": "aws_vpc_endpoint", "depends_on": [], "primary": {"id": "vpce-0d5ebafd2bcf055bf", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:************:vpc-endpoint/vpce-0d5ebafd2bcf055bf", "cidr_blocks.#": "0", "dns_entry.#": "3", "dns_entry.0.dns_name": "vpce-0d5ebafd2bcf055bf-12ag80i7.guardduty-data.ap-southeast-1.vpce.amazonaws.com", "dns_entry.0.hosted_zone_id": "Z18LLCSTV4NVNL", "dns_entry.1.dns_name": "vpce-0d5ebafd2bcf055bf-12ag80i7-ap-southeast-1a.guardduty-data.ap-southeast-1.vpce.amazonaws.com", "dns_entry.1.hosted_zone_id": "Z18LLCSTV4NVNL", "dns_entry.2.dns_name": "guardduty-data.ap-southeast-1.amazonaws.com", "dns_entry.2.hosted_zone_id": "Z06775512JRRABGPETGI7", "dns_options.#": "1", "dns_options.0.dns_record_ip_type": "ipv4", "dns_options.0.private_dns_only_for_inbound_resolver_endpoint": "false", "id": "vpce-0d5ebafd2bcf055bf", "ip_address_type": "ipv4", "network_interface_ids.#": "1", "network_interface_ids.0": "eni-07f709056a9c94268", "owner_id": "************", "policy": "{\"Statement\":[{\"Action\":\"*\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"*\"},{\"Action\":\"*\",\"Condition\":{\"StringNotEquals\":{\"aws:PrincipalAccount\":\"************\"}},\"Effect\":\"<PERSON><PERSON>\",\"Principal\":\"*\",\"Resource\":\"*\"}],\"Version\":\"2012-10-17\"}", "private_dns_enabled": "true", "requester_managed": "false", "resource_configuration_arn": "", "route_table_ids.#": "0", "security_group_ids.#": "1", "security_group_ids.0": "sg-0650a95e2c0c1aaf5", "service_name": "com.amazonaws.ap-southeast-1.guardduty-data", "service_network_arn": "", "service_region": "ap-southeast-1", "state": "available", "subnet_configuration.#": "1", "subnet_configuration.0.ipv4": "**********", "subnet_configuration.0.ipv6": "", "subnet_configuration.0.subnet_id": "subnet-0a1802eb99e96cbed", "subnet_ids.#": "1", "subnet_ids.0": "subnet-0a1802eb99e96cbed", "tags.%": "1", "tags.GuardDutyManaged": "true", "tags_all.%": "1", "tags_all.GuardDutyManaged": "true", "vpc_endpoint_type": "Interface", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_vpc_endpoint.tfer--vpce-0ece27853f422d3a6": {"type": "aws_vpc_endpoint", "depends_on": [], "primary": {"id": "vpce-0ece27853f422d3a6", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:************:vpc-endpoint/vpce-0ece27853f422d3a6", "cidr_blocks.#": "0", "dns_entry.#": "3", "dns_entry.0.dns_name": "vpce-0ece27853f422d3a6-nqgzz65l.ec2messages.ap-southeast-1.vpce.amazonaws.com", "dns_entry.0.hosted_zone_id": "Z18LLCSTV4NVNL", "dns_entry.1.dns_name": "vpce-0ece27853f422d3a6-nqgzz65l-ap-southeast-1a.ec2messages.ap-southeast-1.vpce.amazonaws.com", "dns_entry.1.hosted_zone_id": "Z18LLCSTV4NVNL", "dns_entry.2.dns_name": "ec2messages.ap-southeast-1.amazonaws.com", "dns_entry.2.hosted_zone_id": "Z102977627YIBNY9SPDU3", "dns_options.#": "1", "dns_options.0.dns_record_ip_type": "ipv4", "dns_options.0.private_dns_only_for_inbound_resolver_endpoint": "false", "id": "vpce-0ece27853f422d3a6", "ip_address_type": "ipv4", "network_interface_ids.#": "1", "network_interface_ids.0": "eni-0958f29b852894cd2", "owner_id": "************", "policy": "{\"Statement\":[{\"Action\":\"*\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"*\"}]}", "private_dns_enabled": "true", "requester_managed": "false", "resource_configuration_arn": "", "route_table_ids.#": "0", "security_group_ids.#": "1", "security_group_ids.0": "sg-082d3fc2b26b1004e", "service_name": "com.amazonaws.ap-southeast-1.ec2messages", "service_network_arn": "", "service_region": "ap-southeast-1", "state": "available", "subnet_configuration.#": "1", "subnet_configuration.0.ipv4": "*********", "subnet_configuration.0.ipv6": "", "subnet_configuration.0.subnet_id": "subnet-0c6066202240c38be", "subnet_ids.#": "1", "subnet_ids.0": "subnet-0c6066202240c38be", "tags.%": "9", "tags.AutoShutdown": "true", "tags.Compartment": "ABLRHF", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "infovault-dev-ablrhf-ec2messages-endpoint", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "9", "tags_all.AutoShutdown": "true", "tags_all.Compartment": "ABLRHF", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "infovault-dev-ablrhf-ec2messages-endpoint", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_endpoint_type": "Interface", "vpc_id": "vpc-0e75036a2ad9e5b4f"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_vpc_endpoint.tfer--vpce-0ed0b1bdb378589bc": {"type": "aws_vpc_endpoint", "depends_on": [], "primary": {"id": "vpce-0ed0b1bdb378589bc", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:************:vpc-endpoint/vpce-0ed0b1bdb378589bc", "cidr_blocks.#": "0", "dns_entry.#": "5", "dns_entry.0.dns_name": "vpce-0ed0b1bdb378589bc-heh5p3eq.ec2.ap-southeast-1.vpce.amazonaws.com", "dns_entry.0.hosted_zone_id": "Z18LLCSTV4NVNL", "dns_entry.1.dns_name": "vpce-0ed0b1bdb378589bc-heh5p3eq-ap-southeast-1b.ec2.ap-southeast-1.vpce.amazonaws.com", "dns_entry.1.hosted_zone_id": "Z18LLCSTV4NVNL", "dns_entry.2.dns_name": "vpce-0ed0b1bdb378589bc-heh5p3eq-ap-southeast-1a.ec2.ap-southeast-1.vpce.amazonaws.com", "dns_entry.2.hosted_zone_id": "Z18LLCSTV4NVNL", "dns_entry.3.dns_name": "ec2.ap-southeast-1.amazonaws.com", "dns_entry.3.hosted_zone_id": "Z01290687JRL0CEMEDH7", "dns_entry.4.dns_name": "ec2.ap-southeast-1.api.aws", "dns_entry.4.hosted_zone_id": "Z01286462P9IUFQ0GN5TZ", "dns_options.#": "1", "dns_options.0.dns_record_ip_type": "ipv4", "dns_options.0.private_dns_only_for_inbound_resolver_endpoint": "false", "id": "vpce-0ed0b1bdb378589bc", "ip_address_type": "ipv4", "network_interface_ids.#": "2", "network_interface_ids.0": "eni-01983a4e02f2205aa", "network_interface_ids.1": "eni-03394e0cbba7b7f65", "owner_id": "************", "policy": "{\"Statement\":[{\"Action\":\"*\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"*\"}]}", "private_dns_enabled": "true", "requester_managed": "false", "resource_configuration_arn": "", "route_table_ids.#": "0", "security_group_ids.#": "1", "security_group_ids.0": "sg-05f09a0dc7ab8c53f", "service_name": "com.amazonaws.ap-southeast-1.ec2", "service_network_arn": "", "service_region": "ap-southeast-1", "state": "available", "subnet_configuration.#": "2", "subnet_configuration.0.ipv4": "************", "subnet_configuration.0.ipv6": "", "subnet_configuration.0.subnet_id": "subnet-023cafbedbb31d13e", "subnet_configuration.1.ipv4": "************", "subnet_configuration.1.ipv6": "", "subnet_configuration.1.subnet_id": "subnet-015e4b2acb87091d9", "subnet_ids.#": "2", "subnet_ids.0": "subnet-015e4b2acb87091d9", "subnet_ids.1": "subnet-023cafbedbb31d13e", "tags.%": "0", "tags_all.%": "0", "vpc_endpoint_type": "Interface", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_vpc_endpoint.tfer--vpce-0f0b9eaa555525680": {"type": "aws_vpc_endpoint", "depends_on": [], "primary": {"id": "vpce-0f0b9eaa555525680", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:************:vpc-endpoint/vpce-0f0b9eaa555525680", "cidr_blocks.#": "0", "dns_entry.#": "2", "dns_entry.0.dns_name": "vpce-0f0b9eaa555525680-bi8atct9.ssm.ap-southeast-1.vpce.amazonaws.com", "dns_entry.0.hosted_zone_id": "Z18LLCSTV4NVNL", "dns_entry.1.dns_name": "vpce-0f0b9eaa555525680-bi8atct9-ap-southeast-1a.ssm.ap-southeast-1.vpce.amazonaws.com", "dns_entry.1.hosted_zone_id": "Z18LLCSTV4NVNL", "dns_options.#": "1", "dns_options.0.dns_record_ip_type": "ipv4", "dns_options.0.private_dns_only_for_inbound_resolver_endpoint": "false", "id": "vpce-0f0b9eaa555525680", "ip_address_type": "ipv4", "network_interface_ids.#": "1", "network_interface_ids.0": "eni-07e82bc1231965e15", "owner_id": "************", "policy": "{\"Statement\":[{\"Action\":\"*\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"*\"}]}", "private_dns_enabled": "false", "requester_managed": "false", "resource_configuration_arn": "", "route_table_ids.#": "0", "security_group_ids.#": "1", "security_group_ids.0": "sg-066975c77a66a64d9", "service_name": "com.amazonaws.ap-southeast-1.ssm", "service_network_arn": "", "service_region": "ap-southeast-1", "state": "available", "subnet_configuration.#": "1", "subnet_configuration.0.ipv4": "**********", "subnet_configuration.0.ipv6": "", "subnet_configuration.0.subnet_id": "subnet-0a1802eb99e96cbed", "subnet_ids.#": "1", "subnet_ids.0": "subnet-0a1802eb99e96cbed", "tags.%": "0", "tags_all.%": "0", "vpc_endpoint_type": "Interface", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_vpc_endpoint.tfer--vpce-0f629db35d2992350": {"type": "aws_vpc_endpoint", "depends_on": [], "primary": {"id": "vpce-0f629db35d2992350", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:************:vpc-endpoint/vpce-0f629db35d2992350", "cidr_blocks.#": "0", "dns_entry.#": "3", "dns_entry.0.dns_name": "vpce-0f629db35d2992350-ekum4rz5.guardduty-data.ap-southeast-1.vpce.amazonaws.com", "dns_entry.0.hosted_zone_id": "Z18LLCSTV4NVNL", "dns_entry.1.dns_name": "vpce-0f629db35d2992350-ekum4rz5-ap-southeast-1a.guardduty-data.ap-southeast-1.vpce.amazonaws.com", "dns_entry.1.hosted_zone_id": "Z18LLCSTV4NVNL", "dns_entry.2.dns_name": "guardduty-data.ap-southeast-1.amazonaws.com", "dns_entry.2.hosted_zone_id": "Z07683942R6ZDY0NPISQU", "dns_options.#": "1", "dns_options.0.dns_record_ip_type": "ipv4", "dns_options.0.private_dns_only_for_inbound_resolver_endpoint": "false", "id": "vpce-0f629db35d2992350", "ip_address_type": "ipv4", "network_interface_ids.#": "1", "network_interface_ids.0": "eni-0cd213280801297e2", "owner_id": "************", "policy": "{\"Statement\":[{\"Action\":\"*\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"*\"},{\"Action\":\"*\",\"Condition\":{\"StringNotEquals\":{\"aws:PrincipalAccount\":\"************\"}},\"Effect\":\"<PERSON><PERSON>\",\"Principal\":\"*\",\"Resource\":\"*\"}],\"Version\":\"2012-10-17\"}", "private_dns_enabled": "true", "requester_managed": "false", "resource_configuration_arn": "", "route_table_ids.#": "0", "security_group_ids.#": "1", "security_group_ids.0": "sg-0330a127b43c54ab2", "service_name": "com.amazonaws.ap-southeast-1.guardduty-data", "service_network_arn": "", "service_region": "ap-southeast-1", "state": "available", "subnet_configuration.#": "1", "subnet_configuration.0.ipv4": "***********", "subnet_configuration.0.ipv6": "", "subnet_configuration.0.subnet_id": "subnet-0dacd0ef604204014", "subnet_ids.#": "1", "subnet_ids.0": "subnet-0dacd0ef604204014", "tags.%": "1", "tags.GuardDutyManaged": "true", "tags_all.%": "1", "tags_all.GuardDutyManaged": "true", "vpc_endpoint_type": "Interface", "vpc_id": "vpc-0ca0480cce82946b3"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_vpc_endpoint.tfer--vpce-0fe5ae48246ae8a9d": {"type": "aws_vpc_endpoint", "depends_on": [], "primary": {"id": "vpce-0fe5ae48246ae8a9d", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:************:vpc-endpoint/vpce-0fe5ae48246ae8a9d", "cidr_blocks.#": "0", "dns_entry.#": "4", "dns_entry.0.dns_name": "vpce-0fe5ae48246ae8a9d-j4y70ppd.api.ecr.ap-southeast-1.vpce.amazonaws.com", "dns_entry.0.hosted_zone_id": "Z18LLCSTV4NVNL", "dns_entry.1.dns_name": "vpce-0fe5ae48246ae8a9d-j4y70ppd-ap-southeast-1a.api.ecr.ap-southeast-1.vpce.amazonaws.com", "dns_entry.1.hosted_zone_id": "Z18LLCSTV4NVNL", "dns_entry.2.dns_name": "api.ecr.ap-southeast-1.amazonaws.com", "dns_entry.2.hosted_zone_id": "Z07556601H2RLEJCD2J2N", "dns_entry.3.dns_name": "ecr.ap-southeast-1.api.aws", "dns_entry.3.hosted_zone_id": "Z09187261XRACUJ4QEPJ", "dns_options.#": "1", "dns_options.0.dns_record_ip_type": "ipv4", "dns_options.0.private_dns_only_for_inbound_resolver_endpoint": "false", "id": "vpce-0fe5ae48246ae8a9d", "ip_address_type": "ipv4", "network_interface_ids.#": "1", "network_interface_ids.0": "eni-05f08707c0961ac34", "owner_id": "************", "policy": "{\"Statement\":[{\"Action\":\"*\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"*\"}]}", "private_dns_enabled": "true", "requester_managed": "false", "resource_configuration_arn": "", "route_table_ids.#": "0", "security_group_ids.#": "1", "security_group_ids.0": "sg-00df94fe66c7464f5", "service_name": "com.amazonaws.ap-southeast-1.ecr.api", "service_network_arn": "", "service_region": "ap-southeast-1", "state": "available", "subnet_configuration.#": "1", "subnet_configuration.0.ipv4": "**********", "subnet_configuration.0.ipv6": "", "subnet_configuration.0.subnet_id": "subnet-07ab4b059d69e4044", "subnet_ids.#": "1", "subnet_ids.0": "subnet-07ab4b059d69e4044", "tags.%": "0", "tags_all.%": "0", "vpc_endpoint_type": "Interface", "vpc_id": "vpc-05f1b20a6634c6a38"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}}, "depends_on": []}]}