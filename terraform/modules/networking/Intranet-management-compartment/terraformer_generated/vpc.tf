# Generated by Terraformer for VPC vpc-05f1b20a6634c6a38
# Source: /Users/<USER>/Documents/Projects/infovault-infrastructure/terraform/modules/networking/Intranet-management-compartment/raw_terraformer/aws/vpc/vpc.tf
# Generated on: 2025-06-09 13:16:58

resource "aws_vpc" "tfer--vpc-05f1b20a6634c6a38" {
  assign_generated_ipv6_cidr_block     = "false"
  cidr_block                           = "**********/26"
  enable_dns_hostnames                 = "true"
  enable_dns_support                   = "true"
  enable_network_address_usage_metrics = "false"
  instance_tenancy                     = "default"
  ipv6_netmask_length                  = "0"

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovalut-dev-Intranet-management-compartment"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovalut-dev-Intranet-management-compartment"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }
}
