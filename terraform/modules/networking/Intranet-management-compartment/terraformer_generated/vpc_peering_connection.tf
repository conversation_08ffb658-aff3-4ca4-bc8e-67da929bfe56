# Generated by Terraformer for VPC vpc-05f1b20a6634c6a38
# Source: /Users/<USER>/Documents/Projects/infovault-infrastructure/terraform/modules/networking/Intranet-management-compartment/raw_terraformer/aws/vpc_peering/vpc_peering_connection.tf
# Generated on: 2025-06-09 13:16:58

resource "aws_vpc_peering_connection" "tfer--pcx-00306eabadaf4a547" {
  accepter {
    allow_remote_vpc_dns_resolution = "false"
  }

  peer_owner_id = "046276255144"
  peer_region   = "ap-southeast-1"
  peer_vpc_id   = "vpc-05e0e8104b3321c40"

  requester {
    allow_remote_vpc_dns_resolution = "false"
  }

  tags = {
    Environment = "dev"
    ManagedBy   = "DevOps"
    Name        = "Peering_vpc-05f1b20a6634c6a38_to_vpc-05e0e8104b3321c40"
  }

  tags_all = {
    Environment = "dev"
    ManagedBy   = "DevOps"
    Name        = "Peering_vpc-05f1b20a6634c6a38_to_vpc-05e0e8104b3321c40"
  }

  vpc_id = "vpc-05f1b20a6634c6a38"
}

resource "aws_vpc_peering_connection" "tfer--pcx-02dd2ea276cb24bb3" {
  accepter {
    allow_remote_vpc_dns_resolution = "false"
  }

  peer_owner_id = "046276255144"
  peer_region   = "ap-southeast-1"
  peer_vpc_id   = "vpc-0ca0480cce82946b3"

  requester {
    allow_remote_vpc_dns_resolution = "false"
  }

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-internet-to-patching-peering"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-internet-to-patching-peering"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_id = "vpc-037f619602fa293a1"
}

resource "aws_vpc_peering_connection" "tfer--pcx-0f0db258d11626f4c" {
  accepter {
    allow_remote_vpc_dns_resolution = "false"
  }

  peer_owner_id = "046276255144"
  peer_region   = "ap-southeast-1"
  peer_vpc_id   = "vpc-05f1b20a6634c6a38"

  requester {
    allow_remote_vpc_dns_resolution = "false"
  }

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-internet-to-management-peering"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-internet-to-management-peering"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_id = "vpc-037f619602fa293a1"
}
