# Generated by Terraformer for VPC vpc-05f1b20a6634c6a38
# Source: /Users/<USER>/Documents/Projects/infovault-infrastructure/terraform/modules/networking/Intranet-management-compartment/raw_terraformer/aws/subnet/subnet.tf
# Generated on: 2025-06-09 13:16:58

resource "aws_subnet" "tfer--subnet-015e4b2acb87091d9" {
  assign_ipv6_address_on_creation                = "false"
  cidr_block                                     = "**********/24"
  enable_dns64                                   = "false"
  enable_lni_at_device_index                     = "0"
  enable_resource_name_dns_a_record_on_launch    = "false"
  enable_resource_name_dns_aaaa_record_on_launch = "false"
  ipv6_native                                    = "false"
  map_customer_owned_ip_on_launch                = "false"
  map_public_ip_on_launch                        = "false"
  private_dns_hostname_type_on_launch            = "ip-name"

  tags = {
    AutoShutdown = "true"
    Environment  = "dev"
    Name         = "app-subnet-az2"
    Owner        = "Temus"
    Project      = "InfoVault"
    Purpose      = "EKS-Nodes"
  }

  tags_all = {
    AutoShutdown = "true"
    Environment  = "dev"
    Name         = "app-subnet-az2"
    Owner        = "Temus"
    Project      = "InfoVault"
    Purpose      = "EKS-Nodes"
  }

  vpc_id = "vpc-05e0e8104b3321c40"
}

resource "aws_subnet" "tfer--subnet-01e871c49dc4b9b1f" {
  assign_ipv6_address_on_creation                = "false"
  cidr_block                                     = "**********/28"
  enable_dns64                                   = "false"
  enable_lni_at_device_index                     = "0"
  enable_resource_name_dns_a_record_on_launch    = "false"
  enable_resource_name_dns_aaaa_record_on_launch = "false"
  ipv6_native                                    = "false"
  map_customer_owned_ip_on_launch                = "false"
  map_public_ip_on_launch                        = "true"
  private_dns_hostname_type_on_launch            = "ip-name"

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "dev-net-pub-az1"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
    Tier            = "Public"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "dev-net-pub-az1"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
    Tier            = "Public"
  }

  vpc_id = "vpc-037f619602fa293a1"
}

resource "aws_subnet" "tfer--subnet-023cafbedbb31d13e" {
  assign_ipv6_address_on_creation                = "false"
  cidr_block                                     = "**********/24"
  enable_dns64                                   = "false"
  enable_lni_at_device_index                     = "0"
  enable_resource_name_dns_a_record_on_launch    = "false"
  enable_resource_name_dns_aaaa_record_on_launch = "false"
  ipv6_native                                    = "false"
  map_customer_owned_ip_on_launch                = "false"
  map_public_ip_on_launch                        = "false"
  private_dns_hostname_type_on_launch            = "ip-name"

  tags = {
    AutoShutdown = "true"
    Environment  = "dev"
    Name         = "app-subnet-az1"
    Owner        = "Temus"
    Project      = "InfoVault"
    Purpose      = "EKS-Nodes"
  }

  tags_all = {
    AutoShutdown = "true"
    Environment  = "dev"
    Name         = "app-subnet-az1"
    Owner        = "Temus"
    Project      = "InfoVault"
    Purpose      = "EKS-Nodes"
  }

  vpc_id = "vpc-05e0e8104b3321c40"
}

resource "aws_subnet" "tfer--subnet-043dd23a18b80a280" {
  assign_ipv6_address_on_creation                = "false"
  cidr_block                                     = "*********/28"
  enable_dns64                                   = "false"
  enable_lni_at_device_index                     = "0"
  enable_resource_name_dns_a_record_on_launch    = "false"
  enable_resource_name_dns_aaaa_record_on_launch = "false"
  ipv6_native                                    = "false"
  map_customer_owned_ip_on_launch                = "false"
  map_public_ip_on_launch                        = "false"
  private_dns_hostname_type_on_launch            = "ip-name"

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "gen-egress-az1"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
    Type            = "Egress"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "gen-egress-az1"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
    Type            = "Egress"
  }

  vpc_id = "vpc-05e0e8104b3321c40"
}

resource "aws_subnet" "tfer--subnet-04910ced717a5584a" {
  assign_ipv6_address_on_creation                = "false"
  cidr_block                                     = "***********/28"
  enable_dns64                                   = "false"
  enable_lni_at_device_index                     = "0"
  enable_resource_name_dns_a_record_on_launch    = "false"
  enable_resource_name_dns_aaaa_record_on_launch = "false"
  ipv6_native                                    = "false"
  map_customer_owned_ip_on_launch                = "false"
  map_public_ip_on_launch                        = "false"
  private_dns_hostname_type_on_launch            = "ip-name"

  tags = {
    AutoShutdown    = "false"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-ms-ad-subnet-b"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
    Purpose         = "Microsoft AD"
    Tier            = "PrivateDB"
    Type            = "ms-ad"
  }

  tags_all = {
    AutoShutdown    = "false"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-ms-ad-subnet-b"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
    Purpose         = "Microsoft AD"
    Tier            = "PrivateDB"
    Type            = "ms-ad"
  }

  vpc_id = data.terraform_remote_state.vpc.outputs.aws_vpc_tfer--vpc-05f1b20a6634c6a38_id
}

resource "aws_subnet" "tfer--subnet-056cc33d6674e5027" {
  assign_ipv6_address_on_creation                = "false"
  cidr_block                                     = "***********/20"
  enable_dns64                                   = "false"
  enable_lni_at_device_index                     = "0"
  enable_resource_name_dns_a_record_on_launch    = "false"
  enable_resource_name_dns_aaaa_record_on_launch = "false"
  ipv6_native                                    = "false"
  map_customer_owned_ip_on_launch                = "false"
  map_public_ip_on_launch                        = "false"
  private_dns_hostname_type_on_launch            = "ip-name"

  tags = {
    Name    = "aws-controltower-PrivateSubnet2A"
    Network = "Private"
  }

  tags_all = {
    Name    = "aws-controltower-PrivateSubnet2A"
    Network = "Private"
  }

  vpc_id = "vpc-02282d8d4c9c7764c"
}

resource "aws_subnet" "tfer--subnet-0669d84e56a7dc53e" {
  assign_ipv6_address_on_creation                = "false"
  cidr_block                                     = "***********/20"
  enable_dns64                                   = "false"
  enable_lni_at_device_index                     = "0"
  enable_resource_name_dns_a_record_on_launch    = "false"
  enable_resource_name_dns_aaaa_record_on_launch = "false"
  ipv6_native                                    = "false"
  map_customer_owned_ip_on_launch                = "false"
  map_public_ip_on_launch                        = "false"
  private_dns_hostname_type_on_launch            = "ip-name"

  tags = {
    Name    = "aws-controltower-PrivateSubnet1A"
    Network = "Private"
  }

  tags_all = {
    Name    = "aws-controltower-PrivateSubnet1A"
    Network = "Private"
  }

  vpc_id = "vpc-02282d8d4c9c7764c"
}

resource "aws_subnet" "tfer--subnet-07ab4b059d69e4044" {
  assign_ipv6_address_on_creation                = "false"
  cidr_block                                     = "**********/28"
  enable_dns64                                   = "false"
  enable_lni_at_device_index                     = "0"
  enable_resource_name_dns_a_record_on_launch    = "false"
  enable_resource_name_dns_aaaa_record_on_launch = "false"
  ipv6_native                                    = "false"
  map_customer_owned_ip_on_launch                = "false"
  map_public_ip_on_launch                        = "false"
  private_dns_hostname_type_on_launch            = "ip-name"

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "Management subnet (AZ1)"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
    Tier            = "PrivateApp"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "Management subnet (AZ1)"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
    Tier            = "PrivateApp"
  }

  vpc_id = data.terraform_remote_state.vpc.outputs.aws_vpc_tfer--vpc-05f1b20a6634c6a38_id
}

resource "aws_subnet" "tfer--subnet-080d8fdb4e9add74c" {
  assign_ipv6_address_on_creation                = "false"
  cidr_block                                     = "**********/28"
  enable_dns64                                   = "false"
  enable_lni_at_device_index                     = "0"
  enable_resource_name_dns_a_record_on_launch    = "false"
  enable_resource_name_dns_aaaa_record_on_launch = "false"
  ipv6_native                                    = "false"
  map_customer_owned_ip_on_launch                = "false"
  map_public_ip_on_launch                        = "false"
  private_dns_hostname_type_on_launch            = "ip-name"

  tags = {
    AutoShutdown = "true"
    Environment  = "dev"
    Name         = "smtp-subnet-dlz1"
    Owner        = "Temus"
    Project      = "InfoVault"
  }

  tags_all = {
    AutoShutdown = "true"
    Environment  = "dev"
    Name         = "smtp-subnet-dlz1"
    Owner        = "Temus"
    Project      = "InfoVault"
  }

  vpc_id = "vpc-05e0e8104b3321c40"
}

resource "aws_subnet" "tfer--subnet-081b6eb841b4451b3" {
  assign_ipv6_address_on_creation                = "false"
  cidr_block                                     = "***********/28"
  enable_dns64                                   = "false"
  enable_lni_at_device_index                     = "0"
  enable_resource_name_dns_a_record_on_launch    = "false"
  enable_resource_name_dns_aaaa_record_on_launch = "false"
  ipv6_native                                    = "false"
  map_customer_owned_ip_on_launch                = "false"
  map_public_ip_on_launch                        = "true"
  private_dns_hostname_type_on_launch            = "ip-name"

  tags = {
    AutoShutdown = "true"
    Environment  = "dev"
    Name         = "gen-ingress-az2"
    Project      = "infovault"
    Type         = "Public"
  }

  tags_all = {
    AutoShutdown = "true"
    Environment  = "dev"
    Name         = "gen-ingress-az2"
    Project      = "infovault"
    Type         = "Public"
  }

  vpc_id = "vpc-05e0e8104b3321c40"
}

resource "aws_subnet" "tfer--subnet-0a1802eb99e96cbed" {
  assign_ipv6_address_on_creation                = "false"
  cidr_block                                     = "**********/28"
  enable_dns64                                   = "false"
  enable_lni_at_device_index                     = "0"
  enable_resource_name_dns_a_record_on_launch    = "false"
  enable_resource_name_dns_aaaa_record_on_launch = "false"
  ipv6_native                                    = "false"
  map_customer_owned_ip_on_launch                = "false"
  map_public_ip_on_launch                        = "false"
  private_dns_hostname_type_on_launch            = "ip-name"

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "gen-migration-az1"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
    Type            = "Migration"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "gen-migration-az1"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
    Type            = "Migration"
  }

  vpc_id = "vpc-05e0e8104b3321c40"
}

resource "aws_subnet" "tfer--subnet-0a5d4aa5489b9ab3a" {
  assign_ipv6_address_on_creation                = "false"
  cidr_block                                     = "**********/28"
  enable_dns64                                   = "false"
  enable_lni_at_device_index                     = "0"
  enable_resource_name_dns_a_record_on_launch    = "false"
  enable_resource_name_dns_aaaa_record_on_launch = "false"
  ipv6_native                                    = "false"
  map_customer_owned_ip_on_launch                = "false"
  map_public_ip_on_launch                        = "false"
  private_dns_hostname_type_on_launch            = "ip-name"

  tags = {
    AutoShutdown = "true"
    Environment  = "dev"
    Name         = "smtp-subnet-dlz1"
    Owner        = "Temus"
    Project      = "InfoVault"
  }

  tags_all = {
    AutoShutdown = "true"
    Environment  = "dev"
    Name         = "smtp-subnet-dlz1"
    Owner        = "Temus"
    Project      = "InfoVault"
  }

  vpc_id = "vpc-05e0e8104b3321c40"
}

resource "aws_subnet" "tfer--subnet-0aec855b39a7be02e" {
  assign_ipv6_address_on_creation                = "false"
  cidr_block                                     = "***********/28"
  enable_dns64                                   = "false"
  enable_lni_at_device_index                     = "0"
  enable_resource_name_dns_a_record_on_launch    = "false"
  enable_resource_name_dns_aaaa_record_on_launch = "false"
  ipv6_native                                    = "false"
  map_customer_owned_ip_on_launch                = "false"
  map_public_ip_on_launch                        = "false"
  private_dns_hostname_type_on_launch            = "ip-name"

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "dev-net-fw-az1"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
    Tier            = "PrivateApp"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "dev-net-fw-az1"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
    Tier            = "PrivateApp"
  }

  vpc_id = "vpc-037f619602fa293a1"
}

resource "aws_subnet" "tfer--subnet-0b9afa4ae103de5b5" {
  assign_ipv6_address_on_creation                = "false"
  cidr_block                                     = "***********/20"
  enable_dns64                                   = "false"
  enable_lni_at_device_index                     = "0"
  enable_resource_name_dns_a_record_on_launch    = "false"
  enable_resource_name_dns_aaaa_record_on_launch = "false"
  ipv6_native                                    = "false"
  map_customer_owned_ip_on_launch                = "false"
  map_public_ip_on_launch                        = "false"
  private_dns_hostname_type_on_launch            = "ip-name"

  tags = {
    Name    = "aws-controltower-PrivateSubnet3A"
    Network = "Private"
  }

  tags_all = {
    Name    = "aws-controltower-PrivateSubnet3A"
    Network = "Private"
  }

  vpc_id = "vpc-02282d8d4c9c7764c"
}

resource "aws_subnet" "tfer--subnet-0c6066202240c38be" {
  assign_ipv6_address_on_creation                = "false"
  cidr_block                                     = "*********/28"
  enable_dns64                                   = "false"
  enable_lni_at_device_index                     = "0"
  enable_resource_name_dns_a_record_on_launch    = "false"
  enable_resource_name_dns_aaaa_record_on_launch = "false"
  ipv6_native                                    = "false"
  map_customer_owned_ip_on_launch                = "false"
  map_public_ip_on_launch                        = "false"
  private_dns_hostname_type_on_launch            = "ip-name"

  tags = {
    AutoShutdown    = "true"
    Compartment     = "ABLRHF"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-ablrhf-subnet-az1"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
    Tier            = "Private"
  }

  tags_all = {
    AutoShutdown    = "true"
    Compartment     = "ABLRHF"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-ablrhf-subnet-az1"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
    Tier            = "Private"
  }

  vpc_id = "vpc-0e75036a2ad9e5b4f"
}

resource "aws_subnet" "tfer--subnet-0c775a4f57dfa0e61" {
  assign_ipv6_address_on_creation                = "false"
  cidr_block                                     = "***********/26"
  enable_dns64                                   = "false"
  enable_lni_at_device_index                     = "0"
  enable_resource_name_dns_a_record_on_launch    = "false"
  enable_resource_name_dns_aaaa_record_on_launch = "false"
  ipv6_native                                    = "false"
  map_customer_owned_ip_on_launch                = "false"
  map_public_ip_on_launch                        = "false"
  private_dns_hostname_type_on_launch            = "ip-name"

  tags = {
    AutoShutdown = "true"
    Environment  = "dev"
    Name         = "db-subnet-az2"
    Owner        = "Temus"
    Project      = "InfoVault"
    Purpose      = "Database"
  }

  tags_all = {
    AutoShutdown = "true"
    Environment  = "dev"
    Name         = "db-subnet-az2"
    Owner        = "Temus"
    Project      = "InfoVault"
    Purpose      = "Database"
  }

  vpc_id = "vpc-05e0e8104b3321c40"
}

resource "aws_subnet" "tfer--subnet-0d4f42cf30b20c42d" {
  assign_ipv6_address_on_creation                = "false"
  cidr_block                                     = "**********/26"
  enable_dns64                                   = "false"
  enable_lni_at_device_index                     = "0"
  enable_resource_name_dns_a_record_on_launch    = "false"
  enable_resource_name_dns_aaaa_record_on_launch = "false"
  ipv6_native                                    = "false"
  map_customer_owned_ip_on_launch                = "false"
  map_public_ip_on_launch                        = "false"
  private_dns_hostname_type_on_launch            = "ip-name"

  tags = {
    AutoShutdown = "true"
    Environment  = "dev"
    Name         = "db-subnet-az1"
    Owner        = "Temus"
    Project      = "InfoVault"
    Purpose      = "Database"
  }

  tags_all = {
    AutoShutdown = "true"
    Environment  = "dev"
    Name         = "db-subnet-az1"
    Owner        = "Temus"
    Project      = "InfoVault"
    Purpose      = "Database"
  }

  vpc_id = "vpc-05e0e8104b3321c40"
}

resource "aws_subnet" "tfer--subnet-0dacd0ef604204014" {
  assign_ipv6_address_on_creation                = "false"
  cidr_block                                     = "**********/28"
  enable_dns64                                   = "false"
  enable_lni_at_device_index                     = "0"
  enable_resource_name_dns_a_record_on_launch    = "false"
  enable_resource_name_dns_aaaa_record_on_launch = "false"
  ipv6_native                                    = "false"
  map_customer_owned_ip_on_launch                = "false"
  map_public_ip_on_launch                        = "false"
  private_dns_hostname_type_on_launch            = "ip-name"

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-patching-subnet-az1"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-patching-subnet-az1"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_id = "vpc-0ca0480cce82946b3"
}

resource "aws_subnet" "tfer--subnet-0e2a201f2503f59ee" {
  assign_ipv6_address_on_creation                = "false"
  cidr_block                                     = "**********/28"
  enable_dns64                                   = "false"
  enable_lni_at_device_index                     = "0"
  enable_resource_name_dns_a_record_on_launch    = "false"
  enable_resource_name_dns_aaaa_record_on_launch = "false"
  ipv6_native                                    = "false"
  map_customer_owned_ip_on_launch                = "false"
  map_public_ip_on_launch                        = "true"
  private_dns_hostname_type_on_launch            = "ip-name"

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "gen-ingress-az1"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
    Type            = "Ingress"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "gen-ingress-az1"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
    Type            = "Ingress"
  }

  vpc_id = "vpc-05e0e8104b3321c40"
}

resource "aws_subnet" "tfer--subnet-0ebbc016c2fc6b3db" {
  assign_ipv6_address_on_creation                = "false"
  cidr_block                                     = "***********/28"
  enable_dns64                                   = "false"
  enable_lni_at_device_index                     = "0"
  enable_resource_name_dns_a_record_on_launch    = "false"
  enable_resource_name_dns_aaaa_record_on_launch = "false"
  ipv6_native                                    = "false"
  map_customer_owned_ip_on_launch                = "false"
  map_public_ip_on_launch                        = "false"
  private_dns_hostname_type_on_launch            = "ip-name"

  tags = {
    AutoShutdown = "false"
    Environment  = "dev"
    Name         = "infovault-dev-ms-ad-subnet-a"
    Project      = "infovault"
    Type         = "ms-ad"
  }

  tags_all = {
    AutoShutdown = "false"
    Environment  = "dev"
    Name         = "infovault-dev-ms-ad-subnet-a"
    Project      = "infovault"
    Type         = "ms-ad"
  }

  vpc_id = data.terraform_remote_state.vpc.outputs.aws_vpc_tfer--vpc-05f1b20a6634c6a38_id
}

resource "aws_subnet" "tfer--subnet-0f80c3ddc6536fd2a" {
  assign_ipv6_address_on_creation                = "false"
  cidr_block                                     = "***********/28"
  enable_dns64                                   = "false"
  enable_lni_at_device_index                     = "0"
  enable_resource_name_dns_a_record_on_launch    = "false"
  enable_resource_name_dns_aaaa_record_on_launch = "false"
  ipv6_native                                    = "false"
  map_customer_owned_ip_on_launch                = "false"
  map_public_ip_on_launch                        = "false"
  private_dns_hostname_type_on_launch            = "ip-name"

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-private-db-subnet-a"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
    Tier            = "PrivateDB"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-private-db-subnet-a"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
    Tier            = "PrivateDB"
  }

  vpc_id = data.terraform_remote_state.vpc.outputs.aws_vpc_tfer--vpc-05f1b20a6634c6a38_id
}
