resource "aws_vpc" "tfer--vpc-0e75036a2ad9e5b4f" {
  assign_generated_ipv6_cidr_block     = "false"
  cidr_block                           = "*********/27"
  enable_dns_hostnames                 = "true"
  enable_dns_support                   = "true"
  enable_network_address_usage_metrics = "false"
  instance_tenancy                     = "default"
  ipv6_netmask_length                  = "0"

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-ablrhf-compartment"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-ablrhf-compartment"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }
}
