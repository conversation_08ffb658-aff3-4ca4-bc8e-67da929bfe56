{"version": 3, "terraform_version": "0.12.31", "serial": 1, "lineage": "ebca3d63-35a4-16f5-70f8-536a66fcff81", "modules": [{"path": ["root"], "outputs": {"aws_vpc_tfer--vpc-0e75036a2ad9e5b4f_id": {"sensitive": false, "type": "string", "value": "vpc-0e75036a2ad9e5b4f"}}, "resources": {"aws_vpc.tfer--vpc-0e75036a2ad9e5b4f": {"type": "aws_vpc", "depends_on": [], "primary": {"id": "vpc-0e75036a2ad9e5b4f", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:vpc/vpc-0e75036a2ad9e5b4f", "assign_generated_ipv6_cidr_block": "false", "cidr_block": "*********/27", "default_network_acl_id": "acl-0228326a161d09c04", "default_route_table_id": "rtb-0cb7cabf4d81ee7bd", "default_security_group_id": "sg-010c8f879a2498c96", "dhcp_options_id": "dopt-0651689ceffd5fc0c", "enable_dns_hostnames": "true", "enable_dns_support": "true", "enable_network_address_usage_metrics": "false", "id": "vpc-0e75036a2ad9e5b4f", "instance_tenancy": "default", "ipv6_association_id": "", "ipv6_cidr_block": "", "ipv6_cidr_block_network_border_group": "", "ipv6_ipam_pool_id": "", "ipv6_netmask_length": "0", "main_route_table_id": "rtb-0cb7cabf4d81ee7bd", "owner_id": "046276255144", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "infovault-dev-ablrhf-compartment", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "infovault-dev-ablrhf-compartment", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}}, "depends_on": []}]}