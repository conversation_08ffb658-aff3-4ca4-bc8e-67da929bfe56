{"version": 3, "terraform_version": "0.12.31", "serial": 1, "lineage": "94f208b3-5068-e63e-8c6e-ba94435f59cc", "modules": [{"path": ["root"], "outputs": {"aws_subnet_tfer--subnet-015e4b2acb87091d9_id": {"sensitive": false, "type": "string", "value": "subnet-015e4b2acb87091d9"}, "aws_subnet_tfer--subnet-01e871c49dc4b9b1f_id": {"sensitive": false, "type": "string", "value": "subnet-01e871c49dc4b9b1f"}, "aws_subnet_tfer--subnet-023cafbedbb31d13e_id": {"sensitive": false, "type": "string", "value": "subnet-023cafbedbb31d13e"}, "aws_subnet_tfer--subnet-043dd23a18b80a280_id": {"sensitive": false, "type": "string", "value": "subnet-043dd23a18b80a280"}, "aws_subnet_tfer--subnet-04910ced717a5584a_id": {"sensitive": false, "type": "string", "value": "subnet-04910ced717a5584a"}, "aws_subnet_tfer--subnet-056cc33d6674e5027_id": {"sensitive": false, "type": "string", "value": "subnet-056cc33d6674e5027"}, "aws_subnet_tfer--subnet-0669d84e56a7dc53e_id": {"sensitive": false, "type": "string", "value": "subnet-0669d84e56a7dc53e"}, "aws_subnet_tfer--subnet-07ab4b059d69e4044_id": {"sensitive": false, "type": "string", "value": "subnet-07ab4b059d69e4044"}, "aws_subnet_tfer--subnet-080d8fdb4e9add74c_id": {"sensitive": false, "type": "string", "value": "subnet-080d8fdb4e9add74c"}, "aws_subnet_tfer--subnet-081b6eb841b4451b3_id": {"sensitive": false, "type": "string", "value": "subnet-081b6eb841b4451b3"}, "aws_subnet_tfer--subnet-0a1802eb99e96cbed_id": {"sensitive": false, "type": "string", "value": "subnet-0a1802eb99e96cbed"}, "aws_subnet_tfer--subnet-0a5d4aa5489b9ab3a_id": {"sensitive": false, "type": "string", "value": "subnet-0a5d4aa5489b9ab3a"}, "aws_subnet_tfer--subnet-0aec855b39a7be02e_id": {"sensitive": false, "type": "string", "value": "subnet-0aec855b39a7be02e"}, "aws_subnet_tfer--subnet-0b9afa4ae103de5b5_id": {"sensitive": false, "type": "string", "value": "subnet-0b9afa4ae103de5b5"}, "aws_subnet_tfer--subnet-0c6066202240c38be_id": {"sensitive": false, "type": "string", "value": "subnet-0c6066202240c38be"}, "aws_subnet_tfer--subnet-0c775a4f57dfa0e61_id": {"sensitive": false, "type": "string", "value": "subnet-0c775a4f57dfa0e61"}, "aws_subnet_tfer--subnet-0d4f42cf30b20c42d_id": {"sensitive": false, "type": "string", "value": "subnet-0d4f42cf30b20c42d"}, "aws_subnet_tfer--subnet-0dacd0ef604204014_id": {"sensitive": false, "type": "string", "value": "subnet-0dacd0ef604204014"}, "aws_subnet_tfer--subnet-0e2a201f2503f59ee_id": {"sensitive": false, "type": "string", "value": "subnet-0e2a201f2503f59ee"}, "aws_subnet_tfer--subnet-0ebbc016c2fc6b3db_id": {"sensitive": false, "type": "string", "value": "subnet-0ebbc016c2fc6b3db"}, "aws_subnet_tfer--subnet-0f80c3ddc6536fd2a_id": {"sensitive": false, "type": "string", "value": "subnet-0f80c3ddc6536fd2a"}}, "resources": {"aws_subnet.tfer--subnet-015e4b2acb87091d9": {"type": "aws_subnet", "depends_on": [], "primary": {"id": "subnet-015e4b2acb87091d9", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:subnet/subnet-015e4b2acb87091d9", "assign_ipv6_address_on_creation": "false", "availability_zone": "ap-southeast-1b", "availability_zone_id": "apse1-az2", "cidr_block": "**********/24", "customer_owned_ipv4_pool": "", "enable_dns64": "false", "enable_lni_at_device_index": "0", "enable_resource_name_dns_a_record_on_launch": "false", "enable_resource_name_dns_aaaa_record_on_launch": "false", "id": "subnet-015e4b2acb87091d9", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": "false", "map_customer_owned_ip_on_launch": "false", "map_public_ip_on_launch": "false", "outpost_arn": "", "owner_id": "046276255144", "private_dns_hostname_type_on_launch": "ip-name", "tags.%": "6", "tags.AutoShutdown": "true", "tags.Environment": "dev", "tags.Name": "app-subnet-az2", "tags.Owner": "<PERSON><PERSON>", "tags.Project": "InfoVault", "tags.Purpose": "EKS-Nodes", "tags_all.%": "6", "tags_all.AutoShutdown": "true", "tags_all.Environment": "dev", "tags_all.Name": "app-subnet-az2", "tags_all.Owner": "<PERSON><PERSON>", "tags_all.Project": "InfoVault", "tags_all.Purpose": "EKS-Nodes", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_subnet.tfer--subnet-01e871c49dc4b9b1f": {"type": "aws_subnet", "depends_on": [], "primary": {"id": "subnet-01e871c49dc4b9b1f", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:subnet/subnet-01e871c49dc4b9b1f", "assign_ipv6_address_on_creation": "false", "availability_zone": "ap-southeast-1a", "availability_zone_id": "apse1-az1", "cidr_block": "**********/28", "customer_owned_ipv4_pool": "", "enable_dns64": "false", "enable_lni_at_device_index": "0", "enable_resource_name_dns_a_record_on_launch": "false", "enable_resource_name_dns_aaaa_record_on_launch": "false", "id": "subnet-01e871c49dc4b9b1f", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": "false", "map_customer_owned_ip_on_launch": "false", "map_public_ip_on_launch": "true", "outpost_arn": "", "owner_id": "046276255144", "private_dns_hostname_type_on_launch": "ip-name", "tags.%": "9", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "dev-net-pub-az1", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags.Tier": "Public", "tags_all.%": "9", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "dev-net-pub-az1", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "tags_all.Tier": "Public", "vpc_id": "vpc-037f619602fa293a1"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_subnet.tfer--subnet-023cafbedbb31d13e": {"type": "aws_subnet", "depends_on": [], "primary": {"id": "subnet-023cafbedbb31d13e", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:subnet/subnet-023cafbedbb31d13e", "assign_ipv6_address_on_creation": "false", "availability_zone": "ap-southeast-1a", "availability_zone_id": "apse1-az1", "cidr_block": "**********/24", "customer_owned_ipv4_pool": "", "enable_dns64": "false", "enable_lni_at_device_index": "0", "enable_resource_name_dns_a_record_on_launch": "false", "enable_resource_name_dns_aaaa_record_on_launch": "false", "id": "subnet-023cafbedbb31d13e", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": "false", "map_customer_owned_ip_on_launch": "false", "map_public_ip_on_launch": "false", "outpost_arn": "", "owner_id": "046276255144", "private_dns_hostname_type_on_launch": "ip-name", "tags.%": "6", "tags.AutoShutdown": "true", "tags.Environment": "dev", "tags.Name": "app-subnet-az1", "tags.Owner": "<PERSON><PERSON>", "tags.Project": "InfoVault", "tags.Purpose": "EKS-Nodes", "tags_all.%": "6", "tags_all.AutoShutdown": "true", "tags_all.Environment": "dev", "tags_all.Name": "app-subnet-az1", "tags_all.Owner": "<PERSON><PERSON>", "tags_all.Project": "InfoVault", "tags_all.Purpose": "EKS-Nodes", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_subnet.tfer--subnet-043dd23a18b80a280": {"type": "aws_subnet", "depends_on": [], "primary": {"id": "subnet-043dd23a18b80a280", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:subnet/subnet-043dd23a18b80a280", "assign_ipv6_address_on_creation": "false", "availability_zone": "ap-southeast-1a", "availability_zone_id": "apse1-az1", "cidr_block": "*********/28", "customer_owned_ipv4_pool": "", "enable_dns64": "false", "enable_lni_at_device_index": "0", "enable_resource_name_dns_a_record_on_launch": "false", "enable_resource_name_dns_aaaa_record_on_launch": "false", "id": "subnet-043dd23a18b80a280", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": "false", "map_customer_owned_ip_on_launch": "false", "map_public_ip_on_launch": "false", "outpost_arn": "", "owner_id": "046276255144", "private_dns_hostname_type_on_launch": "ip-name", "tags.%": "9", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "gen-egress-az1", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags.Type": "Egress", "tags_all.%": "9", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "gen-egress-az1", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "tags_all.Type": "Egress", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_subnet.tfer--subnet-04910ced717a5584a": {"type": "aws_subnet", "depends_on": [], "primary": {"id": "subnet-04910ced717a5584a", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:subnet/subnet-04910ced717a5584a", "assign_ipv6_address_on_creation": "false", "availability_zone": "ap-southeast-1b", "availability_zone_id": "apse1-az2", "cidr_block": "***********/28", "customer_owned_ipv4_pool": "", "enable_dns64": "false", "enable_lni_at_device_index": "0", "enable_resource_name_dns_a_record_on_launch": "false", "enable_resource_name_dns_aaaa_record_on_launch": "false", "id": "subnet-04910ced717a5584a", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": "false", "map_customer_owned_ip_on_launch": "false", "map_public_ip_on_launch": "false", "outpost_arn": "", "owner_id": "046276255144", "private_dns_hostname_type_on_launch": "ip-name", "tags.%": "11", "tags.AutoShutdown": "false", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "infovault-dev-ms-ad-subnet-b", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags.Purpose": "Microsoft AD", "tags.Tier": "PrivateDB", "tags.Type": "ms-ad", "tags_all.%": "11", "tags_all.AutoShutdown": "false", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "infovault-dev-ms-ad-subnet-b", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "tags_all.Purpose": "Microsoft AD", "tags_all.Tier": "PrivateDB", "tags_all.Type": "ms-ad", "vpc_id": "vpc-05f1b20a6634c6a38"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_subnet.tfer--subnet-056cc33d6674e5027": {"type": "aws_subnet", "depends_on": [], "primary": {"id": "subnet-056cc33d6674e5027", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:subnet/subnet-056cc33d6674e5027", "assign_ipv6_address_on_creation": "false", "availability_zone": "ap-southeast-1b", "availability_zone_id": "apse1-az2", "cidr_block": "***********/20", "customer_owned_ipv4_pool": "", "enable_dns64": "false", "enable_lni_at_device_index": "0", "enable_resource_name_dns_a_record_on_launch": "false", "enable_resource_name_dns_aaaa_record_on_launch": "false", "id": "subnet-056cc33d6674e5027", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": "false", "map_customer_owned_ip_on_launch": "false", "map_public_ip_on_launch": "false", "outpost_arn": "", "owner_id": "046276255144", "private_dns_hostname_type_on_launch": "ip-name", "tags.%": "2", "tags.Name": "aws-controltower-PrivateSubnet2A", "tags.Network": "Private", "tags_all.%": "2", "tags_all.Name": "aws-controltower-PrivateSubnet2A", "tags_all.Network": "Private", "vpc_id": "vpc-02282d8d4c9c7764c"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_subnet.tfer--subnet-0669d84e56a7dc53e": {"type": "aws_subnet", "depends_on": [], "primary": {"id": "subnet-0669d84e56a7dc53e", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:subnet/subnet-0669d84e56a7dc53e", "assign_ipv6_address_on_creation": "false", "availability_zone": "ap-southeast-1a", "availability_zone_id": "apse1-az1", "cidr_block": "***********/20", "customer_owned_ipv4_pool": "", "enable_dns64": "false", "enable_lni_at_device_index": "0", "enable_resource_name_dns_a_record_on_launch": "false", "enable_resource_name_dns_aaaa_record_on_launch": "false", "id": "subnet-0669d84e56a7dc53e", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": "false", "map_customer_owned_ip_on_launch": "false", "map_public_ip_on_launch": "false", "outpost_arn": "", "owner_id": "046276255144", "private_dns_hostname_type_on_launch": "ip-name", "tags.%": "2", "tags.Name": "aws-controltower-PrivateSubnet1A", "tags.Network": "Private", "tags_all.%": "2", "tags_all.Name": "aws-controltower-PrivateSubnet1A", "tags_all.Network": "Private", "vpc_id": "vpc-02282d8d4c9c7764c"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_subnet.tfer--subnet-07ab4b059d69e4044": {"type": "aws_subnet", "depends_on": [], "primary": {"id": "subnet-07ab4b059d69e4044", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:subnet/subnet-07ab4b059d69e4044", "assign_ipv6_address_on_creation": "false", "availability_zone": "ap-southeast-1a", "availability_zone_id": "apse1-az1", "cidr_block": "**********/28", "customer_owned_ipv4_pool": "", "enable_dns64": "false", "enable_lni_at_device_index": "0", "enable_resource_name_dns_a_record_on_launch": "false", "enable_resource_name_dns_aaaa_record_on_launch": "false", "id": "subnet-07ab4b059d69e4044", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": "false", "map_customer_owned_ip_on_launch": "false", "map_public_ip_on_launch": "false", "outpost_arn": "", "owner_id": "046276255144", "private_dns_hostname_type_on_launch": "ip-name", "tags.%": "9", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "Management subnet (AZ1)", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags.Tier": "PrivateApp", "tags_all.%": "9", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "Management subnet (AZ1)", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "tags_all.Tier": "PrivateApp", "vpc_id": "vpc-05f1b20a6634c6a38"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_subnet.tfer--subnet-080d8fdb4e9add74c": {"type": "aws_subnet", "depends_on": [], "primary": {"id": "subnet-080d8fdb4e9add74c", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:subnet/subnet-080d8fdb4e9add74c", "assign_ipv6_address_on_creation": "false", "availability_zone": "ap-southeast-1a", "availability_zone_id": "apse1-az1", "cidr_block": "**********/28", "customer_owned_ipv4_pool": "", "enable_dns64": "false", "enable_lni_at_device_index": "0", "enable_resource_name_dns_a_record_on_launch": "false", "enable_resource_name_dns_aaaa_record_on_launch": "false", "id": "subnet-080d8fdb4e9add74c", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": "false", "map_customer_owned_ip_on_launch": "false", "map_public_ip_on_launch": "false", "outpost_arn": "", "owner_id": "046276255144", "private_dns_hostname_type_on_launch": "ip-name", "tags.%": "5", "tags.AutoShutdown": "true", "tags.Environment": "dev", "tags.Name": "smtp-subnet-dlz1", "tags.Owner": "<PERSON><PERSON>", "tags.Project": "InfoVault", "tags_all.%": "5", "tags_all.AutoShutdown": "true", "tags_all.Environment": "dev", "tags_all.Name": "smtp-subnet-dlz1", "tags_all.Owner": "<PERSON><PERSON>", "tags_all.Project": "InfoVault", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_subnet.tfer--subnet-081b6eb841b4451b3": {"type": "aws_subnet", "depends_on": [], "primary": {"id": "subnet-081b6eb841b4451b3", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:subnet/subnet-081b6eb841b4451b3", "assign_ipv6_address_on_creation": "false", "availability_zone": "ap-southeast-1b", "availability_zone_id": "apse1-az2", "cidr_block": "***********/28", "customer_owned_ipv4_pool": "", "enable_dns64": "false", "enable_lni_at_device_index": "0", "enable_resource_name_dns_a_record_on_launch": "false", "enable_resource_name_dns_aaaa_record_on_launch": "false", "id": "subnet-081b6eb841b4451b3", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": "false", "map_customer_owned_ip_on_launch": "false", "map_public_ip_on_launch": "true", "outpost_arn": "", "owner_id": "046276255144", "private_dns_hostname_type_on_launch": "ip-name", "tags.%": "5", "tags.AutoShutdown": "true", "tags.Environment": "dev", "tags.Name": "gen-ingress-az2", "tags.Project": "infovault", "tags.Type": "Public", "tags_all.%": "5", "tags_all.AutoShutdown": "true", "tags_all.Environment": "dev", "tags_all.Name": "gen-ingress-az2", "tags_all.Project": "infovault", "tags_all.Type": "Public", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_subnet.tfer--subnet-0a1802eb99e96cbed": {"type": "aws_subnet", "depends_on": [], "primary": {"id": "subnet-0a1802eb99e96cbed", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:subnet/subnet-0a1802eb99e96cbed", "assign_ipv6_address_on_creation": "false", "availability_zone": "ap-southeast-1a", "availability_zone_id": "apse1-az1", "cidr_block": "**********/28", "customer_owned_ipv4_pool": "", "enable_dns64": "false", "enable_lni_at_device_index": "0", "enable_resource_name_dns_a_record_on_launch": "false", "enable_resource_name_dns_aaaa_record_on_launch": "false", "id": "subnet-0a1802eb99e96cbed", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": "false", "map_customer_owned_ip_on_launch": "false", "map_public_ip_on_launch": "false", "outpost_arn": "", "owner_id": "046276255144", "private_dns_hostname_type_on_launch": "ip-name", "tags.%": "9", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "gen-migration-az1", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags.Type": "Migration", "tags_all.%": "9", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "gen-migration-az1", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "tags_all.Type": "Migration", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_subnet.tfer--subnet-0a5d4aa5489b9ab3a": {"type": "aws_subnet", "depends_on": [], "primary": {"id": "subnet-0a5d4aa5489b9ab3a", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:subnet/subnet-0a5d4aa5489b9ab3a", "assign_ipv6_address_on_creation": "false", "availability_zone": "ap-southeast-1a", "availability_zone_id": "apse1-az1", "cidr_block": "**********/28", "customer_owned_ipv4_pool": "", "enable_dns64": "false", "enable_lni_at_device_index": "0", "enable_resource_name_dns_a_record_on_launch": "false", "enable_resource_name_dns_aaaa_record_on_launch": "false", "id": "subnet-0a5d4aa5489b9ab3a", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": "false", "map_customer_owned_ip_on_launch": "false", "map_public_ip_on_launch": "false", "outpost_arn": "", "owner_id": "046276255144", "private_dns_hostname_type_on_launch": "ip-name", "tags.%": "5", "tags.AutoShutdown": "true", "tags.Environment": "dev", "tags.Name": "smtp-subnet-dlz1", "tags.Owner": "<PERSON><PERSON>", "tags.Project": "InfoVault", "tags_all.%": "5", "tags_all.AutoShutdown": "true", "tags_all.Environment": "dev", "tags_all.Name": "smtp-subnet-dlz1", "tags_all.Owner": "<PERSON><PERSON>", "tags_all.Project": "InfoVault", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_subnet.tfer--subnet-0aec855b39a7be02e": {"type": "aws_subnet", "depends_on": [], "primary": {"id": "subnet-0aec855b39a7be02e", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:subnet/subnet-0aec855b39a7be02e", "assign_ipv6_address_on_creation": "false", "availability_zone": "ap-southeast-1a", "availability_zone_id": "apse1-az1", "cidr_block": "***********/28", "customer_owned_ipv4_pool": "", "enable_dns64": "false", "enable_lni_at_device_index": "0", "enable_resource_name_dns_a_record_on_launch": "false", "enable_resource_name_dns_aaaa_record_on_launch": "false", "id": "subnet-0aec855b39a7be02e", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": "false", "map_customer_owned_ip_on_launch": "false", "map_public_ip_on_launch": "false", "outpost_arn": "", "owner_id": "046276255144", "private_dns_hostname_type_on_launch": "ip-name", "tags.%": "9", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "dev-net-fw-az1", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags.Tier": "PrivateApp", "tags_all.%": "9", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "dev-net-fw-az1", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "tags_all.Tier": "PrivateApp", "vpc_id": "vpc-037f619602fa293a1"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_subnet.tfer--subnet-0b9afa4ae103de5b5": {"type": "aws_subnet", "depends_on": [], "primary": {"id": "subnet-0b9afa4ae103de5b5", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:subnet/subnet-0b9afa4ae103de5b5", "assign_ipv6_address_on_creation": "false", "availability_zone": "ap-southeast-1c", "availability_zone_id": "apse1-az3", "cidr_block": "***********/20", "customer_owned_ipv4_pool": "", "enable_dns64": "false", "enable_lni_at_device_index": "0", "enable_resource_name_dns_a_record_on_launch": "false", "enable_resource_name_dns_aaaa_record_on_launch": "false", "id": "subnet-0b9afa4ae103de5b5", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": "false", "map_customer_owned_ip_on_launch": "false", "map_public_ip_on_launch": "false", "outpost_arn": "", "owner_id": "046276255144", "private_dns_hostname_type_on_launch": "ip-name", "tags.%": "2", "tags.Name": "aws-controltower-PrivateSubnet3A", "tags.Network": "Private", "tags_all.%": "2", "tags_all.Name": "aws-controltower-PrivateSubnet3A", "tags_all.Network": "Private", "vpc_id": "vpc-02282d8d4c9c7764c"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_subnet.tfer--subnet-0c6066202240c38be": {"type": "aws_subnet", "depends_on": [], "primary": {"id": "subnet-0c6066202240c38be", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:subnet/subnet-0c6066202240c38be", "assign_ipv6_address_on_creation": "false", "availability_zone": "ap-southeast-1a", "availability_zone_id": "apse1-az1", "cidr_block": "*********/28", "customer_owned_ipv4_pool": "", "enable_dns64": "false", "enable_lni_at_device_index": "0", "enable_resource_name_dns_a_record_on_launch": "false", "enable_resource_name_dns_aaaa_record_on_launch": "false", "id": "subnet-0c6066202240c38be", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": "false", "map_customer_owned_ip_on_launch": "false", "map_public_ip_on_launch": "false", "outpost_arn": "", "owner_id": "046276255144", "private_dns_hostname_type_on_launch": "ip-name", "tags.%": "10", "tags.AutoShutdown": "true", "tags.Compartment": "ABLRHF", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "infovault-dev-ablrhf-subnet-az1", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags.Tier": "Private", "tags_all.%": "10", "tags_all.AutoShutdown": "true", "tags_all.Compartment": "ABLRHF", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "infovault-dev-ablrhf-subnet-az1", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "tags_all.Tier": "Private", "vpc_id": "vpc-0e75036a2ad9e5b4f"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_subnet.tfer--subnet-0c775a4f57dfa0e61": {"type": "aws_subnet", "depends_on": [], "primary": {"id": "subnet-0c775a4f57dfa0e61", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:subnet/subnet-0c775a4f57dfa0e61", "assign_ipv6_address_on_creation": "false", "availability_zone": "ap-southeast-1b", "availability_zone_id": "apse1-az2", "cidr_block": "***********/26", "customer_owned_ipv4_pool": "", "enable_dns64": "false", "enable_lni_at_device_index": "0", "enable_resource_name_dns_a_record_on_launch": "false", "enable_resource_name_dns_aaaa_record_on_launch": "false", "id": "subnet-0c775a4f57dfa0e61", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": "false", "map_customer_owned_ip_on_launch": "false", "map_public_ip_on_launch": "false", "outpost_arn": "", "owner_id": "046276255144", "private_dns_hostname_type_on_launch": "ip-name", "tags.%": "6", "tags.AutoShutdown": "true", "tags.Environment": "dev", "tags.Name": "db-subnet-az2", "tags.Owner": "<PERSON><PERSON>", "tags.Project": "InfoVault", "tags.Purpose": "Database", "tags_all.%": "6", "tags_all.AutoShutdown": "true", "tags_all.Environment": "dev", "tags_all.Name": "db-subnet-az2", "tags_all.Owner": "<PERSON><PERSON>", "tags_all.Project": "InfoVault", "tags_all.Purpose": "Database", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_subnet.tfer--subnet-0d4f42cf30b20c42d": {"type": "aws_subnet", "depends_on": [], "primary": {"id": "subnet-0d4f42cf30b20c42d", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:subnet/subnet-0d4f42cf30b20c42d", "assign_ipv6_address_on_creation": "false", "availability_zone": "ap-southeast-1a", "availability_zone_id": "apse1-az1", "cidr_block": "**********/26", "customer_owned_ipv4_pool": "", "enable_dns64": "false", "enable_lni_at_device_index": "0", "enable_resource_name_dns_a_record_on_launch": "false", "enable_resource_name_dns_aaaa_record_on_launch": "false", "id": "subnet-0d4f42cf30b20c42d", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": "false", "map_customer_owned_ip_on_launch": "false", "map_public_ip_on_launch": "false", "outpost_arn": "", "owner_id": "046276255144", "private_dns_hostname_type_on_launch": "ip-name", "tags.%": "6", "tags.AutoShutdown": "true", "tags.Environment": "dev", "tags.Name": "db-subnet-az1", "tags.Owner": "<PERSON><PERSON>", "tags.Project": "InfoVault", "tags.Purpose": "Database", "tags_all.%": "6", "tags_all.AutoShutdown": "true", "tags_all.Environment": "dev", "tags_all.Name": "db-subnet-az1", "tags_all.Owner": "<PERSON><PERSON>", "tags_all.Project": "InfoVault", "tags_all.Purpose": "Database", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_subnet.tfer--subnet-0dacd0ef604204014": {"type": "aws_subnet", "depends_on": [], "primary": {"id": "subnet-0dacd0ef604204014", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:subnet/subnet-0dacd0ef604204014", "assign_ipv6_address_on_creation": "false", "availability_zone": "ap-southeast-1a", "availability_zone_id": "apse1-az1", "cidr_block": "**********/28", "customer_owned_ipv4_pool": "", "enable_dns64": "false", "enable_lni_at_device_index": "0", "enable_resource_name_dns_a_record_on_launch": "false", "enable_resource_name_dns_aaaa_record_on_launch": "false", "id": "subnet-0dacd0ef604204014", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": "false", "map_customer_owned_ip_on_launch": "false", "map_public_ip_on_launch": "false", "outpost_arn": "", "owner_id": "046276255144", "private_dns_hostname_type_on_launch": "ip-name", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "infovault-dev-patching-subnet-az1", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "infovault-dev-patching-subnet-az1", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_id": "vpc-0ca0480cce82946b3"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_subnet.tfer--subnet-0e2a201f2503f59ee": {"type": "aws_subnet", "depends_on": [], "primary": {"id": "subnet-0e2a201f2503f59ee", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:subnet/subnet-0e2a201f2503f59ee", "assign_ipv6_address_on_creation": "false", "availability_zone": "ap-southeast-1a", "availability_zone_id": "apse1-az1", "cidr_block": "**********/28", "customer_owned_ipv4_pool": "", "enable_dns64": "false", "enable_lni_at_device_index": "0", "enable_resource_name_dns_a_record_on_launch": "false", "enable_resource_name_dns_aaaa_record_on_launch": "false", "id": "subnet-0e2a201f2503f59ee", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": "false", "map_customer_owned_ip_on_launch": "false", "map_public_ip_on_launch": "true", "outpost_arn": "", "owner_id": "046276255144", "private_dns_hostname_type_on_launch": "ip-name", "tags.%": "9", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "gen-ingress-az1", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags.Type": "Ingress", "tags_all.%": "9", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "gen-ingress-az1", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "tags_all.Type": "Ingress", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_subnet.tfer--subnet-0ebbc016c2fc6b3db": {"type": "aws_subnet", "depends_on": [], "primary": {"id": "subnet-0ebbc016c2fc6b3db", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:subnet/subnet-0ebbc016c2fc6b3db", "assign_ipv6_address_on_creation": "false", "availability_zone": "ap-southeast-1a", "availability_zone_id": "apse1-az1", "cidr_block": "***********/28", "customer_owned_ipv4_pool": "", "enable_dns64": "false", "enable_lni_at_device_index": "0", "enable_resource_name_dns_a_record_on_launch": "false", "enable_resource_name_dns_aaaa_record_on_launch": "false", "id": "subnet-0ebbc016c2fc6b3db", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": "false", "map_customer_owned_ip_on_launch": "false", "map_public_ip_on_launch": "false", "outpost_arn": "", "owner_id": "046276255144", "private_dns_hostname_type_on_launch": "ip-name", "tags.%": "5", "tags.AutoShutdown": "false", "tags.Environment": "dev", "tags.Name": "infovault-dev-ms-ad-subnet-a", "tags.Project": "infovault", "tags.Type": "ms-ad", "tags_all.%": "5", "tags_all.AutoShutdown": "false", "tags_all.Environment": "dev", "tags_all.Name": "infovault-dev-ms-ad-subnet-a", "tags_all.Project": "infovault", "tags_all.Type": "ms-ad", "vpc_id": "vpc-05f1b20a6634c6a38"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_subnet.tfer--subnet-0f80c3ddc6536fd2a": {"type": "aws_subnet", "depends_on": [], "primary": {"id": "subnet-0f80c3ddc6536fd2a", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:subnet/subnet-0f80c3ddc6536fd2a", "assign_ipv6_address_on_creation": "false", "availability_zone": "ap-southeast-1a", "availability_zone_id": "apse1-az1", "cidr_block": "***********/28", "customer_owned_ipv4_pool": "", "enable_dns64": "false", "enable_lni_at_device_index": "0", "enable_resource_name_dns_a_record_on_launch": "false", "enable_resource_name_dns_aaaa_record_on_launch": "false", "id": "subnet-0f80c3ddc6536fd2a", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": "false", "map_customer_owned_ip_on_launch": "false", "map_public_ip_on_launch": "false", "outpost_arn": "", "owner_id": "046276255144", "private_dns_hostname_type_on_launch": "ip-name", "tags.%": "9", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "infovault-dev-private-db-subnet-a", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags.Tier": "PrivateDB", "tags_all.%": "9", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "infovault-dev-private-db-subnet-a", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "tags_all.Tier": "PrivateDB", "vpc_id": "vpc-05f1b20a6634c6a38"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}}, "depends_on": []}]}