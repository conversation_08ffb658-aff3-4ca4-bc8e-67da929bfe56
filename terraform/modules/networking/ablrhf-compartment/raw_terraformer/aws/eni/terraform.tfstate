{"version": 3, "terraform_version": "0.12.31", "serial": 1, "lineage": "9b65699e-4986-b4d5-926e-34364ad297ce", "modules": [{"path": ["root"], "outputs": {"aws_network_interface_tfer--eni-0000fc2c82b0ad41e_id": {"sensitive": false, "type": "string", "value": "eni-0000fc2c82b0ad41e"}, "aws_network_interface_tfer--eni-001322889d8fcc4d5_id": {"sensitive": false, "type": "string", "value": "eni-001322889d8fcc4d5"}, "aws_network_interface_tfer--eni-001dd84b73a7b022c_id": {"sensitive": false, "type": "string", "value": "eni-001dd84b73a7b022c"}, "aws_network_interface_tfer--eni-0035a1c563125e610_id": {"sensitive": false, "type": "string", "value": "eni-0035a1c563125e610"}, "aws_network_interface_tfer--eni-0039dd184b1ba0efb_id": {"sensitive": false, "type": "string", "value": "eni-0039dd184b1ba0efb"}, "aws_network_interface_tfer--eni-00424d22e169cc12d_id": {"sensitive": false, "type": "string", "value": "eni-00424d22e169cc12d"}, "aws_network_interface_tfer--eni-0196ea3e075caabb4_id": {"sensitive": false, "type": "string", "value": "eni-0196ea3e075caabb4"}, "aws_network_interface_tfer--eni-01983a4e02f2205aa_id": {"sensitive": false, "type": "string", "value": "eni-01983a4e02f2205aa"}, "aws_network_interface_tfer--eni-01cda4f27be0d17ff_id": {"sensitive": false, "type": "string", "value": "eni-01cda4f27be0d17ff"}, "aws_network_interface_tfer--eni-020e308911f0f67e6_id": {"sensitive": false, "type": "string", "value": "eni-020e308911f0f67e6"}, "aws_network_interface_tfer--eni-025ca62a2a6231a95_id": {"sensitive": false, "type": "string", "value": "eni-025ca62a2a6231a95"}, "aws_network_interface_tfer--eni-02992b069ff5375fe_id": {"sensitive": false, "type": "string", "value": "eni-02992b069ff5375fe"}, "aws_network_interface_tfer--eni-02c628803f0bb3480_id": {"sensitive": false, "type": "string", "value": "eni-02c628803f0bb3480"}, "aws_network_interface_tfer--eni-030e4634c1ca971d9_id": {"sensitive": false, "type": "string", "value": "eni-030e4634c1ca971d9"}, "aws_network_interface_tfer--eni-03394e0cbba7b7f65_id": {"sensitive": false, "type": "string", "value": "eni-03394e0cbba7b7f65"}, "aws_network_interface_tfer--eni-03622b623475ba86b_id": {"sensitive": false, "type": "string", "value": "eni-03622b623475ba86b"}, "aws_network_interface_tfer--eni-03ac92a8fb4055b11_id": {"sensitive": false, "type": "string", "value": "eni-03ac92a8fb4055b11"}, "aws_network_interface_tfer--eni-045645a59befccdf5_id": {"sensitive": false, "type": "string", "value": "eni-045645a59befccdf5"}, "aws_network_interface_tfer--eni-0488dabede95ced8b_id": {"sensitive": false, "type": "string", "value": "eni-0488dabede95ced8b"}, "aws_network_interface_tfer--eni-04f8f5201882ec24e_id": {"sensitive": false, "type": "string", "value": "eni-04f8f5201882ec24e"}, "aws_network_interface_tfer--eni-05073405d05bfbf47_id": {"sensitive": false, "type": "string", "value": "eni-05073405d05bfbf47"}, "aws_network_interface_tfer--eni-056e371c87c8ead5f_id": {"sensitive": false, "type": "string", "value": "eni-056e371c87c8ead5f"}, "aws_network_interface_tfer--eni-056fdfcc740d248fe_id": {"sensitive": false, "type": "string", "value": "eni-056fdfcc740d248fe"}, "aws_network_interface_tfer--eni-05708b1c080c287b1_id": {"sensitive": false, "type": "string", "value": "eni-05708b1c080c287b1"}, "aws_network_interface_tfer--eni-0574685d488017b52_id": {"sensitive": false, "type": "string", "value": "eni-0574685d488017b52"}, "aws_network_interface_tfer--eni-058e6cbf33ccafc06_id": {"sensitive": false, "type": "string", "value": "eni-058e6cbf33ccafc06"}, "aws_network_interface_tfer--eni-05b03ded8d9e22c98_id": {"sensitive": false, "type": "string", "value": "eni-05b03ded8d9e22c98"}, "aws_network_interface_tfer--eni-05d697cc2b6059220_id": {"sensitive": false, "type": "string", "value": "eni-05d697cc2b6059220"}, "aws_network_interface_tfer--eni-05f08707c0961ac34_id": {"sensitive": false, "type": "string", "value": "eni-05f08707c0961ac34"}, "aws_network_interface_tfer--eni-06081ded7801e8023_id": {"sensitive": false, "type": "string", "value": "eni-06081ded7801e8023"}, "aws_network_interface_tfer--eni-066838eeb94b2abd3_id": {"sensitive": false, "type": "string", "value": "eni-066838eeb94b2abd3"}, "aws_network_interface_tfer--eni-06a248b1f83a2a3fb_id": {"sensitive": false, "type": "string", "value": "eni-06a248b1f83a2a3fb"}, "aws_network_interface_tfer--eni-0767288679be0f910_id": {"sensitive": false, "type": "string", "value": "eni-0767288679be0f910"}, "aws_network_interface_tfer--eni-0770cc77805897af3_id": {"sensitive": false, "type": "string", "value": "eni-0770cc77805897af3"}, "aws_network_interface_tfer--eni-07765172bcd661056_id": {"sensitive": false, "type": "string", "value": "eni-07765172bcd661056"}, "aws_network_interface_tfer--eni-07b95a8cc49b899ea_id": {"sensitive": false, "type": "string", "value": "eni-07b95a8cc49b899ea"}, "aws_network_interface_tfer--eni-07e82bc1231965e15_id": {"sensitive": false, "type": "string", "value": "eni-07e82bc1231965e15"}, "aws_network_interface_tfer--eni-07f709056a9c94268_id": {"sensitive": false, "type": "string", "value": "eni-07f709056a9c94268"}, "aws_network_interface_tfer--eni-0811737db6ea8b294_id": {"sensitive": false, "type": "string", "value": "eni-0811737db6ea8b294"}, "aws_network_interface_tfer--eni-082053ae8b1f25e1a_id": {"sensitive": false, "type": "string", "value": "eni-082053ae8b1f25e1a"}, "aws_network_interface_tfer--eni-082f6d6b35faea6c6_id": {"sensitive": false, "type": "string", "value": "eni-082f6d6b35faea6c6"}, "aws_network_interface_tfer--eni-0837bf55019d46b31_id": {"sensitive": false, "type": "string", "value": "eni-0837bf55019d46b31"}, "aws_network_interface_tfer--eni-0842c653b0dad4fbd_id": {"sensitive": false, "type": "string", "value": "eni-0842c653b0dad4fbd"}, "aws_network_interface_tfer--eni-08593c53b89a60ee4_id": {"sensitive": false, "type": "string", "value": "eni-08593c53b89a60ee4"}, "aws_network_interface_tfer--eni-09379042c909d9d32_id": {"sensitive": false, "type": "string", "value": "eni-09379042c909d9d32"}, "aws_network_interface_tfer--eni-0958f29b852894cd2_id": {"sensitive": false, "type": "string", "value": "eni-0958f29b852894cd2"}, "aws_network_interface_tfer--eni-096d098e8a09d429b_id": {"sensitive": false, "type": "string", "value": "eni-096d098e8a09d429b"}, "aws_network_interface_tfer--eni-09dec8943b53ed058_id": {"sensitive": false, "type": "string", "value": "eni-09dec8943b53ed058"}, "aws_network_interface_tfer--eni-09e6836ed6735715e_id": {"sensitive": false, "type": "string", "value": "eni-09e6836ed6735715e"}, "aws_network_interface_tfer--eni-0a08f25491f59d58e_id": {"sensitive": false, "type": "string", "value": "eni-0a08f25491f59d58e"}, "aws_network_interface_tfer--eni-0a181a6d7acba6b00_id": {"sensitive": false, "type": "string", "value": "eni-0a181a6d7acba6b00"}, "aws_network_interface_tfer--eni-0a4a1d4b782759c8d_id": {"sensitive": false, "type": "string", "value": "eni-0a4a1d4b782759c8d"}, "aws_network_interface_tfer--eni-0a5fb5d8d833ef844_id": {"sensitive": false, "type": "string", "value": "eni-0a5fb5d8d833ef844"}, "aws_network_interface_tfer--eni-0b0c10f4ff924ca83_id": {"sensitive": false, "type": "string", "value": "eni-0b0c10f4ff924ca83"}, "aws_network_interface_tfer--eni-0bb4d63b69f41cce3_id": {"sensitive": false, "type": "string", "value": "eni-0bb4d63b69f41cce3"}, "aws_network_interface_tfer--eni-0bfc335183ba65d53_id": {"sensitive": false, "type": "string", "value": "eni-0bfc335183ba65d53"}, "aws_network_interface_tfer--eni-0c3359775b007e6ed_id": {"sensitive": false, "type": "string", "value": "eni-0c3359775b007e6ed"}, "aws_network_interface_tfer--eni-0c4e492f96b742a6f_id": {"sensitive": false, "type": "string", "value": "eni-0c4e492f96b742a6f"}, "aws_network_interface_tfer--eni-0c7ac735edacfcf5e_id": {"sensitive": false, "type": "string", "value": "eni-0c7ac735edacfcf5e"}, "aws_network_interface_tfer--eni-0cbabfcd450ce5061_id": {"sensitive": false, "type": "string", "value": "eni-0cbabfcd450ce5061"}, "aws_network_interface_tfer--eni-0cd213280801297e2_id": {"sensitive": false, "type": "string", "value": "eni-0cd213280801297e2"}, "aws_network_interface_tfer--eni-0cd3deb8661a82b94_id": {"sensitive": false, "type": "string", "value": "eni-0cd3deb8661a82b94"}, "aws_network_interface_tfer--eni-0d4bd2f33d18876ea_id": {"sensitive": false, "type": "string", "value": "eni-0d4bd2f33d18876ea"}, "aws_network_interface_tfer--eni-0d6e441784201ed85_id": {"sensitive": false, "type": "string", "value": "eni-0d6e441784201ed85"}, "aws_network_interface_tfer--eni-0de844e6b4a90ea21_id": {"sensitive": false, "type": "string", "value": "eni-0de844e6b4a90ea21"}, "aws_network_interface_tfer--eni-0e4892cdad21d3d27_id": {"sensitive": false, "type": "string", "value": "eni-0e4892cdad21d3d27"}, "aws_network_interface_tfer--eni-0e961cb5275d963d7_id": {"sensitive": false, "type": "string", "value": "eni-0e961cb5275d963d7"}, "aws_network_interface_tfer--eni-0edd3b7c8064ca22e_id": {"sensitive": false, "type": "string", "value": "eni-0edd3b7c8064ca22e"}, "aws_network_interface_tfer--eni-0f2f4d8868d50f09c_id": {"sensitive": false, "type": "string", "value": "eni-0f2f4d8868d50f09c"}, "aws_network_interface_tfer--eni-0f4f360a445263ef1_id": {"sensitive": false, "type": "string", "value": "eni-0f4f360a445263ef1"}, "aws_network_interface_tfer--eni-0f51e5d7d6ccb7f1b_id": {"sensitive": false, "type": "string", "value": "eni-0f51e5d7d6ccb7f1b"}, "aws_network_interface_tfer--eni-0f5e018aa1e689f80_id": {"sensitive": false, "type": "string", "value": "eni-0f5e018aa1e689f80"}, "aws_network_interface_tfer--eni-0f769cda654ffb0c2_id": {"sensitive": false, "type": "string", "value": "eni-0f769cda654ffb0c2"}, "aws_network_interface_tfer--eni-0f8b1e2c7cde7b2d6_id": {"sensitive": false, "type": "string", "value": "eni-0f8b1e2c7cde7b2d6"}, "aws_network_interface_tfer--eni-0fc71be00817c5145_id": {"sensitive": false, "type": "string", "value": "eni-0fc71be00817c5145"}, "aws_network_interface_tfer--eni-0ffcf9a54002de3ca_id": {"sensitive": false, "type": "string", "value": "eni-0ffcf9a54002de3ca"}}, "resources": {"aws_network_interface.tfer--eni-0000fc2c82b0ad41e": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-0000fc2c82b0ad41e", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-0000fc2c82b0ad41e", "attachment.#": "1", "attachment.0.attachment_id": "ela-attach-0e62d071a8d3c0092", "attachment.0.device_index": "1", "attachment.0.instance": "", "description": "VPC Endpoint Interface vpce-0cd55ff1d8ac0f61f", "id": "eni-0000fc2c82b0ad41e", "interface_type": "vpc_endpoint", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "06:03:6f:14:16:c7", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-100-15-0-44.ap-southeast-1.compute.internal", "private_ip": "***********", "private_ip_list.#": "1", "private_ip_list.0": "***********", "private_ips.#": "1", "private_ips.0": "***********", "private_ips_count": "0", "security_groups.#": "1", "security_groups.0": "sg-0d28b5ada1bb60c9a", "source_dest_check": "true", "subnet_id": "subnet-04910ced717a5584a", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-001322889d8fcc4d5": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-001322889d8fcc4d5", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-001322889d8fcc4d5", "attachment.#": "1", "attachment.0.attachment_id": "eni-attach-022b273f28fb4ded5", "attachment.0.device_index": "1", "attachment.0.instance": "", "description": "ELB app/k8s-dev-albingre-ec7cdc5798/eae51a76a0223b29", "id": "eni-001322889d8fcc4d5", "interface_type": "interface", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "06:8b:b9:a3:a6:cd", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-100-67-2-187.ap-southeast-1.compute.internal", "private_ip": "************", "private_ip_list.#": "1", "private_ip_list.0": "************", "private_ips.#": "1", "private_ips.0": "************", "private_ips_count": "0", "security_groups.#": "2", "security_groups.0": "sg-0353836b1b373e27d", "security_groups.1": "sg-0950397297ecb203f", "source_dest_check": "true", "subnet_id": "subnet-015e4b2acb87091d9", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-001dd84b73a7b022c": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-001dd84b73a7b022c", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-001dd84b73a7b022c", "attachment.#": "1", "attachment.0.attachment_id": "eni-attach-08cfa0c920db74287", "attachment.0.device_index": "1", "attachment.0.instance": "", "description": "ELB app/gen-alb-internet-facing/7cdf809c994f03cb", "id": "eni-001dd84b73a7b022c", "interface_type": "interface", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "02:6c:db:69:40:67", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-10-66-0-24.ap-southeast-1.compute.internal", "private_ip": "**********", "private_ip_list.#": "1", "private_ip_list.0": "**********", "private_ips.#": "1", "private_ips.0": "**********", "private_ips_count": "0", "security_groups.#": "1", "security_groups.0": "sg-0c08c4e2aabf88592", "source_dest_check": "true", "subnet_id": "subnet-0e2a201f2503f59ee", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-0035a1c563125e610": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-0035a1c563125e610", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-0035a1c563125e610", "attachment.#": "1", "attachment.0.attachment_id": "eni-attach-0fbc3badb9726e31e", "attachment.0.device_index": "0", "attachment.0.instance": "i-01a8b5842ff1bff6a", "description": "", "id": "eni-0035a1c563125e610", "interface_type": "interface", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "02:1c:df:16:a9:9f", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-100-15-0-11.ap-southeast-1.compute.internal", "private_ip": "***********", "private_ip_list.#": "1", "private_ip_list.0": "***********", "private_ips.#": "1", "private_ips.0": "***********", "private_ips_count": "0", "security_groups.#": "1", "security_groups.0": "sg-0d7508facd515c5ea", "source_dest_check": "true", "subnet_id": "subnet-07ab4b059d69e4044", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-0039dd184b1ba0efb": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-0039dd184b1ba0efb", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-0039dd184b1ba0efb", "attachment.#": "1", "attachment.0.attachment_id": "eni-attach-005cb1298e1b84f43", "attachment.0.device_index": "0", "attachment.0.instance": "i-06f3f2b6a31516b31", "description": "", "id": "eni-0039dd184b1ba0efb", "interface_type": "interface", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "02:47:ee:7d:9d:99", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-100-15-0-58.ap-southeast-1.compute.internal", "private_ip": "***********", "private_ip_list.#": "1", "private_ip_list.0": "***********", "private_ips.#": "1", "private_ips.0": "***********", "private_ips_count": "0", "security_groups.#": "1", "security_groups.0": "sg-0511f8964219f7537", "source_dest_check": "true", "subnet_id": "subnet-0ebbc016c2fc6b3db", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-00424d22e169cc12d": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-00424d22e169cc12d", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-00424d22e169cc12d", "attachment.#": "1", "attachment.0.attachment_id": "eni-attach-01183b2d16ce0c442", "attachment.0.device_index": "1", "attachment.0.instance": "", "description": "ELB app/gen-alb-internet-facing/7cdf809c994f03cb", "id": "eni-00424d22e169cc12d", "interface_type": "interface", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "06:df:7e:6a:dc:ad", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-100-67-0-27.ap-southeast-1.compute.internal", "private_ip": "***********", "private_ip_list.#": "1", "private_ip_list.0": "***********", "private_ips.#": "1", "private_ips.0": "***********", "private_ips_count": "0", "security_groups.#": "1", "security_groups.0": "sg-0c08c4e2aabf88592", "source_dest_check": "true", "subnet_id": "subnet-081b6eb841b4451b3", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-0196ea3e075caabb4": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-0196ea3e075caabb4", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-0196ea3e075caabb4", "attachment.#": "1", "attachment.0.attachment_id": "ela-attach-0f9014fd8e4b7ed92", "attachment.0.device_index": "1", "attachment.0.instance": "", "description": "VPC Endpoint Interface vpce-0cd55ff1d8ac0f61f", "id": "eni-0196ea3e075caabb4", "interface_type": "vpc_endpoint", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "02:be:5d:4c:e6:01", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-100-15-0-57.ap-southeast-1.compute.internal", "private_ip": "***********", "private_ip_list.#": "1", "private_ip_list.0": "***********", "private_ips.#": "1", "private_ips.0": "***********", "private_ips_count": "0", "security_groups.#": "1", "security_groups.0": "sg-0d28b5ada1bb60c9a", "source_dest_check": "true", "subnet_id": "subnet-0ebbc016c2fc6b3db", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-01983a4e02f2205aa": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-01983a4e02f2205aa", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-01983a4e02f2205aa", "attachment.#": "1", "attachment.0.attachment_id": "ela-attach-0da675eac7614e023", "attachment.0.device_index": "1", "attachment.0.instance": "", "description": "VPC Endpoint Interface vpce-0ed0b1bdb378589bc", "id": "eni-01983a4e02f2205aa", "interface_type": "vpc_endpoint", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "06:0c:6d:9a:a0:61", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-100-67-2-157.ap-southeast-1.compute.internal", "private_ip": "************", "private_ip_list.#": "1", "private_ip_list.0": "************", "private_ips.#": "1", "private_ips.0": "************", "private_ips_count": "0", "security_groups.#": "1", "security_groups.0": "sg-05f09a0dc7ab8c53f", "source_dest_check": "true", "subnet_id": "subnet-015e4b2acb87091d9", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-01cda4f27be0d17ff": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-01cda4f27be0d17ff", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-01cda4f27be0d17ff", "attachment.#": "1", "attachment.0.attachment_id": "ela-attach-0d28aedf2a6444bdb", "attachment.0.device_index": "1", "attachment.0.instance": "", "description": "VPC Endpoint Interface vpce-03557a8cac26c8917", "id": "eni-01cda4f27be0d17ff", "interface_type": "vpc_endpoint", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "02:cb:72:ff:0a:65", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-100-14-0-13.ap-southeast-1.compute.internal", "private_ip": "***********", "private_ip_list.#": "1", "private_ip_list.0": "***********", "private_ips.#": "1", "private_ips.0": "***********", "private_ips_count": "0", "security_groups.#": "1", "security_groups.0": "sg-0870c466d0db940c4", "source_dest_check": "true", "subnet_id": "subnet-0dacd0ef604204014", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-020e308911f0f67e6": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-020e308911f0f67e6", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-020e308911f0f67e6", "attachment.#": "1", "attachment.0.attachment_id": "ela-attach-06dab84c036ebb2fe", "attachment.0.device_index": "1", "attachment.0.instance": "", "description": "VPC Endpoint Interface vpce-084596619ed02fac1", "id": "eni-020e308911f0f67e6", "interface_type": "vpc_endpoint", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "02:9f:6e:c6:e0:1f", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-100-14-0-7.ap-southeast-1.compute.internal", "private_ip": "**********", "private_ip_list.#": "1", "private_ip_list.0": "**********", "private_ips.#": "1", "private_ips.0": "**********", "private_ips_count": "0", "security_groups.#": "1", "security_groups.0": "sg-0870c466d0db940c4", "source_dest_check": "true", "subnet_id": "subnet-0dacd0ef604204014", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-025ca62a2a6231a95": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-025ca62a2a6231a95", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-025ca62a2a6231a95", "attachment.#": "1", "attachment.0.attachment_id": "eni-attach-06574821f4b81d45d", "attachment.0.device_index": "0", "attachment.0.instance": "i-0e9981c8b65f2dd81", "description": "", "id": "eni-025ca62a2a6231a95", "interface_type": "interface", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "02:70:ca:5d:b5:ed", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-100-67-1-244.ap-southeast-1.compute.internal", "private_ip": "************", "private_ip_list.#": "1", "private_ip_list.0": "************", "private_ips.#": "1", "private_ips.0": "************", "private_ips_count": "0", "security_groups.#": "1", "security_groups.0": "sg-095c31fa3b94c41ae", "source_dest_check": "true", "subnet_id": "subnet-023cafbedbb31d13e", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-02992b069ff5375fe": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-02992b069ff5375fe", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-02992b069ff5375fe", "attachment.#": "1", "attachment.0.attachment_id": "ela-attach-07ad4e1d963d6ab40", "attachment.0.device_index": "1", "attachment.0.instance": "", "description": "ELB net/infovault-nlb/aa06b8f0bdcc828a", "id": "eni-02992b069ff5375fe", "interface_type": "network_load_balancer", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "06:31:2b:99:9f:03", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-100-67-3-97.ap-southeast-1.compute.internal", "private_ip": "***********", "private_ip_list.#": "1", "private_ip_list.0": "***********", "private_ips.#": "1", "private_ips.0": "***********", "private_ips_count": "0", "security_groups.#": "0", "source_dest_check": "false", "subnet_id": "subnet-0c775a4f57dfa0e61", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-02c628803f0bb3480": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-02c628803f0bb3480", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-02c628803f0bb3480", "attachment.#": "1", "attachment.0.attachment_id": "ela-attach-00e230c516198beb7", "attachment.0.device_index": "1", "attachment.0.instance": "", "description": "VPC Endpoint Interface vpce-099452683bdb0a349", "id": "eni-02c628803f0bb3480", "interface_type": "vpc_endpoint", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "06:cb:c3:31:62:a7", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-100-67-2-171.ap-southeast-1.compute.internal", "private_ip": "************", "private_ip_list.#": "1", "private_ip_list.0": "************", "private_ips.#": "1", "private_ips.0": "************", "private_ips_count": "0", "security_groups.#": "1", "security_groups.0": "sg-05f09a0dc7ab8c53f", "source_dest_check": "true", "subnet_id": "subnet-015e4b2acb87091d9", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-030e4634c1ca971d9": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-030e4634c1ca971d9", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-030e4634c1ca971d9", "attachment.#": "1", "attachment.0.attachment_id": "ela-attach-045a7376a6a97657a", "attachment.0.device_index": "1", "attachment.0.instance": "", "description": "VPC Endpoint Interface vpce-099452683bdb0a349", "id": "eni-030e4634c1ca971d9", "interface_type": "vpc_endpoint", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "02:73:3c:44:b8:13", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-100-67-1-47.ap-southeast-1.compute.internal", "private_ip": "***********", "private_ip_list.#": "1", "private_ip_list.0": "***********", "private_ips.#": "1", "private_ips.0": "***********", "private_ips_count": "0", "security_groups.#": "1", "security_groups.0": "sg-05f09a0dc7ab8c53f", "source_dest_check": "true", "subnet_id": "subnet-023cafbedbb31d13e", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-03394e0cbba7b7f65": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-03394e0cbba7b7f65", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-03394e0cbba7b7f65", "attachment.#": "1", "attachment.0.attachment_id": "ela-attach-0d2196ceca7f2a7cf", "attachment.0.device_index": "1", "attachment.0.instance": "", "description": "VPC Endpoint Interface vpce-0ed0b1bdb378589bc", "id": "eni-03394e0cbba7b7f65", "interface_type": "vpc_endpoint", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "02:68:cd:a9:9d:f7", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-100-67-1-165.ap-southeast-1.compute.internal", "private_ip": "************", "private_ip_list.#": "1", "private_ip_list.0": "************", "private_ips.#": "1", "private_ips.0": "************", "private_ips_count": "0", "security_groups.#": "1", "security_groups.0": "sg-05f09a0dc7ab8c53f", "source_dest_check": "true", "subnet_id": "subnet-023cafbedbb31d13e", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-03622b623475ba86b": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-03622b623475ba86b", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-03622b623475ba86b", "attachment.#": "1", "attachment.0.attachment_id": "eni-attach-0459da0bd21495aac", "attachment.0.device_index": "0", "attachment.0.instance": "i-0a68d4d8b08ad6fab", "description": "", "id": "eni-03622b623475ba86b", "interface_type": "interface", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "02:b8:46:3e:6a:cf", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-10-66-0-4.ap-southeast-1.compute.internal", "private_ip": "*********", "private_ip_list.#": "1", "private_ip_list.0": "*********", "private_ips.#": "1", "private_ips.0": "*********", "private_ips_count": "0", "security_groups.#": "1", "security_groups.0": "sg-011e29a277dde9f4a", "source_dest_check": "true", "subnet_id": "subnet-043dd23a18b80a280", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-03ac92a8fb4055b11": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-03ac92a8fb4055b11", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-03ac92a8fb4055b11", "attachment.#": "1", "attachment.0.attachment_id": "eni-attach-088b16ae94b7dfcc9", "attachment.0.device_index": "0", "attachment.0.instance": "i-0fd3c2460891779a9", "description": "", "id": "eni-03ac92a8fb4055b11", "interface_type": "interface", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "02:76:16:7f:43:cd", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-100-15-0-9.ap-southeast-1.compute.internal", "private_ip": "**********", "private_ip_list.#": "1", "private_ip_list.0": "**********", "private_ips.#": "1", "private_ips.0": "**********", "private_ips_count": "0", "security_groups.#": "1", "security_groups.0": "sg-00df94fe66c7464f5", "source_dest_check": "true", "subnet_id": "subnet-07ab4b059d69e4044", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-045645a59befccdf5": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-045645a59befccdf5", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-045645a59befccdf5", "attachment.#": "1", "attachment.0.attachment_id": "eni-attach-0ddd8df35e155a602", "attachment.0.device_index": "0", "attachment.0.instance": "i-062061c311cde5ef0", "description": "", "id": "eni-045645a59befccdf5", "interface_type": "interface", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "02:d4:67:f8:af:7d", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-100-15-0-5.ap-southeast-1.compute.internal", "private_ip": "**********", "private_ip_list.#": "1", "private_ip_list.0": "**********", "private_ips.#": "1", "private_ips.0": "**********", "private_ips_count": "0", "security_groups.#": "1", "security_groups.0": "sg-0b67c6b7904908adf", "source_dest_check": "true", "subnet_id": "subnet-07ab4b059d69e4044", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-0488dabede95ced8b": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-0488dabede95ced8b", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-0488dabede95ced8b", "attachment.#": "1", "attachment.0.attachment_id": "ela-attach-030245722cecdd20f", "attachment.0.device_index": "1", "attachment.0.instance": "", "description": "VPC Endpoint Interface vpce-0aab6fc33b265ed8d", "id": "eni-0488dabede95ced8b", "interface_type": "gateway_load_balancer_endpoint", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "02:e9:fb:bf:c6:c1", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-100-67-0-22.ap-southeast-1.compute.internal", "private_ip": "***********", "private_ip_list.#": "1", "private_ip_list.0": "***********", "private_ips.#": "1", "private_ips.0": "***********", "private_ips_count": "0", "security_groups.#": "0", "source_dest_check": "false", "subnet_id": "subnet-0aec855b39a7be02e", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-04f8f5201882ec24e": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-04f8f5201882ec24e", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-04f8f5201882ec24e", "attachment.#": "1", "attachment.0.attachment_id": "ela-attach-0e3079d284f7ba447", "attachment.0.device_index": "1", "attachment.0.instance": "", "description": "VPC Endpoint Interface vpce-0bf3a106d192c71cd", "id": "eni-04f8f5201882ec24e", "interface_type": "vpc_endpoint", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "02:ae:35:f8:d4:67", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-100-67-0-11.ap-southeast-1.compute.internal", "private_ip": "***********", "private_ip_list.#": "1", "private_ip_list.0": "***********", "private_ips.#": "1", "private_ips.0": "***********", "private_ips_count": "0", "security_groups.#": "1", "security_groups.0": "sg-0455dd83ca1b155d3", "source_dest_check": "true", "subnet_id": "subnet-01e871c49dc4b9b1f", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-05073405d05bfbf47": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-05073405d05bfbf47", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-05073405d05bfbf47", "attachment.#": "1", "attachment.0.attachment_id": "eni-attach-0221b09572c8efe68", "attachment.0.device_index": "1", "attachment.0.instance": "", "description": "AWS created network interface for directory d-9667b2da13", "id": "eni-05073405d05bfbf47", "interface_type": "interface", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "06:ce:35:29:13:57", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-100-15-0-40.ap-southeast-1.compute.internal", "private_ip": "***********", "private_ip_list.#": "1", "private_ip_list.0": "***********", "private_ips.#": "1", "private_ips.0": "***********", "private_ips_count": "0", "security_groups.#": "1", "security_groups.0": "sg-0f48a9bd414cfd781", "source_dest_check": "true", "subnet_id": "subnet-04910ced717a5584a", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-056e371c87c8ead5f": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-056e371c87c8ead5f", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-056e371c87c8ead5f", "attachment.#": "1", "attachment.0.attachment_id": "eni-attach-0a0522856e2d63399", "attachment.0.device_index": "0", "attachment.0.instance": "i-0a490aa59b0fb4688", "description": "", "id": "eni-056e371c87c8ead5f", "interface_type": "interface", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "02:97:e7:a9:30:4d", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-100-15-0-4.ap-southeast-1.compute.internal", "private_ip": "**********", "private_ip_list.#": "1", "private_ip_list.0": "**********", "private_ips.#": "1", "private_ips.0": "**********", "private_ips_count": "0", "security_groups.#": "1", "security_groups.0": "sg-0f2fdd192120dbf17", "source_dest_check": "true", "subnet_id": "subnet-07ab4b059d69e4044", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-056fdfcc740d248fe": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-056fdfcc740d248fe", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-056fdfcc740d248fe", "attachment.#": "1", "attachment.0.attachment_id": "eni-attach-0099895e04dda1068", "attachment.0.device_index": "0", "attachment.0.instance": "i-0a9b2de4a3fc29869", "description": "", "id": "eni-056fdfcc740d248fe", "interface_type": "interface", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "02:9a:52:6d:3f:d7", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-100-15-0-8.ap-southeast-1.compute.internal", "private_ip": "**********", "private_ip_list.#": "1", "private_ip_list.0": "**********", "private_ips.#": "1", "private_ips.0": "**********", "private_ips_count": "0", "security_groups.#": "1", "security_groups.0": "sg-095117b1a793c2ce0", "source_dest_check": "true", "subnet_id": "subnet-07ab4b059d69e4044", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-05708b1c080c287b1": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-05708b1c080c287b1", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-05708b1c080c287b1", "attachment.#": "1", "attachment.0.attachment_id": "ela-attach-06e10dcf05fb3f178", "attachment.0.device_index": "1", "attachment.0.instance": "", "description": "VPC Endpoint Interface vpce-0c5ff88c30a3e428d", "id": "eni-05708b1c080c287b1", "interface_type": "vpc_endpoint", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "06:95:55:ac:d7:1b", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-100-15-0-38.ap-southeast-1.compute.internal", "private_ip": "***********", "private_ip_list.#": "1", "private_ip_list.0": "***********", "private_ips.#": "1", "private_ips.0": "***********", "private_ips_count": "0", "security_groups.#": "1", "security_groups.0": "sg-065d1e24d43126489", "source_dest_check": "true", "subnet_id": "subnet-04910ced717a5584a", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-0574685d488017b52": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-0574685d488017b52", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-0574685d488017b52", "attachment.#": "1", "attachment.0.attachment_id": "eni-attach-07f5a0999dbaf1dc8", "attachment.0.device_index": "1", "attachment.0.instance": "", "description": "AWS created network interface for directory d-9667b2da13", "id": "eni-0574685d488017b52", "interface_type": "interface", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "02:47:01:cf:ac:63", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-100-15-0-61.ap-southeast-1.compute.internal", "private_ip": "***********", "private_ip_list.#": "1", "private_ip_list.0": "***********", "private_ips.#": "1", "private_ips.0": "***********", "private_ips_count": "0", "security_groups.#": "1", "security_groups.0": "sg-0f48a9bd414cfd781", "source_dest_check": "true", "subnet_id": "subnet-0ebbc016c2fc6b3db", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-058e6cbf33ccafc06": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-058e6cbf33ccafc06", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-058e6cbf33ccafc06", "attachment.#": "1", "attachment.0.attachment_id": "eni-attach-0a9288ac926bb7b41", "attachment.0.device_index": "1", "attachment.0.instance": "", "description": "ELB app/k8s-dev-albingre-ec7cdc5798/eae51a76a0223b29", "id": "eni-058e6cbf33ccafc06", "interface_type": "interface", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "02:90:33:a1:eb:63", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-100-67-1-242.ap-southeast-1.compute.internal", "private_ip": "************", "private_ip_list.#": "1", "private_ip_list.0": "************", "private_ips.#": "1", "private_ips.0": "************", "private_ips_count": "0", "security_groups.#": "2", "security_groups.0": "sg-0353836b1b373e27d", "security_groups.1": "sg-0950397297ecb203f", "source_dest_check": "true", "subnet_id": "subnet-023cafbedbb31d13e", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-05b03ded8d9e22c98": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-05b03ded8d9e22c98", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-05b03ded8d9e22c98", "attachment.#": "1", "attachment.0.attachment_id": "eni-attach-00d93eb035f444bf0", "attachment.0.device_index": "0", "attachment.0.instance": "i-0617cabf61d1e4692", "description": "", "id": "eni-05b03ded8d9e22c98", "interface_type": "interface", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "02:25:82:79:a0:7b", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-100-67-3-33.ap-southeast-1.compute.internal", "private_ip": "***********", "private_ip_list.#": "1", "private_ip_list.0": "***********", "private_ips.#": "1", "private_ips.0": "***********", "private_ips_count": "0", "security_groups.#": "1", "security_groups.0": "sg-0950397297ecb203f", "source_dest_check": "true", "subnet_id": "subnet-0d4f42cf30b20c42d", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-05d697cc2b6059220": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-05d697cc2b6059220", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-05d697cc2b6059220", "attachment.#": "1", "attachment.0.attachment_id": "eni-attach-0709218d64962d645", "attachment.0.device_index": "0", "attachment.0.instance": "i-046cd06b02397e146", "description": "", "id": "eni-05d697cc2b6059220", "interface_type": "interface", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "02:3f:5b:44:e5:af", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-100-14-0-5.ap-southeast-1.compute.internal", "private_ip": "**********", "private_ip_list.#": "1", "private_ip_list.0": "**********", "private_ips.#": "1", "private_ips.0": "**********", "private_ips_count": "0", "security_groups.#": "1", "security_groups.0": "sg-01f0c371bd11aa6f1", "source_dest_check": "true", "subnet_id": "subnet-0dacd0ef604204014", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-05f08707c0961ac34": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-05f08707c0961ac34", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-05f08707c0961ac34", "attachment.#": "1", "attachment.0.attachment_id": "ela-attach-0b6305526b7c46f28", "attachment.0.device_index": "1", "attachment.0.instance": "", "description": "VPC Endpoint Interface vpce-0fe5ae48246ae8a9d", "id": "eni-05f08707c0961ac34", "interface_type": "vpc_endpoint", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "02:7b:b0:11:30:67", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-100-15-0-6.ap-southeast-1.compute.internal", "private_ip": "**********", "private_ip_list.#": "1", "private_ip_list.0": "**********", "private_ips.#": "1", "private_ips.0": "**********", "private_ips_count": "0", "security_groups.#": "1", "security_groups.0": "sg-00df94fe66c7464f5", "source_dest_check": "true", "subnet_id": "subnet-07ab4b059d69e4044", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-06081ded7801e8023": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-06081ded7801e8023", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-06081ded7801e8023", "attachment.#": "1", "attachment.0.attachment_id": "eni-attach-0c4137972b456f4bd", "attachment.0.device_index": "0", "attachment.0.instance": "i-0691943f24194b6ae", "description": "", "id": "eni-06081ded7801e8023", "interface_type": "interface", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "02:27:f1:56:bf:53", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-100-67-3-47.ap-southeast-1.compute.internal", "private_ip": "***********", "private_ip_list.#": "1", "private_ip_list.0": "***********", "private_ips.#": "1", "private_ips.0": "***********", "private_ips_count": "0", "security_groups.#": "1", "security_groups.0": "sg-0950397297ecb203f", "source_dest_check": "true", "subnet_id": "subnet-0d4f42cf30b20c42d", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-066838eeb94b2abd3": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-066838eeb94b2abd3", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-066838eeb94b2abd3", "attachment.#": "1", "attachment.0.attachment_id": "ela-attach-0d7ed699f79c414a4", "attachment.0.device_index": "1", "attachment.0.instance": "", "description": "Interface for NAT Gateway nat-0431bfe8618055bd8", "id": "eni-066838eeb94b2abd3", "interface_type": "nat_gateway", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "02:86:57:63:59:21", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-10-66-0-14.ap-southeast-1.compute.internal", "private_ip": "**********", "private_ip_list.#": "1", "private_ip_list.0": "**********", "private_ips.#": "1", "private_ips.0": "**********", "private_ips_count": "0", "security_groups.#": "0", "source_dest_check": "false", "subnet_id": "subnet-043dd23a18b80a280", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-06a248b1f83a2a3fb": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-06a248b1f83a2a3fb", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-06a248b1f83a2a3fb", "attachment.#": "1", "attachment.0.attachment_id": "eni-attach-0197a65e735c515bb", "attachment.0.device_index": "0", "attachment.0.instance": "i-03ddfe5f32292e59d", "description": "", "id": "eni-06a248b1f83a2a3fb", "interface_type": "interface", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "06:e4:1b:3d:ba:29", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-100-67-2-110.ap-southeast-1.compute.internal", "private_ip": "************", "private_ip_list.#": "30", "private_ip_list.0": "************", "private_ip_list.1": "**********", "private_ip_list.10": "***********", "private_ip_list.11": "***********", "private_ip_list.12": "************", "private_ip_list.13": "***********", "private_ip_list.14": "***********", "private_ip_list.15": "************", "private_ip_list.16": "************", "private_ip_list.17": "**********3", "private_ip_list.18": "************", "private_ip_list.19": "************", "private_ip_list.2": "***********", "private_ip_list.20": "************", "private_ip_list.21": "************", "private_ip_list.22": "************", "private_ip_list.23": "************", "private_ip_list.24": "***********", "private_ip_list.25": "************", "private_ip_list.26": "***********", "private_ip_list.27": "************", "private_ip_list.28": "************", "private_ip_list.29": "***********", "private_ip_list.3": "************", "private_ip_list.4": "***********", "private_ip_list.5": "***********", "private_ip_list.6": "************", "private_ip_list.7": "***********", "private_ip_list.8": "************", "private_ip_list.9": "************", "private_ips.#": "30", "private_ips.0": "***********", "private_ips.1": "************", "private_ips.10": "************", "private_ips.11": "************", "private_ips.12": "************", "private_ips.13": "************", "private_ips.14": "************", "private_ips.15": "************", "private_ips.16": "************", "private_ips.17": "************", "private_ips.18": "************", "private_ips.19": "***********", "private_ips.2": "************", "private_ips.20": "***********", "private_ips.21": "**********", "private_ips.22": "**********3", "private_ips.23": "***********", "private_ips.24": "***********", "private_ips.25": "***********", "private_ips.26": "***********", "private_ips.27": "***********", "private_ips.28": "***********", "private_ips.29": "***********", "private_ips.3": "************", "private_ips.4": "************", "private_ips.5": "************", "private_ips.6": "************", "private_ips.7": "***********", "private_ips.8": "************", "private_ips.9": "************", "private_ips_count": "29", "security_groups.#": "1", "security_groups.0": "sg-0353836b1b373e27d", "source_dest_check": "true", "subnet_id": "subnet-015e4b2acb87091d9", "tags.%": "4", "tags.cluster.k8s.amazonaws.com/name": "infovault-dev-eks-cluster-v130", "tags.eks:cluster-name": "infovault-dev-eks-cluster-v130", "tags.eks:nodegroup-name": "v130-node-group-3", "tags.node.k8s.amazonaws.com/instance_id": "i-03ddfe5f32292e59d", "tags_all.%": "4", "tags_all.cluster.k8s.amazonaws.com/name": "infovault-dev-eks-cluster-v130", "tags_all.eks:cluster-name": "infovault-dev-eks-cluster-v130", "tags_all.eks:nodegroup-name": "v130-node-group-3", "tags_all.node.k8s.amazonaws.com/instance_id": "i-03ddfe5f32292e59d"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-0767288679be0f910": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-0767288679be0f910", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-0767288679be0f910", "attachment.#": "1", "attachment.0.attachment_id": "eni-attach-0ced0534e0a863583", "attachment.0.device_index": "0", "attachment.0.instance": "i-060584b2a431698f9", "description": "", "id": "eni-0767288679be0f910", "interface_type": "interface", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "06:fb:48:13:f4:2b", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-100-67-3-70.ap-southeast-1.compute.internal", "private_ip": "***********", "private_ip_list.#": "1", "private_ip_list.0": "***********", "private_ips.#": "1", "private_ips.0": "***********", "private_ips_count": "0", "security_groups.#": "1", "security_groups.0": "sg-0950397297ecb203f", "source_dest_check": "true", "subnet_id": "subnet-0c775a4f57dfa0e61", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-0770cc77805897af3": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-0770cc77805897af3", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-0770cc77805897af3", "attachment.#": "1", "attachment.0.attachment_id": "eni-attach-081c44420d2e2909c", "attachment.0.device_index": "1", "attachment.0.instance": "i-047eb48a87cdabc96", "description": "aws-K8S-i-047eb48a87cdabc96", "id": "eni-0770cc77805897af3", "interface_type": "interface", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "02:cd:70:92:12:cf", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-100-67-1-131.ap-southeast-1.compute.internal", "private_ip": "************", "private_ip_list.#": "30", "private_ip_list.0": "************", "private_ip_list.1": "************", "private_ip_list.10": "***********", "private_ip_list.11": "************", "private_ip_list.12": "************", "private_ip_list.13": "***********9", "private_ip_list.14": "************", "private_ip_list.15": "***********", "private_ip_list.16": "***********", "private_ip_list.17": "***********7", "private_ip_list.18": "***********", "private_ip_list.19": "***********4", "private_ip_list.2": "************", "private_ip_list.20": "************", "private_ip_list.21": "***********9", "private_ip_list.22": "************", "private_ip_list.23": "***********", "private_ip_list.24": "************", "private_ip_list.25": "************", "private_ip_list.26": "************", "private_ip_list.27": "**********3", "private_ip_list.28": "************", "private_ip_list.29": "************", "private_ip_list.3": "************", "private_ip_list.4": "************", "private_ip_list.5": "************", "private_ip_list.6": "***********", "private_ip_list.7": "************", "private_ip_list.8": "***********", "private_ip_list.9": "***********", "private_ips.#": "30", "private_ips.0": "************", "private_ips.1": "************", "private_ips.10": "***********7", "private_ips.11": "***********9", "private_ips.12": "************", "private_ips.13": "************", "private_ips.14": "************", "private_ips.15": "************", "private_ips.16": "************", "private_ips.17": "***********", "private_ips.18": "***********9", "private_ips.19": "************", "private_ips.2": "************", "private_ips.20": "************", "private_ips.21": "************", "private_ips.22": "************", "private_ips.23": "***********", "private_ips.24": "***********", "private_ips.25": "***********", "private_ips.26": "***********", "private_ips.27": "**********3", "private_ips.28": "***********", "private_ips.29": "***********", "private_ips.3": "************", "private_ips.4": "************", "private_ips.5": "************", "private_ips.6": "************", "private_ips.7": "************", "private_ips.8": "***********", "private_ips.9": "***********4", "private_ips_count": "29", "security_groups.#": "1", "security_groups.0": "sg-0353836b1b373e27d", "source_dest_check": "true", "subnet_id": "subnet-023cafbedbb31d13e", "tags.%": "3", "tags.cluster.k8s.amazonaws.com/name": "infovault-dev-eks-cluster-v130", "tags.node.k8s.amazonaws.com/createdAt": "2025-05-21T10:24:08Z", "tags.node.k8s.amazonaws.com/instance_id": "i-047eb48a87cdabc96", "tags_all.%": "3", "tags_all.cluster.k8s.amazonaws.com/name": "infovault-dev-eks-cluster-v130", "tags_all.node.k8s.amazonaws.com/createdAt": "2025-05-21T10:24:08Z", "tags_all.node.k8s.amazonaws.com/instance_id": "i-047eb48a87cdabc96"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-07765172bcd661056": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-07765172bcd661056", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-07765172bcd661056", "attachment.#": "1", "attachment.0.attachment_id": "ela-attach-0efc7516c7d62c440", "attachment.0.device_index": "1", "attachment.0.instance": "", "description": "VPC Endpoint Interface vpce-0538ad39973dc4aae", "id": "eni-07765172bcd661056", "interface_type": "vpc_endpoint", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "02:c8:50:f8:8c:91", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-10-66-0-38.ap-southeast-1.compute.internal", "private_ip": "10.66.0.38", "private_ip_list.#": "1", "private_ip_list.0": "10.66.0.38", "private_ips.#": "1", "private_ips.0": "10.66.0.38", "private_ips_count": "0", "security_groups.#": "1", "security_groups.0": "sg-066975c77a66a64d9", "source_dest_check": "true", "subnet_id": "subnet-0a1802eb99e96cbed", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-07b95a8cc49b899ea": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-07b95a8cc49b899ea", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-07b95a8cc49b899ea", "attachment.#": "1", "attachment.0.attachment_id": "ela-attach-0970750759a319ca1", "attachment.0.device_index": "1", "attachment.0.instance": "", "description": "VPC Endpoint Interface vpce-04d5a7ce83abf07b5", "id": "eni-07b95a8cc49b899ea", "interface_type": "vpc_endpoint", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "02:e9:b4:40:af:19", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-100-15-0-60.ap-southeast-1.compute.internal", "private_ip": "**********0", "private_ip_list.#": "1", "private_ip_list.0": "**********0", "private_ips.#": "1", "private_ips.0": "**********0", "private_ips_count": "0", "security_groups.#": "1", "security_groups.0": "sg-0d28b5ada1bb60c9a", "source_dest_check": "true", "subnet_id": "subnet-0ebbc016c2fc6b3db", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-07e82bc1231965e15": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-07e82bc1231965e15", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-07e82bc1231965e15", "attachment.#": "1", "attachment.0.attachment_id": "ela-attach-0624248452e7db833", "attachment.0.device_index": "1", "attachment.0.instance": "", "description": "VPC Endpoint Interface vpce-0f0b9eaa555525680", "id": "eni-07e82bc1231965e15", "interface_type": "vpc_endpoint", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "02:79:a9:43:3a:9b", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-10-66-0-46.ap-southeast-1.compute.internal", "private_ip": "*********6", "private_ip_list.#": "1", "private_ip_list.0": "*********6", "private_ips.#": "1", "private_ips.0": "*********6", "private_ips_count": "0", "security_groups.#": "1", "security_groups.0": "sg-066975c77a66a64d9", "source_dest_check": "true", "subnet_id": "subnet-0a1802eb99e96cbed", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-07f709056a9c94268": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-07f709056a9c94268", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-07f709056a9c94268", "attachment.#": "1", "attachment.0.attachment_id": "ela-attach-05547fc6b9a6c2a1f", "attachment.0.device_index": "1", "attachment.0.instance": "", "description": "VPC Endpoint Interface vpce-0d5ebafd2bcf055bf", "id": "eni-07f709056a9c94268", "interface_type": "vpc_endpoint", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "02:86:60:25:85:3b", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-10-66-0-39.ap-southeast-1.compute.internal", "private_ip": "**********", "private_ip_list.#": "1", "private_ip_list.0": "**********", "private_ips.#": "1", "private_ips.0": "**********", "private_ips_count": "0", "security_groups.#": "1", "security_groups.0": "sg-0650a95e2c0c1aaf5", "source_dest_check": "true", "subnet_id": "subnet-0a1802eb99e96cbed", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-0811737db6ea8b294": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-0811737db6ea8b294", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-0811737db6ea8b294", "attachment.#": "1", "attachment.0.attachment_id": "eni-attach-0badf83fdae4a7c8b", "attachment.0.device_index": "1", "attachment.0.instance": "", "description": "ELB app/k8s-dev-albingre-741d5ef5b1/071846906f6f3677", "id": "eni-0811737db6ea8b294", "interface_type": "interface", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "02:22:00:4a:f9:99", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-100-67-1-192.ap-southeast-1.compute.internal", "private_ip": "************", "private_ip_list.#": "1", "private_ip_list.0": "************", "private_ips.#": "1", "private_ips.0": "************", "private_ips_count": "0", "security_groups.#": "2", "security_groups.0": "sg-0353836b1b373e27d", "security_groups.1": "sg-0950397297ecb203f", "source_dest_check": "true", "subnet_id": "subnet-023cafbedbb31d13e", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-082053ae8b1f25e1a": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-082053ae8b1f25e1a", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-082053ae8b1f25e1a", "attachment.#": "1", "attachment.0.attachment_id": "eni-attach-032cd89d8ecaeac01", "attachment.0.device_index": "1", "attachment.0.instance": "", "description": "ELB app/k8s-dev-albingre-741d5ef5b1/071846906f6f3677", "id": "eni-082053ae8b1f25e1a", "interface_type": "interface", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "06:1f:c2:b2:4d:2f", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-100-67-2-66.ap-southeast-1.compute.internal", "private_ip": "***********", "private_ip_list.#": "1", "private_ip_list.0": "***********", "private_ips.#": "1", "private_ips.0": "***********", "private_ips_count": "0", "security_groups.#": "2", "security_groups.0": "sg-0353836b1b373e27d", "security_groups.1": "sg-0950397297ecb203f", "source_dest_check": "true", "subnet_id": "subnet-015e4b2acb87091d9", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-082f6d6b35faea6c6": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-082f6d6b35faea6c6", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-082f6d6b35faea6c6", "attachment.#": "1", "attachment.0.attachment_id": "eni-attach-072293e91b976db7b", "attachment.0.device_index": "0", "attachment.0.instance": "i-0d619a369131c9627", "description": "", "id": "eni-082f6d6b35faea6c6", "interface_type": "interface", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "06:42:f7:c1:21:d7", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-100-67-3-96.ap-southeast-1.compute.internal", "private_ip": "***********", "private_ip_list.#": "1", "private_ip_list.0": "***********", "private_ips.#": "1", "private_ips.0": "***********", "private_ips_count": "0", "security_groups.#": "1", "security_groups.0": "sg-02794d7f8eaa42fea", "source_dest_check": "true", "subnet_id": "subnet-0c775a4f57dfa0e61", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-0837bf55019d46b31": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-0837bf55019d46b31", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-0837bf55019d46b31", "attachment.#": "1", "attachment.0.attachment_id": "eni-attach-013b23c425e41288c", "attachment.0.device_index": "0", "attachment.0.instance": "i-03df1dee532d60a31", "description": "", "id": "eni-0837bf55019d46b31", "interface_type": "interface", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "02:e8:fe:bb:06:83", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-100-15-0-12.ap-southeast-1.compute.internal", "private_ip": "***********", "private_ip_list.#": "1", "private_ip_list.0": "***********", "private_ips.#": "1", "private_ips.0": "***********", "private_ips_count": "0", "security_groups.#": "1", "security_groups.0": "sg-0f2fdd192120dbf17", "source_dest_check": "true", "subnet_id": "subnet-07ab4b059d69e4044", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-0842c653b0dad4fbd": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-0842c653b0dad4fbd", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-0842c653b0dad4fbd", "attachment.#": "1", "attachment.0.attachment_id": "eni-attach-0e6234823f8213f49", "attachment.0.device_index": "0", "attachment.0.instance": "i-068a4053b9c1f23f0", "description": "", "id": "eni-0842c653b0dad4fbd", "interface_type": "interface", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "02:88:4d:35:c8:cf", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-100-15-0-7.ap-southeast-1.compute.internal", "private_ip": "**********", "private_ip_list.#": "1", "private_ip_list.0": "**********", "private_ips.#": "1", "private_ips.0": "**********", "private_ips_count": "0", "security_groups.#": "1", "security_groups.0": "sg-095117b1a793c2ce0", "source_dest_check": "true", "subnet_id": "subnet-07ab4b059d69e4044", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-08593c53b89a60ee4": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-08593c53b89a60ee4", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-08593c53b89a60ee4", "attachment.#": "1", "attachment.0.attachment_id": "ela-attach-0c9ea9f5b30d31f15", "attachment.0.device_index": "1", "attachment.0.instance": "", "description": "VPC Endpoint Interface vpce-0366281d6822bac46", "id": "eni-08593c53b89a60ee4", "interface_type": "vpc_endpoint", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "02:3b:c7:a2:d7:07", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-10-66-0-6.ap-southeast-1.compute.internal", "private_ip": "*********", "private_ip_list.#": "1", "private_ip_list.0": "*********", "private_ips.#": "1", "private_ips.0": "*********", "private_ips_count": "0", "security_groups.#": "1", "security_groups.0": "sg-082d3fc2b26b1004e", "source_dest_check": "true", "subnet_id": "subnet-0c6066202240c38be", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-09379042c909d9d32": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-09379042c909d9d32", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-09379042c909d9d32", "attachment.#": "1", "attachment.0.attachment_id": "ela-attach-069d6a4277a9e566a", "attachment.0.device_index": "1", "attachment.0.instance": "", "description": "VPC Endpoint Interface vpce-00634b26f7e325402", "id": "eni-09379042c909d9d32", "interface_type": "vpc_endpoint", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "02:8b:18:8e:51:dd", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-100-67-1-99.ap-southeast-1.compute.internal", "private_ip": "***********", "private_ip_list.#": "1", "private_ip_list.0": "***********", "private_ips.#": "1", "private_ips.0": "***********", "private_ips_count": "0", "security_groups.#": "1", "security_groups.0": "sg-05f09a0dc7ab8c53f", "source_dest_check": "true", "subnet_id": "subnet-023cafbedbb31d13e", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-0958f29b852894cd2": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-0958f29b852894cd2", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-0958f29b852894cd2", "attachment.#": "1", "attachment.0.attachment_id": "ela-attach-03b6d8b4c7bf50d72", "attachment.0.device_index": "1", "attachment.0.instance": "", "description": "VPC Endpoint Interface vpce-0ece27853f422d3a6", "id": "eni-0958f29b852894cd2", "interface_type": "vpc_endpoint", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "02:6e:8c:cd:2b:23", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-10-66-0-7.ap-southeast-1.compute.internal", "private_ip": "*********", "private_ip_list.#": "1", "private_ip_list.0": "*********", "private_ips.#": "1", "private_ips.0": "*********", "private_ips_count": "0", "security_groups.#": "1", "security_groups.0": "sg-082d3fc2b26b1004e", "source_dest_check": "true", "subnet_id": "subnet-0c6066202240c38be", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-096d098e8a09d429b": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-096d098e8a09d429b", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-096d098e8a09d429b", "attachment.#": "1", "attachment.0.attachment_id": "ela-attach-0c6c2851c8eab4118", "attachment.0.device_index": "1", "attachment.0.instance": "", "description": "VPC Endpoint Interface vpce-04c5af7d63bb323fb", "id": "eni-096d098e8a09d429b", "interface_type": "vpc_endpoint", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "02:ff:ed:7f:0c:91", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-100-15-0-10.ap-southeast-1.compute.internal", "private_ip": "***********", "private_ip_list.#": "1", "private_ip_list.0": "***********", "private_ips.#": "1", "private_ips.0": "***********", "private_ips_count": "0", "security_groups.#": "1", "security_groups.0": "sg-00df94fe66c7464f5", "source_dest_check": "true", "subnet_id": "subnet-07ab4b059d69e4044", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-09dec8943b53ed058": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-09dec8943b53ed058", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-09dec8943b53ed058", "attachment.#": "1", "attachment.0.attachment_id": "ela-attach-01e64b17fd2bc55c6", "attachment.0.device_index": "1", "attachment.0.instance": "", "description": "ELB net/infovault-nlb/aa06b8f0bdcc828a", "id": "eni-09dec8943b53ed058", "interface_type": "network_load_balancer", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "02:4f:e3:c0:96:cf", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-100-67-3-14.ap-southeast-1.compute.internal", "private_ip": "***********", "private_ip_list.#": "1", "private_ip_list.0": "***********", "private_ips.#": "1", "private_ips.0": "***********", "private_ips_count": "0", "security_groups.#": "0", "source_dest_check": "false", "subnet_id": "subnet-0d4f42cf30b20c42d", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-09e6836ed6735715e": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-09e6836ed6735715e", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-09e6836ed6735715e", "attachment.#": "1", "attachment.0.attachment_id": "ela-attach-0d5d27d5542bd3ed3", "attachment.0.device_index": "1", "attachment.0.instance": "", "description": "VPC Endpoint Interface vpce-000d3536c71bcfc70", "id": "eni-09e6836ed6735715e", "interface_type": "vpc_endpoint", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "02:7a:a9:03:a2:0b", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-100-14-0-6.ap-southeast-1.compute.internal", "private_ip": "**********", "private_ip_list.#": "1", "private_ip_list.0": "**********", "private_ips.#": "1", "private_ips.0": "**********", "private_ips_count": "0", "security_groups.#": "1", "security_groups.0": "sg-0870c466d0db940c4", "source_dest_check": "true", "subnet_id": "subnet-0dacd0ef604204014", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-0a08f25491f59d58e": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-0a08f25491f59d58e", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-0a08f25491f59d58e", "attachment.#": "1", "attachment.0.attachment_id": "ela-attach-04b6aae9bb1a6af52", "attachment.0.device_index": "1", "attachment.0.instance": "", "description": "VPC Endpoint Interface vpce-08c51d0d6582b0aa6", "id": "eni-0a08f25491f59d58e", "interface_type": "vpc_endpoint", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "02:a6:f8:59:a9:3f", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-10-66-0-42.ap-southeast-1.compute.internal", "private_ip": "*********2", "private_ip_list.#": "1", "private_ip_list.0": "*********2", "private_ips.#": "1", "private_ips.0": "*********2", "private_ips_count": "0", "security_groups.#": "1", "security_groups.0": "sg-066975c77a66a64d9", "source_dest_check": "true", "subnet_id": "subnet-0a1802eb99e96cbed", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-0a181a6d7acba6b00": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-0a181a6d7acba6b00", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-0a181a6d7acba6b00", "attachment.#": "1", "attachment.0.attachment_id": "ela-attach-02b58355251fa7c4e", "attachment.0.device_index": "1", "attachment.0.instance": "", "description": "VPC Endpoint Interface vpce-04d5a7ce83abf07b5", "id": "eni-0a181a6d7acba6b00", "interface_type": "vpc_endpoint", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "06:5a:eb:27:02:c7", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-100-15-0-43.ap-southeast-1.compute.internal", "private_ip": "***********", "private_ip_list.#": "1", "private_ip_list.0": "***********", "private_ips.#": "1", "private_ips.0": "***********", "private_ips_count": "0", "security_groups.#": "1", "security_groups.0": "sg-0d28b5ada1bb60c9a", "source_dest_check": "true", "subnet_id": "subnet-04910ced717a5584a", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-0a4a1d4b782759c8d": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-0a4a1d4b782759c8d", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-0a4a1d4b782759c8d", "attachment.#": "1", "attachment.0.attachment_id": "ela-attach-062ad56ddf4da43af", "attachment.0.device_index": "1", "attachment.0.instance": "", "description": "VPC Endpoint Interface vpce-0c5ff88c30a3e428d", "id": "eni-0a4a1d4b782759c8d", "interface_type": "vpc_endpoint", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "02:c2:ba:4a:c4:6d", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-100-15-0-13.ap-southeast-1.compute.internal", "private_ip": "***********", "private_ip_list.#": "1", "private_ip_list.0": "***********", "private_ips.#": "1", "private_ips.0": "***********", "private_ips_count": "0", "security_groups.#": "1", "security_groups.0": "sg-065d1e24d43126489", "source_dest_check": "true", "subnet_id": "subnet-07ab4b059d69e4044", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-0a5fb5d8d833ef844": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-0a5fb5d8d833ef844", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-0a5fb5d8d833ef844", "attachment.#": "1", "attachment.0.attachment_id": "eni-attach-0c645a67803b1dd70", "attachment.0.device_index": "1", "attachment.0.instance": "", "description": "Amazon EKS infovault-dev-eks-cluster-v130", "id": "eni-0a5fb5d8d833ef844", "interface_type": "interface", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "06:c2:10:d8:0a:fd", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-100-67-2-130.ap-southeast-1.compute.internal", "private_ip": "************", "private_ip_list.#": "1", "private_ip_list.0": "************", "private_ips.#": "1", "private_ips.0": "************", "private_ips_count": "0", "security_groups.#": "2", "security_groups.0": "sg-0353836b1b373e27d", "security_groups.1": "sg-0950397297ecb203f", "source_dest_check": "true", "subnet_id": "subnet-015e4b2acb87091d9", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-0b0c10f4ff924ca83": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-0b0c10f4ff924ca83", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-0b0c10f4ff924ca83", "attachment.#": "1", "attachment.0.attachment_id": "ela-attach-0aea088a66fa658c0", "attachment.0.device_index": "1", "attachment.0.instance": "", "description": "VPC Endpoint Interface vpce-0ce3dc776f7700c04", "id": "eni-0b0c10f4ff924ca83", "interface_type": "vpc_endpoint", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "02:7a:1e:34:c6:9f", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-100-15-0-55.ap-southeast-1.compute.internal", "private_ip": "**********5", "private_ip_list.#": "1", "private_ip_list.0": "**********5", "private_ips.#": "1", "private_ips.0": "**********5", "private_ips_count": "0", "security_groups.#": "1", "security_groups.0": "sg-0d28b5ada1bb60c9a", "source_dest_check": "true", "subnet_id": "subnet-0ebbc016c2fc6b3db", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-0bb4d63b69f41cce3": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-0bb4d63b69f41cce3", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-0bb4d63b69f41cce3", "attachment.#": "1", "attachment.0.attachment_id": "eni-attach-058e540968e68884d", "attachment.0.device_index": "0", "attachment.0.instance": "i-03b6f949999d93642", "description": "", "id": "eni-0bb4d63b69f41cce3", "interface_type": "interface", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "02:e0:af:17:b6:f9", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-100-67-1-10.ap-southeast-1.compute.internal", "private_ip": "***********", "private_ip_list.#": "1", "private_ip_list.0": "***********", "private_ips.#": "1", "private_ips.0": "***********", "private_ips_count": "0", "security_groups.#": "1", "security_groups.0": "sg-0c081d79227db7d86", "source_dest_check": "true", "subnet_id": "subnet-023cafbedbb31d13e", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-0bfc335183ba65d53": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-0bfc335183ba65d53", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-0bfc335183ba65d53", "attachment.#": "1", "attachment.0.attachment_id": "eni-attach-0851b0522b412cc7e", "attachment.0.device_index": "1", "attachment.0.instance": "i-03ddfe5f32292e59d", "description": "aws-K8S-i-03ddfe5f32292e59d", "id": "eni-0bfc335183ba65d53", "interface_type": "interface", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "06:b6:ae:fb:5d:29", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-100-67-2-69.ap-southeast-1.compute.internal", "private_ip": "***********", "private_ip_list.#": "30", "private_ip_list.0": "***********", "private_ip_list.1": "***********7", "private_ip_list.10": "***********", "private_ip_list.11": "***********", "private_ip_list.12": "***********9", "private_ip_list.13": "***********", "private_ip_list.14": "***********1", "private_ip_list.15": "***********7", "private_ip_list.16": "************", "private_ip_list.17": "************", "private_ip_list.18": "************", "private_ip_list.19": "************", "private_ip_list.2": "***********", "private_ip_list.20": "***********9", "private_ip_list.21": "************", "private_ip_list.22": "***********", "private_ip_list.23": "************", "private_ip_list.24": "************", "private_ip_list.25": "************", "private_ip_list.26": "************", "private_ip_list.27": "************", "private_ip_list.28": "************", "private_ip_list.29": "************", "private_ip_list.3": "************", "private_ip_list.4": "***********", "private_ip_list.5": "************", "private_ip_list.6": "***********", "private_ip_list.7": "************", "private_ip_list.8": "************", "private_ip_list.9": "************", "private_ips.#": "30", "private_ips.0": "***********1", "private_ips.1": "***********7", "private_ips.10": "************", "private_ips.11": "************", "private_ips.12": "************", "private_ips.13": "***********", "private_ips.14": "************", "private_ips.15": "***********7", "private_ips.16": "************", "private_ips.17": "************", "private_ips.18": "************", "private_ips.19": "************", "private_ips.2": "***********9", "private_ips.20": "************", "private_ips.21": "************", "private_ips.22": "************", "private_ips.23": "************", "private_ips.24": "***********", "private_ips.25": "***********", "private_ips.26": "***********", "private_ips.27": "***********", "private_ips.28": "***********", "private_ips.29": "***********", "private_ips.3": "***********", "private_ips.4": "************", "private_ips.5": "************", "private_ips.6": "************", "private_ips.7": "************", "private_ips.8": "***********9", "private_ips.9": "************", "private_ips_count": "29", "security_groups.#": "1", "security_groups.0": "sg-0353836b1b373e27d", "source_dest_check": "true", "subnet_id": "subnet-015e4b2acb87091d9", "tags.%": "3", "tags.cluster.k8s.amazonaws.com/name": "infovault-dev-eks-cluster-v130", "tags.node.k8s.amazonaws.com/createdAt": "2025-05-21T10:24:10Z", "tags.node.k8s.amazonaws.com/instance_id": "i-03ddfe5f32292e59d", "tags_all.%": "3", "tags_all.cluster.k8s.amazonaws.com/name": "infovault-dev-eks-cluster-v130", "tags_all.node.k8s.amazonaws.com/createdAt": "2025-05-21T10:24:10Z", "tags_all.node.k8s.amazonaws.com/instance_id": "i-03ddfe5f32292e59d"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-0c3359775b007e6ed": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-0c3359775b007e6ed", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-0c3359775b007e6ed", "attachment.#": "1", "attachment.0.attachment_id": "eni-attach-0a58bd54cd0430dcc", "attachment.0.device_index": "0", "attachment.0.instance": "i-0edc7073f7666d047", "description": "", "id": "eni-0c3359775b007e6ed", "interface_type": "interface", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "02:b0:02:32:f9:19", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-100-67-3-26.ap-southeast-1.compute.internal", "private_ip": "***********", "private_ip_list.#": "1", "private_ip_list.0": "***********", "private_ips.#": "1", "private_ips.0": "***********", "private_ips_count": "0", "security_groups.#": "1", "security_groups.0": "sg-043af8ab2fe4ec50f", "source_dest_check": "true", "subnet_id": "subnet-0d4f42cf30b20c42d", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-0c4e492f96b742a6f": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-0c4e492f96b742a6f", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-0c4e492f96b742a6f", "attachment.#": "1", "attachment.0.attachment_id": "ela-attach-0acd931ee51d1fa1f", "attachment.0.device_index": "1", "attachment.0.instance": "", "description": "VPC Endpoint Interface vpce-0c7798e909bd72ff8", "id": "eni-0c4e492f96b742a6f", "interface_type": "vpc_endpoint", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "02:e9:76:f0:bf:59", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-100-67-1-35.ap-southeast-1.compute.internal", "private_ip": "***********", "private_ip_list.#": "1", "private_ip_list.0": "***********", "private_ips.#": "1", "private_ips.0": "***********", "private_ips_count": "0", "security_groups.#": "1", "security_groups.0": "sg-05f09a0dc7ab8c53f", "source_dest_check": "true", "subnet_id": "subnet-023cafbedbb31d13e", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-0c7ac735edacfcf5e": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-0c7ac735edacfcf5e", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-0c7ac735edacfcf5e", "attachment.#": "1", "attachment.0.attachment_id": "ela-attach-0af406d7a78f8053d", "attachment.0.device_index": "1", "attachment.0.instance": "", "description": "ELB net/app-nlb-internal/e4f71be0ff87ebcc", "id": "eni-0c7ac735edacfcf5e", "interface_type": "network_load_balancer", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "06:5c:16:71:0f:77", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-100-67-3-89.ap-southeast-1.compute.internal", "private_ip": "***********", "private_ip_list.#": "1", "private_ip_list.0": "***********", "private_ips.#": "1", "private_ips.0": "***********", "private_ips_count": "0", "security_groups.#": "0", "source_dest_check": "false", "subnet_id": "subnet-0c775a4f57dfa0e61", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-0cbabfcd450ce5061": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-0cbabfcd450ce5061", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-0cbabfcd450ce5061", "attachment.#": "1", "attachment.0.attachment_id": "eni-attach-032bafef7ff498d47", "attachment.0.device_index": "0", "attachment.0.instance": "i-0b7a54f938f84affa", "description": "", "id": "eni-0cbabfcd450ce5061", "interface_type": "interface", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "02:f5:76:88:61:ef", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-100-67-3-20.ap-southeast-1.compute.internal", "private_ip": "***********", "private_ip_list.#": "1", "private_ip_list.0": "***********", "private_ips.#": "1", "private_ips.0": "***********", "private_ips_count": "0", "security_groups.#": "1", "security_groups.0": "sg-0d8a5fa845dca93c9", "source_dest_check": "true", "subnet_id": "subnet-0d4f42cf30b20c42d", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-0cd213280801297e2": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-0cd213280801297e2", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-0cd213280801297e2", "attachment.#": "1", "attachment.0.attachment_id": "ela-attach-0f3520ca9add3a35f", "attachment.0.device_index": "1", "attachment.0.instance": "", "description": "VPC Endpoint Interface vpce-0f629db35d2992350", "id": "eni-0cd213280801297e2", "interface_type": "vpc_endpoint", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "02:ee:92:ee:e4:87", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-100-14-0-14.ap-southeast-1.compute.internal", "private_ip": "***********", "private_ip_list.#": "1", "private_ip_list.0": "***********", "private_ips.#": "1", "private_ips.0": "***********", "private_ips_count": "0", "security_groups.#": "1", "security_groups.0": "sg-0330a127b43c54ab2", "source_dest_check": "true", "subnet_id": "subnet-0dacd0ef604204014", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-0cd3deb8661a82b94": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-0cd3deb8661a82b94", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-0cd3deb8661a82b94", "attachment.#": "1", "attachment.0.attachment_id": "eni-attach-0d7f2aeadddd76728", "attachment.0.device_index": "1", "attachment.0.instance": "", "description": "Amazon EKS infovault-dev-eks-cluster-v130", "id": "eni-0cd3deb8661a82b94", "interface_type": "interface", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "02:af:cc:15:7c:4f", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-100-67-1-54.ap-southeast-1.compute.internal", "private_ip": "***********", "private_ip_list.#": "1", "private_ip_list.0": "***********", "private_ips.#": "1", "private_ips.0": "***********", "private_ips_count": "0", "security_groups.#": "2", "security_groups.0": "sg-0353836b1b373e27d", "security_groups.1": "sg-0950397297ecb203f", "source_dest_check": "true", "subnet_id": "subnet-023cafbedbb31d13e", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-0d4bd2f33d18876ea": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-0d4bd2f33d18876ea", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-0d4bd2f33d18876ea", "attachment.#": "1", "attachment.0.attachment_id": "ela-attach-0b1fa83d1a1e667c4", "attachment.0.device_index": "1", "attachment.0.instance": "", "description": "ELB net/app-nlb-internal/e4f71be0ff87ebcc", "id": "eni-0d4bd2f33d18876ea", "interface_type": "network_load_balancer", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "02:a9:79:8f:35:a1", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-100-67-1-226.ap-southeast-1.compute.internal", "private_ip": "************", "private_ip_list.#": "1", "private_ip_list.0": "************", "private_ips.#": "1", "private_ips.0": "************", "private_ips_count": "0", "security_groups.#": "0", "source_dest_check": "false", "subnet_id": "subnet-023cafbedbb31d13e", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-0d6e441784201ed85": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-0d6e441784201ed85", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-0d6e441784201ed85", "attachment.#": "1", "attachment.0.attachment_id": "ela-attach-05855926c36591150", "attachment.0.device_index": "1", "attachment.0.instance": "", "description": "Interface for NAT Gateway nat-0faca993cb72fc7d6", "id": "eni-0d6e441784201ed85", "interface_type": "nat_gateway", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "02:eb:00:31:b3:7b", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-100-67-0-9.ap-southeast-1.compute.internal", "private_ip": "**********", "private_ip_list.#": "1", "private_ip_list.0": "**********", "private_ips.#": "1", "private_ips.0": "**********", "private_ips_count": "0", "security_groups.#": "0", "source_dest_check": "false", "subnet_id": "subnet-01e871c49dc4b9b1f", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-0de844e6b4a90ea21": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-0de844e6b4a90ea21", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-0de844e6b4a90ea21", "attachment.#": "1", "attachment.0.attachment_id": "eni-attach-067ae5af9b2e1beba", "attachment.0.device_index": "0", "attachment.0.instance": "i-09e9e4c01cabdc2ba", "description": "", "id": "eni-0de844e6b4a90ea21", "interface_type": "interface", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "02:f5:6c:d9:44:45", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-10-66-0-57.ap-southeast-1.compute.internal", "private_ip": "**********", "private_ip_list.#": "1", "private_ip_list.0": "**********", "private_ips.#": "1", "private_ips.0": "**********", "private_ips_count": "0", "security_groups.#": "1", "security_groups.0": "sg-0764227cd7e054e0a", "source_dest_check": "true", "subnet_id": "subnet-0a5d4aa5489b9ab3a", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-0e4892cdad21d3d27": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-0e4892cdad21d3d27", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-0e4892cdad21d3d27", "attachment.#": "1", "attachment.0.attachment_id": "ela-attach-0ca8010f3e7df4ee3", "attachment.0.device_index": "1", "attachment.0.instance": "", "description": "VPC Endpoint Interface vpce-025b48dac964f67b4", "id": "eni-0e4892cdad21d3d27", "interface_type": "vpc_endpoint", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "02:ea:7a:9a:b9:25", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-10-66-0-45.ap-southeast-1.compute.internal", "private_ip": "*********5", "private_ip_list.#": "1", "private_ip_list.0": "*********5", "private_ips.#": "1", "private_ips.0": "*********5", "private_ips_count": "0", "security_groups.#": "1", "security_groups.0": "sg-066975c77a66a64d9", "source_dest_check": "true", "subnet_id": "subnet-0a1802eb99e96cbed", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-0e961cb5275d963d7": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-0e961cb5275d963d7", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-0e961cb5275d963d7", "attachment.#": "1", "attachment.0.attachment_id": "ela-attach-0606befe0b6107bf4", "attachment.0.device_index": "1", "attachment.0.instance": "", "description": "VPC Endpoint Interface vpce-094e78f395ad5f6f7", "id": "eni-0e961cb5275d963d7", "interface_type": "vpc_endpoint", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "02:1b:04:9b:60:c9", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-10-66-0-40.ap-southeast-1.compute.internal", "private_ip": "*********0", "private_ip_list.#": "1", "private_ip_list.0": "*********0", "private_ips.#": "1", "private_ips.0": "*********0", "private_ips_count": "0", "security_groups.#": "1", "security_groups.0": "sg-066975c77a66a64d9", "source_dest_check": "true", "subnet_id": "subnet-0a1802eb99e96cbed", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-0edd3b7c8064ca22e": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-0edd3b7c8064ca22e", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-0edd3b7c8064ca22e", "attachment.#": "1", "attachment.0.attachment_id": "ela-attach-06b19de68ec1abc05", "attachment.0.device_index": "1", "attachment.0.instance": "", "description": "VPC Endpoint Interface vpce-0c7798e909bd72ff8", "id": "eni-0edd3b7c8064ca22e", "interface_type": "vpc_endpoint", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "06:fe:97:2b:9b:57", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-100-67-2-250.ap-southeast-1.compute.internal", "private_ip": "************", "private_ip_list.#": "1", "private_ip_list.0": "************", "private_ips.#": "1", "private_ips.0": "************", "private_ips_count": "0", "security_groups.#": "1", "security_groups.0": "sg-05f09a0dc7ab8c53f", "source_dest_check": "true", "subnet_id": "subnet-015e4b2acb87091d9", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-0f2f4d8868d50f09c": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-0f2f4d8868d50f09c", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-0f2f4d8868d50f09c", "attachment.#": "1", "attachment.0.attachment_id": "eni-attach-0ea9c81622201aae7", "attachment.0.device_index": "0", "attachment.0.instance": "i-026e961cb556a37aa", "description": "", "id": "eni-0f2f4d8868d50f09c", "interface_type": "interface", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "02:62:d6:64:b6:f1", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-10-66-0-25.ap-southeast-1.compute.internal", "private_ip": "**********", "private_ip_list.#": "1", "private_ip_list.0": "**********", "private_ips.#": "1", "private_ips.0": "**********", "private_ips_count": "0", "security_groups.#": "1", "security_groups.0": "sg-029c5f3f1ddf2855e", "source_dest_check": "true", "subnet_id": "subnet-0e2a201f2503f59ee", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-0f4f360a445263ef1": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-0f4f360a445263ef1", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-0f4f360a445263ef1", "attachment.#": "1", "attachment.0.attachment_id": "ela-attach-09df1ff1d81a4af90", "attachment.0.device_index": "1", "attachment.0.instance": "", "description": "VPC Endpoint Interface vpce-0ce3dc776f7700c04", "id": "eni-0f4f360a445263ef1", "interface_type": "vpc_endpoint", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "06:1f:f6:25:9f:0b", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-100-15-0-36.ap-southeast-1.compute.internal", "private_ip": "***********", "private_ip_list.#": "1", "private_ip_list.0": "***********", "private_ips.#": "1", "private_ips.0": "***********", "private_ips_count": "0", "security_groups.#": "1", "security_groups.0": "sg-0d28b5ada1bb60c9a", "source_dest_check": "true", "subnet_id": "subnet-04910ced717a5584a", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-0f51e5d7d6ccb7f1b": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-0f51e5d7d6ccb7f1b", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-0f51e5d7d6ccb7f1b", "attachment.#": "1", "attachment.0.attachment_id": "ela-attach-090e2d0516eef716b", "attachment.0.device_index": "1", "attachment.0.instance": "", "description": "VPC Endpoint Interface vpce-0ac5502093940b08f", "id": "eni-0f51e5d7d6ccb7f1b", "interface_type": "vpc_endpoint", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "02:46:a5:30:6e:c7", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-10-66-0-4.ap-southeast-1.compute.internal", "private_ip": "*********", "private_ip_list.#": "1", "private_ip_list.0": "*********", "private_ips.#": "1", "private_ips.0": "*********", "private_ips_count": "0", "security_groups.#": "1", "security_groups.0": "sg-082d3fc2b26b1004e", "source_dest_check": "true", "subnet_id": "subnet-0c6066202240c38be", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-0f5e018aa1e689f80": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-0f5e018aa1e689f80", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-0f5e018aa1e689f80", "attachment.#": "1", "attachment.0.attachment_id": "eni-attach-02090c77934673021", "attachment.0.device_index": "0", "attachment.0.instance": "i-041e4356012a67cb5", "description": "", "id": "eni-0f5e018aa1e689f80", "interface_type": "interface", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "02:1a:46:2b:23:d3", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-100-67-0-5.ap-southeast-1.compute.internal", "private_ip": "**********", "private_ip_list.#": "1", "private_ip_list.0": "**********", "private_ips.#": "1", "private_ips.0": "**********", "private_ips_count": "0", "security_groups.#": "1", "security_groups.0": "sg-00b66d34fb63637f3", "source_dest_check": "true", "subnet_id": "subnet-01e871c49dc4b9b1f", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-0f769cda654ffb0c2": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-0f769cda654ffb0c2", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-0f769cda654ffb0c2", "attachment.#": "1", "attachment.0.attachment_id": "eni-attach-0c1748589fbda7b84", "attachment.0.device_index": "0", "attachment.0.instance": "i-0e6aace81e99d8af9", "description": "", "id": "eni-0f769cda654ffb0c2", "interface_type": "interface", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "02:41:c2:59:bb:5f", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-100-14-0-4.ap-southeast-1.compute.internal", "private_ip": "**********", "private_ip_list.#": "1", "private_ip_list.0": "**********", "private_ips.#": "1", "private_ips.0": "**********", "private_ips_count": "0", "security_groups.#": "1", "security_groups.0": "sg-04080d467ff2ca6c4", "source_dest_check": "true", "subnet_id": "subnet-0dacd0ef604204014", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-0f8b1e2c7cde7b2d6": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-0f8b1e2c7cde7b2d6", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-0f8b1e2c7cde7b2d6", "attachment.#": "1", "attachment.0.attachment_id": "eni-attach-0b206bf09cccc3ce8", "attachment.0.device_index": "0", "attachment.0.instance": "i-047eb48a87cdabc96", "description": "", "id": "eni-0f8b1e2c7cde7b2d6", "interface_type": "interface", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "02:01:1a:12:ce:f7", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-100-67-1-179.ap-southeast-1.compute.internal", "private_ip": "************", "private_ip_list.#": "30", "private_ip_list.0": "************", "private_ip_list.1": "************", "private_ip_list.10": "***********", "private_ip_list.11": "************", "private_ip_list.12": "***********", "private_ip_list.13": "************", "private_ip_list.14": "***********", "private_ip_list.15": "***********", "private_ip_list.16": "************", "private_ip_list.17": "***********", "private_ip_list.18": "***********", "private_ip_list.19": "************", "private_ip_list.2": "***********", "private_ip_list.20": "***********5", "private_ip_list.21": "************", "private_ip_list.22": "***********3", "private_ip_list.23": "***********8", "private_ip_list.24": "************", "private_ip_list.25": "************", "private_ip_list.26": "************", "private_ip_list.27": "***********", "private_ip_list.28": "***********", "private_ip_list.29": "************", "private_ip_list.3": "**********5", "private_ip_list.4": "************", "private_ip_list.5": "**********", "private_ip_list.6": "**********9", "private_ip_list.7": "************", "private_ip_list.8": "***********", "private_ip_list.9": "***********", "private_ips.#": "30", "private_ips.0": "************", "private_ips.1": "************", "private_ips.10": "***********3", "private_ips.11": "***********5", "private_ips.12": "***********8", "private_ips.13": "************", "private_ips.14": "************", "private_ips.15": "************", "private_ips.16": "************", "private_ips.17": "************", "private_ips.18": "***********", "private_ips.19": "***********", "private_ips.2": "************", "private_ips.20": "***********", "private_ips.21": "***********", "private_ips.22": "***********", "private_ips.23": "**********", "private_ips.24": "**********5", "private_ips.25": "***********", "private_ips.26": "**********9", "private_ips.27": "***********", "private_ips.28": "***********", "private_ips.29": "***********", "private_ips.3": "************", "private_ips.4": "************", "private_ips.5": "***********", "private_ips.6": "************", "private_ips.7": "************", "private_ips.8": "************", "private_ips.9": "***********", "private_ips_count": "29", "security_groups.#": "1", "security_groups.0": "sg-0353836b1b373e27d", "source_dest_check": "true", "subnet_id": "subnet-023cafbedbb31d13e", "tags.%": "4", "tags.cluster.k8s.amazonaws.com/name": "infovault-dev-eks-cluster-v130", "tags.eks:cluster-name": "infovault-dev-eks-cluster-v130", "tags.eks:nodegroup-name": "v130-node-group-3", "tags.node.k8s.amazonaws.com/instance_id": "i-047eb48a87cdabc96", "tags_all.%": "4", "tags_all.cluster.k8s.amazonaws.com/name": "infovault-dev-eks-cluster-v130", "tags_all.eks:cluster-name": "infovault-dev-eks-cluster-v130", "tags_all.eks:nodegroup-name": "v130-node-group-3", "tags_all.node.k8s.amazonaws.com/instance_id": "i-047eb48a87cdabc96"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-0fc71be00817c5145": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-0fc71be00817c5145", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-0fc71be00817c5145", "attachment.#": "1", "attachment.0.attachment_id": "eni-attach-0fa28905f4565b67f", "attachment.0.device_index": "0", "attachment.0.instance": "i-063edc7bb33841b7c", "description": "", "id": "eni-0fc71be00817c5145", "interface_type": "interface", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "02:4f:e3:0f:50:91", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-100-67-3-10.ap-southeast-1.compute.internal", "private_ip": "***********", "private_ip_list.#": "1", "private_ip_list.0": "***********", "private_ips.#": "1", "private_ips.0": "***********", "private_ips_count": "0", "security_groups.#": "1", "security_groups.0": "sg-0950397297ecb203f", "source_dest_check": "true", "subnet_id": "subnet-0d4f42cf30b20c42d", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_interface.tfer--eni-0ffcf9a54002de3ca": {"type": "aws_network_interface", "depends_on": [], "primary": {"id": "eni-0ffcf9a54002de3ca", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-interface/eni-0ffcf9a54002de3ca", "attachment.#": "1", "attachment.0.attachment_id": "ela-attach-06bb5374933567a90", "attachment.0.device_index": "1", "attachment.0.instance": "", "description": "VPC Endpoint Interface vpce-00634b26f7e325402", "id": "eni-0ffcf9a54002de3ca", "interface_type": "vpc_endpoint", "ipv4_prefix_count": "0", "ipv4_prefixes.#": "0", "ipv6_address_count": "0", "ipv6_address_list.#": "0", "ipv6_addresses.#": "0", "ipv6_prefix_count": "0", "ipv6_prefixes.#": "0", "mac_address": "06:53:64:06:a1:21", "outpost_arn": "", "owner_id": "046276255144", "private_dns_name": "ip-100-67-2-79.ap-southeast-1.compute.internal", "private_ip": "***********", "private_ip_list.#": "1", "private_ip_list.0": "***********", "private_ips.#": "1", "private_ips.0": "***********", "private_ips_count": "0", "security_groups.#": "1", "security_groups.0": "sg-05f09a0dc7ab8c53f", "source_dest_check": "true", "subnet_id": "subnet-015e4b2acb87091d9", "tags.%": "0", "tags_all.%": "0"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}}, "depends_on": []}]}