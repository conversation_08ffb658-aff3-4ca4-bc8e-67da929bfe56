{"version": 3, "terraform_version": "0.12.31", "serial": 1, "lineage": "5615333f-e86f-46aa-4274-461b7bcec798", "modules": [{"path": ["root"], "outputs": {"aws_vpc_tfer--vpc-05e0e8104b3321c40_id": {"sensitive": false, "type": "string", "value": "vpc-05e0e8104b3321c40"}}, "resources": {"aws_vpc.tfer--vpc-05e0e8104b3321c40": {"type": "aws_vpc", "depends_on": [], "primary": {"id": "vpc-05e0e8104b3321c40", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:vpc/vpc-05e0e8104b3321c40", "assign_generated_ipv6_cidr_block": "false", "cidr_block": "*********/26", "default_network_acl_id": "acl-0cf3a7e6c0d8d70ff", "default_route_table_id": "rtb-08edcd5b7304aa2aa", "default_security_group_id": "sg-0a2302a73783eb575", "dhcp_options_id": "dopt-0651689ceffd5fc0c", "enable_dns_hostnames": "true", "enable_dns_support": "true", "enable_network_address_usage_metrics": "false", "id": "vpc-05e0e8104b3321c40", "instance_tenancy": "default", "ipv6_association_id": "", "ipv6_cidr_block": "", "ipv6_cidr_block_network_border_group": "", "ipv6_ipam_pool_id": "", "ipv6_netmask_length": "0", "main_route_table_id": "rtb-08edcd5b7304aa2aa", "owner_id": "046276255144", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "infovault-dev-gen-facing-compartment", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "infovault-dev-gen-facing-compartment", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault"}, "meta": {"schema_version": 1}, "tainted": false}, "deposed": [], "provider": "provider.aws"}}, "depends_on": []}]}