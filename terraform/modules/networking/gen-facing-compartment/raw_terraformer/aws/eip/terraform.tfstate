{"version": 3, "terraform_version": "0.12.31", "serial": 1, "lineage": "ab97a2bb-8a62-fc57-e576-774cad1ed52d", "modules": [{"path": ["root"], "outputs": {"aws_eip_tfer--eipalloc-01c7e7d9a87bf6629_id": {"sensitive": false, "type": "string", "value": "eipalloc-01c7e7d9a87bf6629"}, "aws_eip_tfer--eipalloc-05b293b1c50d5ff91_id": {"sensitive": false, "type": "string", "value": "eipalloc-05b293b1c50d5ff91"}, "aws_eip_tfer--eipalloc-073d1259e09e3694d_id": {"sensitive": false, "type": "string", "value": "eipalloc-073d1259e09e3694d"}}, "resources": {"aws_eip.tfer--eipalloc-01c7e7d9a87bf6629": {"type": "aws_eip", "depends_on": [], "primary": {"id": "eipalloc-01c7e7d9a87bf6629", "attributes": {"allocation_id": "eipalloc-01c7e7d9a87bf6629", "arn": "arn:aws:ec2:ap-southeast-1:046276255144:elastic-ip/eipalloc-01c7e7d9a87bf6629", "association_id": "", "carrier_ip": "", "customer_owned_ip": "", "customer_owned_ipv4_pool": "", "domain": "vpc", "id": "eipalloc-01c7e7d9a87bf6629", "instance": "", "network_border_group": "ap-southeast-1", "network_interface": "", "private_ip": "", "ptr_record": "", "public_dns": "ec2-18-139-140-50.ap-southeast-1.compute.amazonaws.com", "public_ip": "*************", "public_ipv4_pool": "amazon", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "infovault-dev-app-db-nat-eip", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "infovault-dev-app-db-nat-eip", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc": "true"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_eip.tfer--eipalloc-05b293b1c50d5ff91": {"type": "aws_eip", "depends_on": [], "primary": {"id": "eipalloc-05b293b1c50d5ff91", "attributes": {"allocation_id": "eipalloc-05b293b1c50d5ff91", "arn": "arn:aws:ec2:ap-southeast-1:046276255144:elastic-ip/eipalloc-05b293b1c50d5ff91", "association_id": "eipassoc-002f0d8b9d050a68f", "carrier_ip": "", "customer_owned_ip": "", "customer_owned_ipv4_pool": "", "domain": "vpc", "id": "eipalloc-05b293b1c50d5ff91", "instance": "", "network_border_group": "ap-southeast-1", "network_interface": "eni-0d6e441784201ed85", "private_dns": "ip-100-67-0-9.ap-southeast-1.compute.internal", "private_ip": "**********", "ptr_record": "", "public_dns": "ec2-54-251-106-53.ap-southeast-1.compute.amazonaws.com", "public_ip": "*************", "public_ipv4_pool": "amazon", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "infovault-dev-nat-eip", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "infovault-dev-nat-eip", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc": "true"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_eip.tfer--eipalloc-073d1259e09e3694d": {"type": "aws_eip", "depends_on": [], "primary": {"id": "eipalloc-073d1259e09e3694d", "attributes": {"allocation_id": "eipalloc-073d1259e09e3694d", "arn": "arn:aws:ec2:ap-southeast-1:046276255144:elastic-ip/eipalloc-073d1259e09e3694d", "association_id": "eipassoc-064917cfbdbb30519", "carrier_ip": "", "customer_owned_ip": "", "customer_owned_ipv4_pool": "", "domain": "vpc", "id": "eipalloc-073d1259e09e3694d", "instance": "", "network_border_group": "ap-southeast-1", "network_interface": "eni-066838eeb94b2abd3", "private_dns": "ip-10-66-0-14.ap-southeast-1.compute.internal", "private_ip": "**********", "ptr_record": "", "public_dns": "ec2-54-151-205-249.ap-southeast-1.compute.amazonaws.com", "public_ip": "**************", "public_ipv4_pool": "amazon", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "infovault-dev-gen-facing-compartment-nat-eip", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "infovault-dev-gen-facing-compartment-nat-eip", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc": "true"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}}, "depends_on": []}]}