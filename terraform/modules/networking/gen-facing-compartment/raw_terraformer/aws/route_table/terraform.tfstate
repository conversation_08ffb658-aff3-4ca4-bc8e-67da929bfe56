{"version": 3, "terraform_version": "0.12.31", "serial": 1, "lineage": "5301931e-4322-6392-3b91-2fd4e08a60e6", "modules": [{"path": ["root"], "outputs": {"aws_main_route_table_association_tfer--vpc-02282d8d4c9c7764c_id": {"sensitive": false, "type": "string", "value": "rtbassoc-0fd2a15650ac9cf21"}, "aws_main_route_table_association_tfer--vpc-037f619602fa293a1_id": {"sensitive": false, "type": "string", "value": "rtbassoc-0d7e3d04f1b37836f"}, "aws_main_route_table_association_tfer--vpc-05e0e8104b3321c40_id": {"sensitive": false, "type": "string", "value": "rtbassoc-02c4317fa98812ad3"}, "aws_main_route_table_association_tfer--vpc-05f1b20a6634c6a38_id": {"sensitive": false, "type": "string", "value": "rtbassoc-0e26e2746fb77028a"}, "aws_main_route_table_association_tfer--vpc-0ca0480cce82946b3_id": {"sensitive": false, "type": "string", "value": "rtbassoc-02236fedb936c9ce1"}, "aws_main_route_table_association_tfer--vpc-0e75036a2ad9e5b4f_id": {"sensitive": false, "type": "string", "value": "rtbassoc-02e107d32f210003a"}, "aws_route_table_association_tfer--subnet-015e4b2acb87091d9_id": {"sensitive": false, "type": "string", "value": "rtbassoc-06146946bb9e68346"}, "aws_route_table_association_tfer--subnet-01e871c49dc4b9b1f_id": {"sensitive": false, "type": "string", "value": "rtbassoc-0145386c6a5e15295"}, "aws_route_table_association_tfer--subnet-023cafbedbb31d13e_id": {"sensitive": false, "type": "string", "value": "rtbassoc-0a77663682b071234"}, "aws_route_table_association_tfer--subnet-043dd23a18b80a280_id": {"sensitive": false, "type": "string", "value": "rtbassoc-093f7871287b6842f"}, "aws_route_table_association_tfer--subnet-056cc33d6674e5027_id": {"sensitive": false, "type": "string", "value": "rtbassoc-016d734fcc59228fb"}, "aws_route_table_association_tfer--subnet-0669d84e56a7dc53e_id": {"sensitive": false, "type": "string", "value": "rtbassoc-06eb808fa3c17a31d"}, "aws_route_table_association_tfer--subnet-07ab4b059d69e4044_id": {"sensitive": false, "type": "string", "value": "rtbassoc-0f6a5133d0777ab3d"}, "aws_route_table_association_tfer--subnet-080d8fdb4e9add74c_id": {"sensitive": false, "type": "string", "value": "rtbassoc-020136bee2a4527ea"}, "aws_route_table_association_tfer--subnet-081b6eb841b4451b3_id": {"sensitive": false, "type": "string", "value": "rtbassoc-06a02157a37f09003"}, "aws_route_table_association_tfer--subnet-0a1802eb99e96cbed_id": {"sensitive": false, "type": "string", "value": "rtbassoc-0970518f864799a57"}, "aws_route_table_association_tfer--subnet-0a5d4aa5489b9ab3a_id": {"sensitive": false, "type": "string", "value": "rtbassoc-05c945c54de24a721"}, "aws_route_table_association_tfer--subnet-0aec855b39a7be02e_id": {"sensitive": false, "type": "string", "value": "rtbassoc-0b52b0666d1283362"}, "aws_route_table_association_tfer--subnet-0b9afa4ae103de5b5_id": {"sensitive": false, "type": "string", "value": "rtbassoc-051824847cfbe0a0f"}, "aws_route_table_association_tfer--subnet-0c6066202240c38be_id": {"sensitive": false, "type": "string", "value": "rtbassoc-0c71a767d2ff6b502"}, "aws_route_table_association_tfer--subnet-0c775a4f57dfa0e61_id": {"sensitive": false, "type": "string", "value": "rtbassoc-07e961bcbe1e529de"}, "aws_route_table_association_tfer--subnet-0d4f42cf30b20c42d_id": {"sensitive": false, "type": "string", "value": "rtbassoc-03e7c028d710cd01b"}, "aws_route_table_association_tfer--subnet-0dacd0ef604204014_id": {"sensitive": false, "type": "string", "value": "rtbassoc-09b91ec6adfeb06de"}, "aws_route_table_association_tfer--subnet-0e2a201f2503f59ee_id": {"sensitive": false, "type": "string", "value": "rtbassoc-0a472e0c79f833a27"}, "aws_route_table_tfer--rtb-00c4597c163a0ed96_id": {"sensitive": false, "type": "string", "value": "rtb-00c4597c163a0ed96"}, "aws_route_table_tfer--rtb-00c5fd2f2cd25c1f8_id": {"sensitive": false, "type": "string", "value": "rtb-00c5fd2f2cd25c1f8"}, "aws_route_table_tfer--rtb-015ae5889ca4aaadd_id": {"sensitive": false, "type": "string", "value": "rtb-015ae5889ca4aaadd"}, "aws_route_table_tfer--rtb-02a850249ccfbe80b_id": {"sensitive": false, "type": "string", "value": "rtb-02a850249ccfbe80b"}, "aws_route_table_tfer--rtb-02a9859d16aa6e04b_id": {"sensitive": false, "type": "string", "value": "rtb-02a9859d16aa6e04b"}, "aws_route_table_tfer--rtb-0355b1ea7a0ec320a_id": {"sensitive": false, "type": "string", "value": "rtb-0355b1ea7a0ec320a"}, "aws_route_table_tfer--rtb-038d861f618d80392_id": {"sensitive": false, "type": "string", "value": "rtb-038d861f618d80392"}, "aws_route_table_tfer--rtb-03b5487f3d5603599_id": {"sensitive": false, "type": "string", "value": "rtb-03b5487f3d5603599"}, "aws_route_table_tfer--rtb-0445ba29313210ac5_id": {"sensitive": false, "type": "string", "value": "rtb-0445ba29313210ac5"}, "aws_route_table_tfer--rtb-048f93d202df7cd40_id": {"sensitive": false, "type": "string", "value": "rtb-048f93d202df7cd40"}, "aws_route_table_tfer--rtb-04dea60d5aeca9def_id": {"sensitive": false, "type": "string", "value": "rtb-04dea60d5aeca9def"}, "aws_route_table_tfer--rtb-04e5bfdb40e860b95_id": {"sensitive": false, "type": "string", "value": "rtb-04e5bfdb40e860b95"}, "aws_route_table_tfer--rtb-05df1c4be4e435f04_id": {"sensitive": false, "type": "string", "value": "rtb-05df1c4be4e435f04"}, "aws_route_table_tfer--rtb-06ca622706fb0676d_id": {"sensitive": false, "type": "string", "value": "rtb-06ca622706fb0676d"}, "aws_route_table_tfer--rtb-0795b2e3d3e3f88ed_id": {"sensitive": false, "type": "string", "value": "rtb-0795b2e3d3e3f88ed"}, "aws_route_table_tfer--rtb-07eb7e9c7e982fdba_id": {"sensitive": false, "type": "string", "value": "rtb-07eb7e9c7e982fdba"}, "aws_route_table_tfer--rtb-089ed5e648707e2d9_id": {"sensitive": false, "type": "string", "value": "rtb-089ed5e648707e2d9"}, "aws_route_table_tfer--rtb-08edcd5b7304aa2aa_id": {"sensitive": false, "type": "string", "value": "rtb-08edcd5b7304aa2aa"}, "aws_route_table_tfer--rtb-098958cd03a46c219_id": {"sensitive": false, "type": "string", "value": "rtb-098958cd03a46c219"}, "aws_route_table_tfer--rtb-09a828af05f32b0cf_id": {"sensitive": false, "type": "string", "value": "rtb-09a828af05f32b0cf"}, "aws_route_table_tfer--rtb-09e7342b3a3bc5507_id": {"sensitive": false, "type": "string", "value": "rtb-09e7342b3a3bc5507"}, "aws_route_table_tfer--rtb-0a3ed8d3c391e4cca_id": {"sensitive": false, "type": "string", "value": "rtb-0a3ed8d3c391e4cca"}, "aws_route_table_tfer--rtb-0aa2547a225d317bd_id": {"sensitive": false, "type": "string", "value": "rtb-0aa2547a225d317bd"}, "aws_route_table_tfer--rtb-0c5895a403cd0d50e_id": {"sensitive": false, "type": "string", "value": "rtb-0c5895a403cd0d50e"}, "aws_route_table_tfer--rtb-0c7da122446383cc5_id": {"sensitive": false, "type": "string", "value": "rtb-0c7da122446383cc5"}, "aws_route_table_tfer--rtb-0cb7cabf4d81ee7bd_id": {"sensitive": false, "type": "string", "value": "rtb-0cb7cabf4d81ee7bd"}, "aws_route_table_tfer--rtb-0d508af52f5965f6a_id": {"sensitive": false, "type": "string", "value": "rtb-0d508af52f5965f6a"}, "aws_route_table_tfer--rtb-0de7189dd22690311_id": {"sensitive": false, "type": "string", "value": "rtb-0de7189dd22690311"}, "aws_route_table_tfer--rtb-0e47b2c134f9ec024_id": {"sensitive": false, "type": "string", "value": "rtb-0e47b2c134f9ec024"}, "aws_route_table_tfer--rtb-0eb045ebd5d6d7479_id": {"sensitive": false, "type": "string", "value": "rtb-0eb045ebd5d6d7479"}, "aws_route_table_tfer--rtb-0f04c8976ea037652_id": {"sensitive": false, "type": "string", "value": "rtb-0f04c8976ea037652"}, "aws_route_table_tfer--rtb-0f6307e8c214b6a71_id": {"sensitive": false, "type": "string", "value": "rtb-0f6307e8c214b6a71"}}, "resources": {"aws_main_route_table_association.tfer--vpc-02282d8d4c9c7764c": {"type": "aws_main_route_table_association", "depends_on": [], "primary": {"id": "rtbassoc-0fd2a15650ac9cf21", "attributes": {"id": "rtbassoc-0fd2a15650ac9cf21", "route_table_id": "rtb-04e5bfdb40e860b95", "vpc_id": "vpc-02282d8d4c9c7764c"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_main_route_table_association.tfer--vpc-037f619602fa293a1": {"type": "aws_main_route_table_association", "depends_on": [], "primary": {"id": "rtbassoc-0d7e3d04f1b37836f", "attributes": {"id": "rtbassoc-0d7e3d04f1b37836f", "route_table_id": "rtb-0de7189dd22690311", "vpc_id": "vpc-037f619602fa293a1"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_main_route_table_association.tfer--vpc-05e0e8104b3321c40": {"type": "aws_main_route_table_association", "depends_on": [], "primary": {"id": "rtbassoc-02c4317fa98812ad3", "attributes": {"id": "rtbassoc-02c4317fa98812ad3", "route_table_id": "rtb-08edcd5b7304aa2aa", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_main_route_table_association.tfer--vpc-05f1b20a6634c6a38": {"type": "aws_main_route_table_association", "depends_on": [], "primary": {"id": "rtbassoc-0e26e2746fb77028a", "attributes": {"id": "rtbassoc-0e26e2746fb77028a", "route_table_id": "rtb-02a850249ccfbe80b", "vpc_id": "vpc-05f1b20a6634c6a38"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_main_route_table_association.tfer--vpc-0ca0480cce82946b3": {"type": "aws_main_route_table_association", "depends_on": [], "primary": {"id": "rtbassoc-02236fedb936c9ce1", "attributes": {"id": "rtbassoc-02236fedb936c9ce1", "route_table_id": "rtb-00c5fd2f2cd25c1f8", "vpc_id": "vpc-0ca0480cce82946b3"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_main_route_table_association.tfer--vpc-0e75036a2ad9e5b4f": {"type": "aws_main_route_table_association", "depends_on": [], "primary": {"id": "rtbassoc-02e107d32f210003a", "attributes": {"id": "rtbassoc-02e107d32f210003a", "route_table_id": "rtb-0cb7cabf4d81ee7bd", "vpc_id": "vpc-0e75036a2ad9e5b4f"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_route_table.tfer--rtb-00c4597c163a0ed96": {"type": "aws_route_table", "depends_on": [], "primary": {"id": "rtb-00c4597c163a0ed96", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:route-table/rtb-00c4597c163a0ed96", "id": "rtb-00c4597c163a0ed96", "owner_id": "046276255144", "propagating_vgws.#": "0", "route.#": "3", "route.0.carrier_gateway_id": "", "route.0.cidr_block": "0.0.0.0/0", "route.0.core_network_arn": "", "route.0.destination_prefix_list_id": "", "route.0.egress_only_gateway_id": "", "route.0.gateway_id": "", "route.0.ipv6_cidr_block": "", "route.0.local_gateway_id": "", "route.0.nat_gateway_id": "nat-0faca993cb72fc7d6", "route.0.network_interface_id": "", "route.0.transit_gateway_id": "", "route.0.vpc_endpoint_id": "", "route.0.vpc_peering_connection_id": "", "route.1.carrier_gateway_id": "", "route.1.cidr_block": "**********/27", "route.1.core_network_arn": "", "route.1.destination_prefix_list_id": "", "route.1.egress_only_gateway_id": "", "route.1.gateway_id": "", "route.1.ipv6_cidr_block": "", "route.1.local_gateway_id": "", "route.1.nat_gateway_id": "", "route.1.network_interface_id": "", "route.1.transit_gateway_id": "", "route.1.vpc_endpoint_id": "", "route.1.vpc_peering_connection_id": "pcx-02dd2ea276cb24bb3", "route.2.carrier_gateway_id": "", "route.2.cidr_block": "**********/26", "route.2.core_network_arn": "", "route.2.destination_prefix_list_id": "", "route.2.egress_only_gateway_id": "", "route.2.gateway_id": "", "route.2.ipv6_cidr_block": "", "route.2.local_gateway_id": "", "route.2.nat_gateway_id": "", "route.2.network_interface_id": "", "route.2.transit_gateway_id": "", "route.2.vpc_endpoint_id": "", "route.2.vpc_peering_connection_id": "pcx-0f0db258d11626f4c", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "dev-net-rtb-priv-az1", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "dev-net-rtb-priv-az1", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_id": "vpc-037f619602fa293a1"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_route_table.tfer--rtb-00c5fd2f2cd25c1f8": {"type": "aws_route_table", "depends_on": [], "primary": {"id": "rtb-00c5fd2f2cd25c1f8", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:route-table/rtb-00c5fd2f2cd25c1f8", "id": "rtb-00c5fd2f2cd25c1f8", "owner_id": "046276255144", "propagating_vgws.#": "0", "route.#": "1", "route.0.carrier_gateway_id": "", "route.0.cidr_block": "**********/27", "route.0.core_network_arn": "", "route.0.destination_prefix_list_id": "", "route.0.egress_only_gateway_id": "", "route.0.gateway_id": "", "route.0.ipv6_cidr_block": "", "route.0.local_gateway_id": "", "route.0.nat_gateway_id": "", "route.0.network_interface_id": "", "route.0.transit_gateway_id": "", "route.0.vpc_endpoint_id": "", "route.0.vpc_peering_connection_id": "pcx-02dd2ea276cb24bb3", "tags.%": "0", "tags_all.%": "0", "vpc_id": "vpc-0ca0480cce82946b3"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_route_table.tfer--rtb-015ae5889ca4aaadd": {"type": "aws_route_table", "depends_on": [], "primary": {"id": "rtb-015ae5889ca4aaadd", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:route-table/rtb-015ae5889ca4aaadd", "id": "rtb-015ae5889ca4aaadd", "owner_id": "046276255144", "propagating_vgws.#": "0", "route.#": "3", "route.0.carrier_gateway_id": "", "route.0.cidr_block": "0.0.0.0/0", "route.0.core_network_arn": "", "route.0.destination_prefix_list_id": "", "route.0.egress_only_gateway_id": "", "route.0.gateway_id": "igw-05beaf9de3b1e3902", "route.0.ipv6_cidr_block": "", "route.0.local_gateway_id": "", "route.0.nat_gateway_id": "", "route.0.network_interface_id": "", "route.0.transit_gateway_id": "", "route.0.vpc_endpoint_id": "", "route.0.vpc_peering_connection_id": "", "route.1.carrier_gateway_id": "", "route.1.cidr_block": "**********/26", "route.1.core_network_arn": "", "route.1.destination_prefix_list_id": "", "route.1.egress_only_gateway_id": "", "route.1.gateway_id": "", "route.1.ipv6_cidr_block": "", "route.1.local_gateway_id": "", "route.1.nat_gateway_id": "", "route.1.network_interface_id": "", "route.1.transit_gateway_id": "", "route.1.vpc_endpoint_id": "", "route.1.vpc_peering_connection_id": "pcx-00306eabadaf4a547", "route.2.carrier_gateway_id": "", "route.2.cidr_block": "**********/22", "route.2.core_network_arn": "", "route.2.destination_prefix_list_id": "", "route.2.egress_only_gateway_id": "", "route.2.gateway_id": "", "route.2.ipv6_cidr_block": "", "route.2.local_gateway_id": "", "route.2.nat_gateway_id": "", "route.2.network_interface_id": "", "route.2.transit_gateway_id": "", "route.2.vpc_endpoint_id": "", "route.2.vpc_peering_connection_id": "pcx-07364bf66a2a53a7d", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "rtb-gen-ingress", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "rtb-gen-ingress", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_route_table.tfer--rtb-02a850249ccfbe80b": {"type": "aws_route_table", "depends_on": [], "primary": {"id": "rtb-02a850249ccfbe80b", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:route-table/rtb-02a850249ccfbe80b", "id": "rtb-02a850249ccfbe80b", "owner_id": "046276255144", "propagating_vgws.#": "0", "route.#": "3", "route.0.carrier_gateway_id": "", "route.0.cidr_block": "*********/26", "route.0.core_network_arn": "", "route.0.destination_prefix_list_id": "", "route.0.egress_only_gateway_id": "", "route.0.gateway_id": "", "route.0.ipv6_cidr_block": "", "route.0.local_gateway_id": "", "route.0.nat_gateway_id": "", "route.0.network_interface_id": "", "route.0.transit_gateway_id": "", "route.0.vpc_endpoint_id": "", "route.0.vpc_peering_connection_id": "pcx-00306eabadaf4a547", "route.1.carrier_gateway_id": "", "route.1.cidr_block": "**********/22", "route.1.core_network_arn": "", "route.1.destination_prefix_list_id": "", "route.1.egress_only_gateway_id": "", "route.1.gateway_id": "", "route.1.ipv6_cidr_block": "", "route.1.local_gateway_id": "", "route.1.nat_gateway_id": "", "route.1.network_interface_id": "", "route.1.transit_gateway_id": "", "route.1.vpc_endpoint_id": "", "route.1.vpc_peering_connection_id": "pcx-00306eabadaf4a547", "route.2.carrier_gateway_id": "", "route.2.cidr_block": "**********/27", "route.2.core_network_arn": "", "route.2.destination_prefix_list_id": "", "route.2.egress_only_gateway_id": "", "route.2.gateway_id": "", "route.2.ipv6_cidr_block": "", "route.2.local_gateway_id": "", "route.2.nat_gateway_id": "", "route.2.network_interface_id": "", "route.2.transit_gateway_id": "", "route.2.vpc_endpoint_id": "", "route.2.vpc_peering_connection_id": "pcx-0f0db258d11626f4c", "tags.%": "0", "tags_all.%": "0", "vpc_id": "vpc-05f1b20a6634c6a38"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_route_table.tfer--rtb-02a9859d16aa6e04b": {"type": "aws_route_table", "depends_on": [], "primary": {"id": "rtb-02a9859d16aa6e04b", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:route-table/rtb-02a9859d16aa6e04b", "id": "rtb-02a9859d16aa6e04b", "owner_id": "046276255144", "propagating_vgws.#": "0", "route.#": "0", "tags.%": "2", "tags.Name": "aws-controltower-PrivateSubnet1ARouteTable", "tags.Network": "Private", "tags_all.%": "2", "tags_all.Name": "aws-controltower-PrivateSubnet1ARouteTable", "tags_all.Network": "Private", "vpc_id": "vpc-02282d8d4c9c7764c"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_route_table.tfer--rtb-0355b1ea7a0ec320a": {"type": "aws_route_table", "depends_on": [], "primary": {"id": "rtb-0355b1ea7a0ec320a", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:route-table/rtb-0355b1ea7a0ec320a", "id": "rtb-0355b1ea7a0ec320a", "owner_id": "046276255144", "propagating_vgws.#": "0", "route.#": "2", "route.0.carrier_gateway_id": "", "route.0.cidr_block": "**********/27", "route.0.core_network_arn": "", "route.0.destination_prefix_list_id": "", "route.0.egress_only_gateway_id": "", "route.0.gateway_id": "", "route.0.ipv6_cidr_block": "", "route.0.local_gateway_id": "", "route.0.nat_gateway_id": "", "route.0.network_interface_id": "", "route.0.transit_gateway_id": "", "route.0.vpc_endpoint_id": "", "route.0.vpc_peering_connection_id": "pcx-02dd2ea276cb24bb3", "route.1.carrier_gateway_id": "", "route.1.cidr_block": "**********/26", "route.1.core_network_arn": "", "route.1.destination_prefix_list_id": "", "route.1.egress_only_gateway_id": "", "route.1.gateway_id": "", "route.1.ipv6_cidr_block": "", "route.1.local_gateway_id": "", "route.1.nat_gateway_id": "", "route.1.network_interface_id": "", "route.1.transit_gateway_id": "", "route.1.vpc_endpoint_id": "", "route.1.vpc_peering_connection_id": "pcx-0f0db258d11626f4c", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "dev-net-rtb-tgw", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "dev-net-rtb-tgw", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_id": "vpc-037f619602fa293a1"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_route_table.tfer--rtb-038d861f618d80392": {"type": "aws_route_table", "depends_on": [], "primary": {"id": "rtb-038d861f618d80392", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:route-table/rtb-038d861f618d80392", "id": "rtb-038d861f618d80392", "owner_id": "046276255144", "propagating_vgws.#": "0", "route.#": "2", "route.0.carrier_gateway_id": "", "route.0.cidr_block": "0.0.0.0/0", "route.0.core_network_arn": "", "route.0.destination_prefix_list_id": "", "route.0.egress_only_gateway_id": "", "route.0.gateway_id": "", "route.0.ipv6_cidr_block": "", "route.0.local_gateway_id": "", "route.0.nat_gateway_id": "nat-0431bfe8618055bd8", "route.0.network_interface_id": "", "route.0.transit_gateway_id": "", "route.0.vpc_endpoint_id": "", "route.0.vpc_peering_connection_id": "", "route.1.carrier_gateway_id": "", "route.1.cidr_block": "**********/26", "route.1.core_network_arn": "", "route.1.destination_prefix_list_id": "", "route.1.egress_only_gateway_id": "", "route.1.gateway_id": "", "route.1.ipv6_cidr_block": "", "route.1.local_gateway_id": "", "route.1.nat_gateway_id": "", "route.1.network_interface_id": "", "route.1.transit_gateway_id": "", "route.1.vpc_endpoint_id": "", "route.1.vpc_peering_connection_id": "pcx-00306eabadaf4a547", "tags.%": "5", "tags.AutoShutdown": "true", "tags.Environment": "dev", "tags.Name": "app-subnet-az2-rt", "tags.Owner": "<PERSON><PERSON>", "tags.Project": "InfoVault", "tags_all.%": "5", "tags_all.AutoShutdown": "true", "tags_all.Environment": "dev", "tags_all.Name": "app-subnet-az2-rt", "tags_all.Owner": "<PERSON><PERSON>", "tags_all.Project": "InfoVault", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_route_table.tfer--rtb-03b5487f3d5603599": {"type": "aws_route_table", "depends_on": [], "primary": {"id": "rtb-03b5487f3d5603599", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:route-table/rtb-03b5487f3d5603599", "id": "rtb-03b5487f3d5603599", "owner_id": "046276255144", "propagating_vgws.#": "0", "route.#": "3", "route.0.carrier_gateway_id": "", "route.0.cidr_block": "*********/26", "route.0.core_network_arn": "", "route.0.destination_prefix_list_id": "", "route.0.egress_only_gateway_id": "", "route.0.gateway_id": "", "route.0.ipv6_cidr_block": "", "route.0.local_gateway_id": "", "route.0.nat_gateway_id": "", "route.0.network_interface_id": "", "route.0.transit_gateway_id": "", "route.0.vpc_endpoint_id": "", "route.0.vpc_peering_connection_id": "pcx-00306eabadaf4a547", "route.1.carrier_gateway_id": "", "route.1.cidr_block": "**********/22", "route.1.core_network_arn": "", "route.1.destination_prefix_list_id": "", "route.1.egress_only_gateway_id": "", "route.1.gateway_id": "", "route.1.ipv6_cidr_block": "", "route.1.local_gateway_id": "", "route.1.nat_gateway_id": "", "route.1.network_interface_id": "", "route.1.transit_gateway_id": "", "route.1.vpc_endpoint_id": "", "route.1.vpc_peering_connection_id": "pcx-00306eabadaf4a547", "route.2.carrier_gateway_id": "", "route.2.cidr_block": "**********/27", "route.2.core_network_arn": "", "route.2.destination_prefix_list_id": "", "route.2.egress_only_gateway_id": "", "route.2.gateway_id": "", "route.2.ipv6_cidr_block": "", "route.2.local_gateway_id": "", "route.2.nat_gateway_id": "", "route.2.network_interface_id": "", "route.2.transit_gateway_id": "", "route.2.vpc_endpoint_id": "", "route.2.vpc_peering_connection_id": "pcx-0f0db258d11626f4c", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "dev-mgmt-rtb-tgw", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "dev-mgmt-rtb-tgw", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_id": "vpc-05f1b20a6634c6a38"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_route_table.tfer--rtb-0445ba29313210ac5": {"type": "aws_route_table", "depends_on": [], "primary": {"id": "rtb-0445ba29313210ac5", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:route-table/rtb-0445ba29313210ac5", "id": "rtb-0445ba29313210ac5", "owner_id": "046276255144", "propagating_vgws.#": "0", "route.#": "3", "route.0.carrier_gateway_id": "", "route.0.cidr_block": "0.0.0.0/0", "route.0.core_network_arn": "", "route.0.destination_prefix_list_id": "", "route.0.egress_only_gateway_id": "", "route.0.gateway_id": "igw-066231fa2c240e55d", "route.0.ipv6_cidr_block": "", "route.0.local_gateway_id": "", "route.0.nat_gateway_id": "", "route.0.network_interface_id": "", "route.0.transit_gateway_id": "", "route.0.vpc_endpoint_id": "", "route.0.vpc_peering_connection_id": "", "route.1.carrier_gateway_id": "", "route.1.cidr_block": "*********/26", "route.1.core_network_arn": "", "route.1.destination_prefix_list_id": "", "route.1.egress_only_gateway_id": "", "route.1.gateway_id": "", "route.1.ipv6_cidr_block": "", "route.1.local_gateway_id": "", "route.1.nat_gateway_id": "", "route.1.network_interface_id": "", "route.1.transit_gateway_id": "", "route.1.vpc_endpoint_id": "", "route.1.vpc_peering_connection_id": "pcx-00306eabadaf4a547", "route.2.carrier_gateway_id": "", "route.2.cidr_block": "**********/22", "route.2.core_network_arn": "", "route.2.destination_prefix_list_id": "", "route.2.egress_only_gateway_id": "", "route.2.gateway_id": "", "route.2.ipv6_cidr_block": "", "route.2.local_gateway_id": "", "route.2.nat_gateway_id": "", "route.2.network_interface_id": "", "route.2.transit_gateway_id": "", "route.2.vpc_endpoint_id": "", "route.2.vpc_peering_connection_id": "pcx-00306eabadaf4a547", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "infovault-dev-public-rt", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "infovault-dev-public-rt", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_id": "vpc-05f1b20a6634c6a38"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_route_table.tfer--rtb-048f93d202df7cd40": {"type": "aws_route_table", "depends_on": [], "primary": {"id": "rtb-048f93d202df7cd40", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:route-table/rtb-048f93d202df7cd40", "id": "rtb-048f93d202df7cd40", "owner_id": "046276255144", "propagating_vgws.#": "0", "route.#": "0", "tags.%": "2", "tags.Name": "aws-controltower-PrivateSubnet3ARouteTable", "tags.Network": "Private", "tags_all.%": "2", "tags_all.Name": "aws-controltower-PrivateSubnet3ARouteTable", "tags_all.Network": "Private", "vpc_id": "vpc-02282d8d4c9c7764c"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_route_table.tfer--rtb-04dea60d5aeca9def": {"type": "aws_route_table", "depends_on": [], "primary": {"id": "rtb-04dea60d5aeca9def", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:route-table/rtb-04dea60d5aeca9def", "id": "rtb-04dea60d5aeca9def", "owner_id": "046276255144", "propagating_vgws.#": "0", "route.#": "3", "route.0.carrier_gateway_id": "", "route.0.cidr_block": "0.0.0.0/0", "route.0.core_network_arn": "", "route.0.destination_prefix_list_id": "", "route.0.egress_only_gateway_id": "", "route.0.gateway_id": "igw-05beaf9de3b1e3902", "route.0.ipv6_cidr_block": "", "route.0.local_gateway_id": "", "route.0.nat_gateway_id": "", "route.0.network_interface_id": "", "route.0.transit_gateway_id": "", "route.0.vpc_endpoint_id": "", "route.0.vpc_peering_connection_id": "", "route.1.carrier_gateway_id": "", "route.1.cidr_block": "**********/26", "route.1.core_network_arn": "", "route.1.destination_prefix_list_id": "", "route.1.egress_only_gateway_id": "", "route.1.gateway_id": "", "route.1.ipv6_cidr_block": "", "route.1.local_gateway_id": "", "route.1.nat_gateway_id": "", "route.1.network_interface_id": "", "route.1.transit_gateway_id": "", "route.1.vpc_endpoint_id": "", "route.1.vpc_peering_connection_id": "pcx-00306eabadaf4a547", "route.2.carrier_gateway_id": "", "route.2.cidr_block": "**********/22", "route.2.core_network_arn": "", "route.2.destination_prefix_list_id": "", "route.2.egress_only_gateway_id": "", "route.2.gateway_id": "", "route.2.ipv6_cidr_block": "", "route.2.local_gateway_id": "", "route.2.nat_gateway_id": "", "route.2.network_interface_id": "", "route.2.transit_gateway_id": "", "route.2.vpc_endpoint_id": "", "route.2.vpc_peering_connection_id": "pcx-07364bf66a2a53a7d", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "infovault-dev-public-rt", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "infovault-dev-public-rt", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_route_table.tfer--rtb-04e5bfdb40e860b95": {"type": "aws_route_table", "depends_on": [], "primary": {"id": "rtb-04e5bfdb40e860b95", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:route-table/rtb-04e5bfdb40e860b95", "id": "rtb-04e5bfdb40e860b95", "owner_id": "046276255144", "propagating_vgws.#": "0", "route.#": "0", "tags.%": "0", "tags_all.%": "0", "vpc_id": "vpc-02282d8d4c9c7764c"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_route_table.tfer--rtb-05df1c4be4e435f04": {"type": "aws_route_table", "depends_on": [], "primary": {"id": "rtb-05df1c4be4e435f04", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:route-table/rtb-05df1c4be4e435f04", "id": "rtb-05df1c4be4e435f04", "owner_id": "046276255144", "propagating_vgws.#": "0", "route.#": "3", "route.0.carrier_gateway_id": "", "route.0.cidr_block": "*********/26", "route.0.core_network_arn": "", "route.0.destination_prefix_list_id": "", "route.0.egress_only_gateway_id": "", "route.0.gateway_id": "", "route.0.ipv6_cidr_block": "", "route.0.local_gateway_id": "", "route.0.nat_gateway_id": "", "route.0.network_interface_id": "", "route.0.transit_gateway_id": "", "route.0.vpc_endpoint_id": "", "route.0.vpc_peering_connection_id": "pcx-00306eabadaf4a547", "route.1.carrier_gateway_id": "", "route.1.cidr_block": "**********/22", "route.1.core_network_arn": "", "route.1.destination_prefix_list_id": "", "route.1.egress_only_gateway_id": "", "route.1.gateway_id": "", "route.1.ipv6_cidr_block": "", "route.1.local_gateway_id": "", "route.1.nat_gateway_id": "", "route.1.network_interface_id": "", "route.1.transit_gateway_id": "", "route.1.vpc_endpoint_id": "", "route.1.vpc_peering_connection_id": "pcx-00306eabadaf4a547", "route.2.carrier_gateway_id": "", "route.2.cidr_block": "**********/27", "route.2.core_network_arn": "", "route.2.destination_prefix_list_id": "", "route.2.egress_only_gateway_id": "", "route.2.gateway_id": "", "route.2.ipv6_cidr_block": "", "route.2.local_gateway_id": "", "route.2.nat_gateway_id": "", "route.2.network_interface_id": "", "route.2.transit_gateway_id": "", "route.2.vpc_endpoint_id": "", "route.2.vpc_peering_connection_id": "pcx-0f0db258d11626f4c", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "rtb-mgmt-main", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "rtb-mgmt-main", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_id": "vpc-05f1b20a6634c6a38"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_route_table.tfer--rtb-06ca622706fb0676d": {"type": "aws_route_table", "depends_on": [], "primary": {"id": "rtb-06ca622706fb0676d", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:route-table/rtb-06ca622706fb0676d", "id": "rtb-06ca622706fb0676d", "owner_id": "046276255144", "propagating_vgws.#": "0", "route.#": "2", "route.0.carrier_gateway_id": "", "route.0.cidr_block": "0.0.0.0/0", "route.0.core_network_arn": "", "route.0.destination_prefix_list_id": "", "route.0.egress_only_gateway_id": "", "route.0.gateway_id": "", "route.0.ipv6_cidr_block": "", "route.0.local_gateway_id": "", "route.0.nat_gateway_id": "nat-0431bfe8618055bd8", "route.0.network_interface_id": "", "route.0.transit_gateway_id": "", "route.0.vpc_endpoint_id": "", "route.0.vpc_peering_connection_id": "", "route.1.carrier_gateway_id": "", "route.1.cidr_block": "**********/26", "route.1.core_network_arn": "", "route.1.destination_prefix_list_id": "", "route.1.egress_only_gateway_id": "", "route.1.gateway_id": "", "route.1.ipv6_cidr_block": "", "route.1.local_gateway_id": "", "route.1.nat_gateway_id": "", "route.1.network_interface_id": "", "route.1.transit_gateway_id": "", "route.1.vpc_endpoint_id": "", "route.1.vpc_peering_connection_id": "pcx-00306eabadaf4a547", "tags.%": "5", "tags.AutoShutdown": "true", "tags.Environment": "dev", "tags.Name": "db-subnet-az2-rt", "tags.Owner": "<PERSON><PERSON>", "tags.Project": "InfoVault", "tags_all.%": "5", "tags_all.AutoShutdown": "true", "tags_all.Environment": "dev", "tags_all.Name": "db-subnet-az2-rt", "tags_all.Owner": "<PERSON><PERSON>", "tags_all.Project": "InfoVault", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_route_table.tfer--rtb-0795b2e3d3e3f88ed": {"type": "aws_route_table", "depends_on": [], "primary": {"id": "rtb-0795b2e3d3e3f88ed", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:route-table/rtb-0795b2e3d3e3f88ed", "id": "rtb-0795b2e3d3e3f88ed", "owner_id": "046276255144", "propagating_vgws.#": "0", "route.#": "2", "route.0.carrier_gateway_id": "", "route.0.cidr_block": "0.0.0.0/0", "route.0.core_network_arn": "", "route.0.destination_prefix_list_id": "", "route.0.egress_only_gateway_id": "", "route.0.gateway_id": "", "route.0.ipv6_cidr_block": "", "route.0.local_gateway_id": "", "route.0.nat_gateway_id": "nat-0431bfe8618055bd8", "route.0.network_interface_id": "", "route.0.transit_gateway_id": "", "route.0.vpc_endpoint_id": "", "route.0.vpc_peering_connection_id": "", "route.1.carrier_gateway_id": "", "route.1.cidr_block": "**********/26", "route.1.core_network_arn": "", "route.1.destination_prefix_list_id": "", "route.1.egress_only_gateway_id": "", "route.1.gateway_id": "", "route.1.ipv6_cidr_block": "", "route.1.local_gateway_id": "", "route.1.nat_gateway_id": "", "route.1.network_interface_id": "", "route.1.transit_gateway_id": "", "route.1.vpc_endpoint_id": "", "route.1.vpc_peering_connection_id": "pcx-00306eabadaf4a547", "tags.%": "5", "tags.AutoShutdown": "true", "tags.Environment": "dev", "tags.Name": "app-subnet-az1-rt", "tags.Owner": "<PERSON><PERSON>", "tags.Project": "InfoVault", "tags_all.%": "5", "tags_all.AutoShutdown": "true", "tags_all.Environment": "dev", "tags_all.Name": "app-subnet-az1-rt", "tags_all.Owner": "<PERSON><PERSON>", "tags_all.Project": "InfoVault", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_route_table.tfer--rtb-07eb7e9c7e982fdba": {"type": "aws_route_table", "depends_on": [], "primary": {"id": "rtb-07eb7e9c7e982fdba", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:route-table/rtb-07eb7e9c7e982fdba", "id": "rtb-07eb7e9c7e982fdba", "owner_id": "046276255144", "propagating_vgws.#": "0", "route.#": "1", "route.0.carrier_gateway_id": "", "route.0.cidr_block": "0.0.0.0/0", "route.0.core_network_arn": "", "route.0.destination_prefix_list_id": "", "route.0.egress_only_gateway_id": "", "route.0.gateway_id": "igw-00a312b81674ba618", "route.0.ipv6_cidr_block": "", "route.0.local_gateway_id": "", "route.0.nat_gateway_id": "", "route.0.network_interface_id": "", "route.0.transit_gateway_id": "", "route.0.vpc_endpoint_id": "", "route.0.vpc_peering_connection_id": "", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "dev-net-rtb-fw-az1", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "dev-net-rtb-fw-az1", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_id": "vpc-037f619602fa293a1"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_route_table.tfer--rtb-089ed5e648707e2d9": {"type": "aws_route_table", "depends_on": [], "primary": {"id": "rtb-089ed5e648707e2d9", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:route-table/rtb-089ed5e648707e2d9", "id": "rtb-089ed5e648707e2d9", "owner_id": "046276255144", "propagating_vgws.#": "0", "route.#": "1", "route.0.carrier_gateway_id": "", "route.0.cidr_block": "0.0.0.0/0", "route.0.core_network_arn": "", "route.0.destination_prefix_list_id": "", "route.0.egress_only_gateway_id": "", "route.0.gateway_id": "igw-0b197ebf6057fbe90", "route.0.ipv6_cidr_block": "", "route.0.local_gateway_id": "", "route.0.nat_gateway_id": "", "route.0.network_interface_id": "", "route.0.transit_gateway_id": "", "route.0.vpc_endpoint_id": "", "route.0.vpc_peering_connection_id": "", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "infovault-dev-public-rt", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "infovault-dev-public-rt", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_id": "vpc-0e75036a2ad9e5b4f"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_route_table.tfer--rtb-08edcd5b7304aa2aa": {"type": "aws_route_table", "depends_on": [], "primary": {"id": "rtb-08edcd5b7304aa2aa", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:route-table/rtb-08edcd5b7304aa2aa", "id": "rtb-08edcd5b7304aa2aa", "owner_id": "046276255144", "propagating_vgws.#": "0", "route.#": "2", "route.0.carrier_gateway_id": "", "route.0.cidr_block": "**********/26", "route.0.core_network_arn": "", "route.0.destination_prefix_list_id": "", "route.0.egress_only_gateway_id": "", "route.0.gateway_id": "", "route.0.ipv6_cidr_block": "", "route.0.local_gateway_id": "", "route.0.nat_gateway_id": "", "route.0.network_interface_id": "", "route.0.transit_gateway_id": "", "route.0.vpc_endpoint_id": "", "route.0.vpc_peering_connection_id": "pcx-00306eabadaf4a547", "route.1.carrier_gateway_id": "", "route.1.cidr_block": "**********/22", "route.1.core_network_arn": "", "route.1.destination_prefix_list_id": "", "route.1.egress_only_gateway_id": "", "route.1.gateway_id": "", "route.1.ipv6_cidr_block": "", "route.1.local_gateway_id": "", "route.1.nat_gateway_id": "", "route.1.network_interface_id": "", "route.1.transit_gateway_id": "", "route.1.vpc_endpoint_id": "", "route.1.vpc_peering_connection_id": "pcx-07364bf66a2a53a7d", "tags.%": "0", "tags_all.%": "0", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_route_table.tfer--rtb-098958cd03a46c219": {"type": "aws_route_table", "depends_on": [], "primary": {"id": "rtb-098958cd03a46c219", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:route-table/rtb-098958cd03a46c219", "id": "rtb-098958cd03a46c219", "owner_id": "046276255144", "propagating_vgws.#": "0", "route.#": "2", "route.0.carrier_gateway_id": "", "route.0.cidr_block": "0.0.0.0/0", "route.0.core_network_arn": "", "route.0.destination_prefix_list_id": "", "route.0.egress_only_gateway_id": "", "route.0.gateway_id": "", "route.0.ipv6_cidr_block": "", "route.0.local_gateway_id": "", "route.0.nat_gateway_id": "nat-0431bfe8618055bd8", "route.0.network_interface_id": "", "route.0.transit_gateway_id": "", "route.0.vpc_endpoint_id": "", "route.0.vpc_peering_connection_id": "", "route.1.carrier_gateway_id": "", "route.1.cidr_block": "**********/26", "route.1.core_network_arn": "", "route.1.destination_prefix_list_id": "", "route.1.egress_only_gateway_id": "", "route.1.gateway_id": "", "route.1.ipv6_cidr_block": "", "route.1.local_gateway_id": "", "route.1.nat_gateway_id": "", "route.1.network_interface_id": "", "route.1.transit_gateway_id": "", "route.1.vpc_endpoint_id": "", "route.1.vpc_peering_connection_id": "pcx-00306eabadaf4a547", "tags.%": "5", "tags.AutoShutdown": "true", "tags.Environment": "dev", "tags.Name": "db-subnet-az1-rt", "tags.Owner": "<PERSON><PERSON>", "tags.Project": "InfoVault", "tags_all.%": "5", "tags_all.AutoShutdown": "true", "tags_all.Environment": "dev", "tags_all.Name": "db-subnet-az1-rt", "tags_all.Owner": "<PERSON><PERSON>", "tags_all.Project": "InfoVault", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_route_table.tfer--rtb-09a828af05f32b0cf": {"type": "aws_route_table", "depends_on": [], "primary": {"id": "rtb-09a828af05f32b0cf", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:route-table/rtb-09a828af05f32b0cf", "id": "rtb-09a828af05f32b0cf", "owner_id": "046276255144", "propagating_vgws.#": "0", "route.#": "1", "route.0.carrier_gateway_id": "", "route.0.cidr_block": "**********/27", "route.0.core_network_arn": "", "route.0.destination_prefix_list_id": "", "route.0.egress_only_gateway_id": "", "route.0.gateway_id": "", "route.0.ipv6_cidr_block": "", "route.0.local_gateway_id": "", "route.0.nat_gateway_id": "", "route.0.network_interface_id": "", "route.0.transit_gateway_id": "", "route.0.vpc_endpoint_id": "", "route.0.vpc_peering_connection_id": "pcx-02dd2ea276cb24bb3", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "infovault-dev-patching-subnet-az1-rt", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "infovault-dev-patching-subnet-az1-rt", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_id": "vpc-0ca0480cce82946b3"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_route_table.tfer--rtb-09e7342b3a3bc5507": {"type": "aws_route_table", "depends_on": [], "primary": {"id": "rtb-09e7342b3a3bc5507", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:route-table/rtb-09e7342b3a3bc5507", "id": "rtb-09e7342b3a3bc5507", "owner_id": "046276255144", "propagating_vgws.#": "0", "route.#": "0", "tags.%": "2", "tags.Name": "aws-controltower-PrivateSubnet2ARouteTable", "tags.Network": "Private", "tags_all.%": "2", "tags_all.Name": "aws-controltower-PrivateSubnet2ARouteTable", "tags_all.Network": "Private", "vpc_id": "vpc-02282d8d4c9c7764c"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_route_table.tfer--rtb-0a3ed8d3c391e4cca": {"type": "aws_route_table", "depends_on": [], "primary": {"id": "rtb-0a3ed8d3c391e4cca", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:route-table/rtb-0a3ed8d3c391e4cca", "id": "rtb-0a3ed8d3c391e4cca", "owner_id": "046276255144", "propagating_vgws.#": "0", "route.#": "2", "route.0.carrier_gateway_id": "", "route.0.cidr_block": "**********/26", "route.0.core_network_arn": "", "route.0.destination_prefix_list_id": "", "route.0.egress_only_gateway_id": "", "route.0.gateway_id": "", "route.0.ipv6_cidr_block": "", "route.0.local_gateway_id": "", "route.0.nat_gateway_id": "", "route.0.network_interface_id": "", "route.0.transit_gateway_id": "", "route.0.vpc_endpoint_id": "", "route.0.vpc_peering_connection_id": "pcx-00306eabadaf4a547", "route.1.carrier_gateway_id": "", "route.1.cidr_block": "**********/22", "route.1.core_network_arn": "", "route.1.destination_prefix_list_id": "", "route.1.egress_only_gateway_id": "", "route.1.gateway_id": "", "route.1.ipv6_cidr_block": "", "route.1.local_gateway_id": "", "route.1.nat_gateway_id": "", "route.1.network_interface_id": "", "route.1.transit_gateway_id": "", "route.1.vpc_endpoint_id": "", "route.1.vpc_peering_connection_id": "pcx-07364bf66a2a53a7d", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "infovault-dev-private-rt", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "infovault-dev-private-rt", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_route_table.tfer--rtb-0aa2547a225d317bd": {"type": "aws_route_table", "depends_on": [], "primary": {"id": "rtb-0aa2547a225d317bd", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:route-table/rtb-0aa2547a225d317bd", "id": "rtb-0aa2547a225d317bd", "owner_id": "046276255144", "propagating_vgws.#": "0", "route.#": "0", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "infovault-dev-private-rt", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "infovault-dev-private-rt", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_id": "vpc-0e75036a2ad9e5b4f"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_route_table.tfer--rtb-0c5895a403cd0d50e": {"type": "aws_route_table", "depends_on": [], "primary": {"id": "rtb-0c5895a403cd0d50e", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:route-table/rtb-0c5895a403cd0d50e", "id": "rtb-0c5895a403cd0d50e", "owner_id": "046276255144", "propagating_vgws.#": "0", "route.#": "0", "tags.%": "9", "tags.AutoShutdown": "true", "tags.Compartment": "ABLRHF", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "infovault-dev-ablrhf-rt", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "9", "tags_all.AutoShutdown": "true", "tags_all.Compartment": "ABLRHF", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "infovault-dev-ablrhf-rt", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_id": "vpc-0e75036a2ad9e5b4f"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_route_table.tfer--rtb-0c7da122446383cc5": {"type": "aws_route_table", "depends_on": [], "primary": {"id": "rtb-0c7da122446383cc5", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:route-table/rtb-0c7da122446383cc5", "id": "rtb-0c7da122446383cc5", "owner_id": "046276255144", "propagating_vgws.#": "0", "route.#": "2", "route.0.carrier_gateway_id": "", "route.0.cidr_block": "0.0.0.0/0", "route.0.core_network_arn": "", "route.0.destination_prefix_list_id": "", "route.0.egress_only_gateway_id": "", "route.0.gateway_id": "", "route.0.ipv6_cidr_block": "", "route.0.local_gateway_id": "", "route.0.nat_gateway_id": "nat-0431bfe8618055bd8", "route.0.network_interface_id": "", "route.0.transit_gateway_id": "", "route.0.vpc_endpoint_id": "", "route.0.vpc_peering_connection_id": "", "route.1.carrier_gateway_id": "", "route.1.cidr_block": "**********/26", "route.1.core_network_arn": "", "route.1.destination_prefix_list_id": "", "route.1.egress_only_gateway_id": "", "route.1.gateway_id": "", "route.1.ipv6_cidr_block": "", "route.1.local_gateway_id": "", "route.1.nat_gateway_id": "", "route.1.network_interface_id": "", "route.1.transit_gateway_id": "", "route.1.vpc_endpoint_id": "", "route.1.vpc_peering_connection_id": "pcx-00306eabadaf4a547", "tags.%": "6", "tags.AutoShutdown": "true", "tags.Environment": "dev", "tags.Name": "smtp-subnet-dlz1-private-rt", "tags.Owner": "<PERSON><PERSON>", "tags.Project": "InfoVault", "tags.Type": "Private", "tags_all.%": "6", "tags_all.AutoShutdown": "true", "tags_all.Environment": "dev", "tags_all.Name": "smtp-subnet-dlz1-private-rt", "tags_all.Owner": "<PERSON><PERSON>", "tags_all.Project": "InfoVault", "tags_all.Type": "Private", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_route_table.tfer--rtb-0cb7cabf4d81ee7bd": {"type": "aws_route_table", "depends_on": [], "primary": {"id": "rtb-0cb7cabf4d81ee7bd", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:route-table/rtb-0cb7cabf4d81ee7bd", "id": "rtb-0cb7cabf4d81ee7bd", "owner_id": "046276255144", "propagating_vgws.#": "0", "route.#": "0", "tags.%": "0", "tags_all.%": "0", "vpc_id": "vpc-0e75036a2ad9e5b4f"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_route_table.tfer--rtb-0d508af52f5965f6a": {"type": "aws_route_table", "depends_on": [], "primary": {"id": "rtb-0d508af52f5965f6a", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:route-table/rtb-0d508af52f5965f6a", "id": "rtb-0d508af52f5965f6a", "owner_id": "046276255144", "propagating_vgws.#": "0", "route.#": "3", "route.0.carrier_gateway_id": "", "route.0.cidr_block": "0.0.0.0/0", "route.0.core_network_arn": "", "route.0.destination_prefix_list_id": "", "route.0.egress_only_gateway_id": "", "route.0.gateway_id": "igw-05beaf9de3b1e3902", "route.0.ipv6_cidr_block": "", "route.0.local_gateway_id": "", "route.0.nat_gateway_id": "", "route.0.network_interface_id": "", "route.0.transit_gateway_id": "", "route.0.vpc_endpoint_id": "", "route.0.vpc_peering_connection_id": "", "route.1.carrier_gateway_id": "", "route.1.cidr_block": "**********/26", "route.1.core_network_arn": "", "route.1.destination_prefix_list_id": "", "route.1.egress_only_gateway_id": "", "route.1.gateway_id": "", "route.1.ipv6_cidr_block": "", "route.1.local_gateway_id": "", "route.1.nat_gateway_id": "", "route.1.network_interface_id": "", "route.1.transit_gateway_id": "", "route.1.vpc_endpoint_id": "", "route.1.vpc_peering_connection_id": "pcx-00306eabadaf4a547", "route.2.carrier_gateway_id": "", "route.2.cidr_block": "**********/22", "route.2.core_network_arn": "", "route.2.destination_prefix_list_id": "", "route.2.egress_only_gateway_id": "", "route.2.gateway_id": "", "route.2.ipv6_cidr_block": "", "route.2.local_gateway_id": "", "route.2.nat_gateway_id": "", "route.2.network_interface_id": "", "route.2.transit_gateway_id": "", "route.2.vpc_endpoint_id": "", "route.2.vpc_peering_connection_id": "pcx-07364bf66a2a53a7d", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "rtb-gen-egress", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "rtb-gen-egress", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_route_table.tfer--rtb-0de7189dd22690311": {"type": "aws_route_table", "depends_on": [], "primary": {"id": "rtb-0de7189dd22690311", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:route-table/rtb-0de7189dd22690311", "id": "rtb-0de7189dd22690311", "owner_id": "046276255144", "propagating_vgws.#": "0", "route.#": "1", "route.0.carrier_gateway_id": "", "route.0.cidr_block": "**********/26", "route.0.core_network_arn": "", "route.0.destination_prefix_list_id": "", "route.0.egress_only_gateway_id": "", "route.0.gateway_id": "", "route.0.ipv6_cidr_block": "", "route.0.local_gateway_id": "", "route.0.nat_gateway_id": "", "route.0.network_interface_id": "", "route.0.transit_gateway_id": "", "route.0.vpc_endpoint_id": "", "route.0.vpc_peering_connection_id": "pcx-0f0db258d11626f4c", "tags.%": "0", "tags_all.%": "0", "vpc_id": "vpc-037f619602fa293a1"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_route_table.tfer--rtb-0e47b2c134f9ec024": {"type": "aws_route_table", "depends_on": [], "primary": {"id": "rtb-0e47b2c134f9ec024", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:route-table/rtb-0e47b2c134f9ec024", "id": "rtb-0e47b2c134f9ec024", "owner_id": "046276255144", "propagating_vgws.#": "0", "route.#": "3", "route.0.carrier_gateway_id": "", "route.0.cidr_block": "0.0.0.0/0", "route.0.core_network_arn": "", "route.0.destination_prefix_list_id": "", "route.0.egress_only_gateway_id": "", "route.0.gateway_id": "", "route.0.ipv6_cidr_block": "", "route.0.local_gateway_id": "", "route.0.nat_gateway_id": "nat-0431bfe8618055bd8", "route.0.network_interface_id": "", "route.0.transit_gateway_id": "", "route.0.vpc_endpoint_id": "", "route.0.vpc_peering_connection_id": "", "route.1.carrier_gateway_id": "", "route.1.cidr_block": "**********/26", "route.1.core_network_arn": "", "route.1.destination_prefix_list_id": "", "route.1.egress_only_gateway_id": "", "route.1.gateway_id": "", "route.1.ipv6_cidr_block": "", "route.1.local_gateway_id": "", "route.1.nat_gateway_id": "", "route.1.network_interface_id": "", "route.1.transit_gateway_id": "", "route.1.vpc_endpoint_id": "", "route.1.vpc_peering_connection_id": "pcx-00306eabadaf4a547", "route.2.carrier_gateway_id": "", "route.2.cidr_block": "**********/22", "route.2.core_network_arn": "", "route.2.destination_prefix_list_id": "", "route.2.egress_only_gateway_id": "", "route.2.gateway_id": "", "route.2.ipv6_cidr_block": "", "route.2.local_gateway_id": "", "route.2.nat_gateway_id": "", "route.2.network_interface_id": "", "route.2.transit_gateway_id": "", "route.2.vpc_endpoint_id": "", "route.2.vpc_peering_connection_id": "pcx-07364bf66a2a53a7d", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "rtb-gen-migration", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "rtb-gen-migration", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_route_table.tfer--rtb-0eb045ebd5d6d7479": {"type": "aws_route_table", "depends_on": [], "primary": {"id": "rtb-0eb045ebd5d6d7479", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:route-table/rtb-0eb045ebd5d6d7479", "id": "rtb-0eb045ebd5d6d7479", "owner_id": "046276255144", "propagating_vgws.#": "0", "route.#": "3", "route.0.carrier_gateway_id": "", "route.0.cidr_block": "0.0.0.0/0", "route.0.core_network_arn": "", "route.0.destination_prefix_list_id": "", "route.0.egress_only_gateway_id": "", "route.0.gateway_id": "igw-00a312b81674ba618", "route.0.ipv6_cidr_block": "", "route.0.local_gateway_id": "", "route.0.nat_gateway_id": "", "route.0.network_interface_id": "", "route.0.transit_gateway_id": "", "route.0.vpc_endpoint_id": "", "route.0.vpc_peering_connection_id": "", "route.1.carrier_gateway_id": "", "route.1.cidr_block": "**********/27", "route.1.core_network_arn": "", "route.1.destination_prefix_list_id": "", "route.1.egress_only_gateway_id": "", "route.1.gateway_id": "", "route.1.ipv6_cidr_block": "", "route.1.local_gateway_id": "", "route.1.nat_gateway_id": "", "route.1.network_interface_id": "", "route.1.transit_gateway_id": "", "route.1.vpc_endpoint_id": "", "route.1.vpc_peering_connection_id": "pcx-02dd2ea276cb24bb3", "route.2.carrier_gateway_id": "", "route.2.cidr_block": "**********/26", "route.2.core_network_arn": "", "route.2.destination_prefix_list_id": "", "route.2.egress_only_gateway_id": "", "route.2.gateway_id": "", "route.2.ipv6_cidr_block": "", "route.2.local_gateway_id": "", "route.2.nat_gateway_id": "", "route.2.network_interface_id": "", "route.2.transit_gateway_id": "", "route.2.vpc_endpoint_id": "", "route.2.vpc_peering_connection_id": "pcx-0f0db258d11626f4c", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "dev-net-rtb-pub", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "dev-net-rtb-pub", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_id": "vpc-037f619602fa293a1"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_route_table.tfer--rtb-0f04c8976ea037652": {"type": "aws_route_table", "depends_on": [], "primary": {"id": "rtb-0f04c8976ea037652", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:route-table/rtb-0f04c8976ea037652", "id": "rtb-0f04c8976ea037652", "owner_id": "046276255144", "propagating_vgws.#": "0", "route.#": "1", "route.0.carrier_gateway_id": "", "route.0.cidr_block": "**********/26", "route.0.core_network_arn": "", "route.0.destination_prefix_list_id": "", "route.0.egress_only_gateway_id": "", "route.0.gateway_id": "", "route.0.ipv6_cidr_block": "", "route.0.local_gateway_id": "", "route.0.nat_gateway_id": "", "route.0.network_interface_id": "", "route.0.transit_gateway_id": "", "route.0.vpc_endpoint_id": "", "route.0.vpc_peering_connection_id": "pcx-00306eabadaf4a547", "tags.%": "0", "tags_all.%": "0", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_route_table.tfer--rtb-0f6307e8c214b6a71": {"type": "aws_route_table", "depends_on": [], "primary": {"id": "rtb-0f6307e8c214b6a71", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:route-table/rtb-0f6307e8c214b6a71", "id": "rtb-0f6307e8c214b6a71", "owner_id": "046276255144", "propagating_vgws.#": "0", "route.#": "2", "route.0.carrier_gateway_id": "", "route.0.cidr_block": "0.0.0.0/0", "route.0.core_network_arn": "", "route.0.destination_prefix_list_id": "", "route.0.egress_only_gateway_id": "", "route.0.gateway_id": "", "route.0.ipv6_cidr_block": "", "route.0.local_gateway_id": "", "route.0.nat_gateway_id": "nat-0431bfe8618055bd8", "route.0.network_interface_id": "", "route.0.transit_gateway_id": "", "route.0.vpc_endpoint_id": "", "route.0.vpc_peering_connection_id": "", "route.1.carrier_gateway_id": "", "route.1.cidr_block": "**********/26", "route.1.core_network_arn": "", "route.1.destination_prefix_list_id": "", "route.1.egress_only_gateway_id": "", "route.1.gateway_id": "", "route.1.ipv6_cidr_block": "", "route.1.local_gateway_id": "", "route.1.nat_gateway_id": "", "route.1.network_interface_id": "", "route.1.transit_gateway_id": "", "route.1.vpc_endpoint_id": "", "route.1.vpc_peering_connection_id": "pcx-00306eabadaf4a547", "tags.%": "6", "tags.AutoShutdown": "true", "tags.Environment": "dev", "tags.Name": "smtp-subnet-dlz1-private-rt", "tags.Owner": "<PERSON><PERSON>", "tags.Project": "InfoVault", "tags.Type": "Private", "tags_all.%": "6", "tags_all.AutoShutdown": "true", "tags_all.Environment": "dev", "tags_all.Name": "smtp-subnet-dlz1-private-rt", "tags_all.Owner": "<PERSON><PERSON>", "tags_all.Project": "InfoVault", "tags_all.Type": "Private", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_route_table_association.tfer--subnet-015e4b2acb87091d9": {"type": "aws_route_table_association", "depends_on": [], "primary": {"id": "rtbassoc-06146946bb9e68346", "attributes": {"gateway_id": "", "id": "rtbassoc-06146946bb9e68346", "route_table_id": "rtb-038d861f618d80392", "subnet_id": "subnet-015e4b2acb87091d9"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_route_table_association.tfer--subnet-01e871c49dc4b9b1f": {"type": "aws_route_table_association", "depends_on": [], "primary": {"id": "rtbassoc-0145386c6a5e15295", "attributes": {"gateway_id": "", "id": "rtbassoc-0145386c6a5e15295", "route_table_id": "rtb-0eb045ebd5d6d7479", "subnet_id": "subnet-01e871c49dc4b9b1f"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_route_table_association.tfer--subnet-023cafbedbb31d13e": {"type": "aws_route_table_association", "depends_on": [], "primary": {"id": "rtbassoc-0a77663682b071234", "attributes": {"gateway_id": "", "id": "rtbassoc-0a77663682b071234", "route_table_id": "rtb-0795b2e3d3e3f88ed", "subnet_id": "subnet-023cafbedbb31d13e"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_route_table_association.tfer--subnet-043dd23a18b80a280": {"type": "aws_route_table_association", "depends_on": [], "primary": {"id": "rtbassoc-093f7871287b6842f", "attributes": {"gateway_id": "", "id": "rtbassoc-093f7871287b6842f", "route_table_id": "rtb-0d508af52f5965f6a", "subnet_id": "subnet-043dd23a18b80a280"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_route_table_association.tfer--subnet-056cc33d6674e5027": {"type": "aws_route_table_association", "depends_on": [], "primary": {"id": "rtbassoc-016d734fcc59228fb", "attributes": {"gateway_id": "", "id": "rtbassoc-016d734fcc59228fb", "route_table_id": "rtb-09e7342b3a3bc5507", "subnet_id": "subnet-056cc33d6674e5027"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_route_table_association.tfer--subnet-0669d84e56a7dc53e": {"type": "aws_route_table_association", "depends_on": [], "primary": {"id": "rtbassoc-06eb808fa3c17a31d", "attributes": {"gateway_id": "", "id": "rtbassoc-06eb808fa3c17a31d", "route_table_id": "rtb-02a9859d16aa6e04b", "subnet_id": "subnet-0669d84e56a7dc53e"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_route_table_association.tfer--subnet-07ab4b059d69e4044": {"type": "aws_route_table_association", "depends_on": [], "primary": {"id": "rtbassoc-0f6a5133d0777ab3d", "attributes": {"gateway_id": "", "id": "rtbassoc-0f6a5133d0777ab3d", "route_table_id": "rtb-05df1c4be4e435f04", "subnet_id": "subnet-07ab4b059d69e4044"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_route_table_association.tfer--subnet-080d8fdb4e9add74c": {"type": "aws_route_table_association", "depends_on": [], "primary": {"id": "rtbassoc-020136bee2a4527ea", "attributes": {"gateway_id": "", "id": "rtbassoc-020136bee2a4527ea", "route_table_id": "rtb-0c7da122446383cc5", "subnet_id": "subnet-080d8fdb4e9add74c"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_route_table_association.tfer--subnet-081b6eb841b4451b3": {"type": "aws_route_table_association", "depends_on": [], "primary": {"id": "rtbassoc-06a02157a37f09003", "attributes": {"gateway_id": "", "id": "rtbassoc-06a02157a37f09003", "route_table_id": "rtb-04dea60d5aeca9def", "subnet_id": "subnet-081b6eb841b4451b3"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_route_table_association.tfer--subnet-0a1802eb99e96cbed": {"type": "aws_route_table_association", "depends_on": [], "primary": {"id": "rtbassoc-0970518f864799a57", "attributes": {"gateway_id": "", "id": "rtbassoc-0970518f864799a57", "route_table_id": "rtb-0e47b2c134f9ec024", "subnet_id": "subnet-0a1802eb99e96cbed"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_route_table_association.tfer--subnet-0a5d4aa5489b9ab3a": {"type": "aws_route_table_association", "depends_on": [], "primary": {"id": "rtbassoc-05c945c54de24a721", "attributes": {"gateway_id": "", "id": "rtbassoc-05c945c54de24a721", "route_table_id": "rtb-0f6307e8c214b6a71", "subnet_id": "subnet-0a5d4aa5489b9ab3a"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_route_table_association.tfer--subnet-0aec855b39a7be02e": {"type": "aws_route_table_association", "depends_on": [], "primary": {"id": "rtbassoc-0b52b0666d1283362", "attributes": {"gateway_id": "", "id": "rtbassoc-0b52b0666d1283362", "route_table_id": "rtb-00c4597c163a0ed96", "subnet_id": "subnet-0aec855b39a7be02e"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_route_table_association.tfer--subnet-0b9afa4ae103de5b5": {"type": "aws_route_table_association", "depends_on": [], "primary": {"id": "rtbassoc-051824847cfbe0a0f", "attributes": {"gateway_id": "", "id": "rtbassoc-051824847cfbe0a0f", "route_table_id": "rtb-048f93d202df7cd40", "subnet_id": "subnet-0b9afa4ae103de5b5"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_route_table_association.tfer--subnet-0c6066202240c38be": {"type": "aws_route_table_association", "depends_on": [], "primary": {"id": "rtbassoc-0c71a767d2ff6b502", "attributes": {"gateway_id": "", "id": "rtbassoc-0c71a767d2ff6b502", "route_table_id": "rtb-0c5895a403cd0d50e", "subnet_id": "subnet-0c6066202240c38be"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_route_table_association.tfer--subnet-0c775a4f57dfa0e61": {"type": "aws_route_table_association", "depends_on": [], "primary": {"id": "rtbassoc-07e961bcbe1e529de", "attributes": {"gateway_id": "", "id": "rtbassoc-07e961bcbe1e529de", "route_table_id": "rtb-06ca622706fb0676d", "subnet_id": "subnet-0c775a4f57dfa0e61"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_route_table_association.tfer--subnet-0d4f42cf30b20c42d": {"type": "aws_route_table_association", "depends_on": [], "primary": {"id": "rtbassoc-03e7c028d710cd01b", "attributes": {"gateway_id": "", "id": "rtbassoc-03e7c028d710cd01b", "route_table_id": "rtb-098958cd03a46c219", "subnet_id": "subnet-0d4f42cf30b20c42d"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_route_table_association.tfer--subnet-0dacd0ef604204014": {"type": "aws_route_table_association", "depends_on": [], "primary": {"id": "rtbassoc-09b91ec6adfeb06de", "attributes": {"gateway_id": "", "id": "rtbassoc-09b91ec6adfeb06de", "route_table_id": "rtb-09a828af05f32b0cf", "subnet_id": "subnet-0dacd0ef604204014"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_route_table_association.tfer--subnet-0e2a201f2503f59ee": {"type": "aws_route_table_association", "depends_on": [], "primary": {"id": "rtbassoc-0a472e0c79f833a27", "attributes": {"gateway_id": "", "id": "rtbassoc-0a472e0c79f833a27", "route_table_id": "rtb-015ae5889ca4aaadd", "subnet_id": "subnet-0e2a201f2503f59ee"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}}, "depends_on": []}]}