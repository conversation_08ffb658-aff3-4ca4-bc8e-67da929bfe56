{"version": 3, "terraform_version": "0.12.31", "serial": 1, "lineage": "dce9d0da-edcb-f3a2-166b-3b76e07ddeae", "modules": [{"path": ["root"], "outputs": {"aws_default_network_acl_tfer--acl-0022470dc4a0a4d11_id": {"sensitive": false, "type": "string", "value": "acl-0022470dc4a0a4d11"}, "aws_default_network_acl_tfer--acl-0228326a161d09c04_id": {"sensitive": false, "type": "string", "value": "acl-0228326a161d09c04"}, "aws_default_network_acl_tfer--acl-024c584c2c3382d5c_id": {"sensitive": false, "type": "string", "value": "acl-024c584c2c3382d5c"}, "aws_default_network_acl_tfer--acl-030537d8bab318bd1_id": {"sensitive": false, "type": "string", "value": "acl-030537d8bab318bd1"}, "aws_default_network_acl_tfer--acl-06b07cdd2cf4b4ab2_id": {"sensitive": false, "type": "string", "value": "acl-06b07cdd2cf4b4ab2"}, "aws_default_network_acl_tfer--acl-0cf3a7e6c0d8d70ff_id": {"sensitive": false, "type": "string", "value": "acl-0cf3a7e6c0d8d70ff"}, "aws_network_acl_tfer--acl-03f8fdb675f07f357_id": {"sensitive": false, "type": "string", "value": "acl-03f8fdb675f07f357"}, "aws_network_acl_tfer--acl-08f8318685da83166_id": {"sensitive": false, "type": "string", "value": "acl-08f8318685da83166"}}, "resources": {"aws_default_network_acl.tfer--acl-0022470dc4a0a4d11": {"type": "aws_default_network_acl", "depends_on": [], "primary": {"id": "acl-0022470dc4a0a4d11", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-acl/acl-0022470dc4a0a4d11", "egress.#": "1", "egress.0.action": "allow", "egress.0.cidr_block": "0.0.0.0/0", "egress.0.from_port": "0", "egress.0.icmp_code": "0", "egress.0.icmp_type": "0", "egress.0.ipv6_cidr_block": "", "egress.0.protocol": "-1", "egress.0.rule_no": "100", "egress.0.to_port": "0", "id": "acl-0022470dc4a0a4d11", "ingress.#": "1", "ingress.0.action": "allow", "ingress.0.cidr_block": "0.0.0.0/0", "ingress.0.from_port": "0", "ingress.0.icmp_code": "0", "ingress.0.icmp_type": "0", "ingress.0.ipv6_cidr_block": "", "ingress.0.protocol": "-1", "ingress.0.rule_no": "100", "ingress.0.to_port": "0", "owner_id": "046276255144", "subnet_ids.#": "3", "subnet_ids.0": "subnet-056cc33d6674e5027", "subnet_ids.1": "subnet-0669d84e56a7dc53e", "subnet_ids.2": "subnet-0b9afa4ae103de5b5", "tags.%": "0", "tags_all.%": "0", "vpc_id": "vpc-02282d8d4c9c7764c"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_default_network_acl.tfer--acl-0228326a161d09c04": {"type": "aws_default_network_acl", "depends_on": [], "primary": {"id": "acl-0228326a161d09c04", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-acl/acl-0228326a161d09c04", "egress.#": "1", "egress.0.action": "allow", "egress.0.cidr_block": "0.0.0.0/0", "egress.0.from_port": "0", "egress.0.icmp_code": "0", "egress.0.icmp_type": "0", "egress.0.ipv6_cidr_block": "", "egress.0.protocol": "-1", "egress.0.rule_no": "100", "egress.0.to_port": "0", "id": "acl-0228326a161d09c04", "ingress.#": "1", "ingress.0.action": "allow", "ingress.0.cidr_block": "0.0.0.0/0", "ingress.0.from_port": "0", "ingress.0.icmp_code": "0", "ingress.0.icmp_type": "0", "ingress.0.ipv6_cidr_block": "", "ingress.0.protocol": "-1", "ingress.0.rule_no": "100", "ingress.0.to_port": "0", "owner_id": "046276255144", "subnet_ids.#": "0", "tags.%": "0", "tags_all.%": "0", "vpc_id": "vpc-0e75036a2ad9e5b4f"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_default_network_acl.tfer--acl-024c584c2c3382d5c": {"type": "aws_default_network_acl", "depends_on": [], "primary": {"id": "acl-024c584c2c3382d5c", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-acl/acl-024c584c2c3382d5c", "egress.#": "1", "egress.0.action": "allow", "egress.0.cidr_block": "0.0.0.0/0", "egress.0.from_port": "0", "egress.0.icmp_code": "0", "egress.0.icmp_type": "0", "egress.0.ipv6_cidr_block": "", "egress.0.protocol": "-1", "egress.0.rule_no": "100", "egress.0.to_port": "0", "id": "acl-024c584c2c3382d5c", "ingress.#": "1", "ingress.0.action": "allow", "ingress.0.cidr_block": "0.0.0.0/0", "ingress.0.from_port": "0", "ingress.0.icmp_code": "0", "ingress.0.icmp_type": "0", "ingress.0.ipv6_cidr_block": "", "ingress.0.protocol": "-1", "ingress.0.rule_no": "100", "ingress.0.to_port": "0", "owner_id": "046276255144", "subnet_ids.#": "0", "tags.%": "0", "tags_all.%": "0", "vpc_id": "vpc-0ca0480cce82946b3"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_default_network_acl.tfer--acl-030537d8bab318bd1": {"type": "aws_default_network_acl", "depends_on": [], "primary": {"id": "acl-030537d8bab318bd1", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-acl/acl-030537d8bab318bd1", "egress.#": "1", "egress.0.action": "allow", "egress.0.cidr_block": "0.0.0.0/0", "egress.0.from_port": "0", "egress.0.icmp_code": "0", "egress.0.icmp_type": "0", "egress.0.ipv6_cidr_block": "", "egress.0.protocol": "-1", "egress.0.rule_no": "100", "egress.0.to_port": "0", "id": "acl-030537d8bab318bd1", "ingress.#": "1", "ingress.0.action": "allow", "ingress.0.cidr_block": "0.0.0.0/0", "ingress.0.from_port": "0", "ingress.0.icmp_code": "0", "ingress.0.icmp_type": "0", "ingress.0.ipv6_cidr_block": "", "ingress.0.protocol": "-1", "ingress.0.rule_no": "100", "ingress.0.to_port": "0", "owner_id": "046276255144", "subnet_ids.#": "4", "subnet_ids.0": "subnet-04910ced717a5584a", "subnet_ids.1": "subnet-07ab4b059d69e4044", "subnet_ids.2": "subnet-0ebbc016c2fc6b3db", "subnet_ids.3": "subnet-0f80c3ddc6536fd2a", "tags.%": "0", "tags_all.%": "0", "vpc_id": "vpc-05f1b20a6634c6a38"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_default_network_acl.tfer--acl-06b07cdd2cf4b4ab2": {"type": "aws_default_network_acl", "depends_on": [], "primary": {"id": "acl-06b07cdd2cf4b4ab2", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-acl/acl-06b07cdd2cf4b4ab2", "egress.#": "1", "egress.0.action": "allow", "egress.0.cidr_block": "0.0.0.0/0", "egress.0.from_port": "0", "egress.0.icmp_code": "0", "egress.0.icmp_type": "0", "egress.0.ipv6_cidr_block": "", "egress.0.protocol": "-1", "egress.0.rule_no": "100", "egress.0.to_port": "0", "id": "acl-06b07cdd2cf4b4ab2", "ingress.#": "1", "ingress.0.action": "allow", "ingress.0.cidr_block": "0.0.0.0/0", "ingress.0.from_port": "0", "ingress.0.icmp_code": "0", "ingress.0.icmp_type": "0", "ingress.0.ipv6_cidr_block": "", "ingress.0.protocol": "-1", "ingress.0.rule_no": "100", "ingress.0.to_port": "0", "owner_id": "046276255144", "subnet_ids.#": "2", "subnet_ids.0": "subnet-01e871c49dc4b9b1f", "subnet_ids.1": "subnet-0aec855b39a7be02e", "tags.%": "0", "tags_all.%": "0", "vpc_id": "vpc-037f619602fa293a1"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_default_network_acl.tfer--acl-0cf3a7e6c0d8d70ff": {"type": "aws_default_network_acl", "depends_on": [], "primary": {"id": "acl-0cf3a7e6c0d8d70ff", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-acl/acl-0cf3a7e6c0d8d70ff", "egress.#": "1", "egress.0.action": "allow", "egress.0.cidr_block": "0.0.0.0/0", "egress.0.from_port": "0", "egress.0.icmp_code": "0", "egress.0.icmp_type": "0", "egress.0.ipv6_cidr_block": "", "egress.0.protocol": "-1", "egress.0.rule_no": "100", "egress.0.to_port": "0", "id": "acl-0cf3a7e6c0d8d70ff", "ingress.#": "1", "ingress.0.action": "allow", "ingress.0.cidr_block": "0.0.0.0/0", "ingress.0.from_port": "0", "ingress.0.icmp_code": "0", "ingress.0.icmp_type": "0", "ingress.0.ipv6_cidr_block": "", "ingress.0.protocol": "-1", "ingress.0.rule_no": "100", "ingress.0.to_port": "0", "owner_id": "046276255144", "subnet_ids.#": "10", "subnet_ids.0": "subnet-015e4b2acb87091d9", "subnet_ids.1": "subnet-023cafbedbb31d13e", "subnet_ids.2": "subnet-043dd23a18b80a280", "subnet_ids.3": "subnet-080d8fdb4e9add74c", "subnet_ids.4": "subnet-081b6eb841b4451b3", "subnet_ids.5": "subnet-0a1802eb99e96cbed", "subnet_ids.6": "subnet-0a5d4aa5489b9ab3a", "subnet_ids.7": "subnet-0c775a4f57dfa0e61", "subnet_ids.8": "subnet-0d4f42cf30b20c42d", "subnet_ids.9": "subnet-0e2a201f2503f59ee", "tags.%": "0", "tags_all.%": "0", "vpc_id": "vpc-05e0e8104b3321c40"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_acl.tfer--acl-03f8fdb675f07f357": {"type": "aws_network_acl", "depends_on": [], "primary": {"id": "acl-03f8fdb675f07f357", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-acl/acl-03f8fdb675f07f357", "egress.#": "1", "egress.0.action": "allow", "egress.0.cidr_block": "0.0.0.0/0", "egress.0.from_port": "0", "egress.0.icmp_code": "0", "egress.0.icmp_type": "0", "egress.0.ipv6_cidr_block": "", "egress.0.protocol": "-1", "egress.0.rule_no": "100", "egress.0.to_port": "0", "id": "acl-03f8fdb675f07f357", "ingress.#": "1", "ingress.0.action": "allow", "ingress.0.cidr_block": "0.0.0.0/0", "ingress.0.from_port": "0", "ingress.0.icmp_code": "0", "ingress.0.icmp_type": "0", "ingress.0.ipv6_cidr_block": "", "ingress.0.protocol": "-1", "ingress.0.rule_no": "100", "ingress.0.to_port": "0", "owner_id": "046276255144", "subnet_ids.#": "1", "subnet_ids.0": "subnet-0c6066202240c38be", "tags.%": "9", "tags.AutoShutdown": "true", "tags.Compartment": "ABLRHF", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "infovault-dev-ablrhf-nacl", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "9", "tags_all.AutoShutdown": "true", "tags_all.Compartment": "ABLRHF", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "infovault-dev-ablrhf-nacl", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_id": "vpc-0e75036a2ad9e5b4f"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_network_acl.tfer--acl-08f8318685da83166": {"type": "aws_network_acl", "depends_on": [], "primary": {"id": "acl-08f8318685da83166", "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:046276255144:network-acl/acl-08f8318685da83166", "egress.#": "1", "egress.0.action": "allow", "egress.0.cidr_block": "0.0.0.0/0", "egress.0.from_port": "0", "egress.0.icmp_code": "0", "egress.0.icmp_type": "0", "egress.0.ipv6_cidr_block": "", "egress.0.protocol": "-1", "egress.0.rule_no": "100", "egress.0.to_port": "0", "id": "acl-08f8318685da83166", "ingress.#": "1", "ingress.0.action": "allow", "ingress.0.cidr_block": "0.0.0.0/0", "ingress.0.from_port": "0", "ingress.0.icmp_code": "0", "ingress.0.icmp_type": "0", "ingress.0.ipv6_cidr_block": "", "ingress.0.protocol": "-1", "ingress.0.rule_no": "100", "ingress.0.to_port": "0", "owner_id": "046276255144", "subnet_ids.#": "1", "subnet_ids.0": "subnet-0dacd0ef604204014", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "infovault-dev-patching-subnet-az1-nacl", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "infovault-dev-patching-subnet-az1-nacl", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_id": "vpc-0ca0480cce82946b3"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}}, "depends_on": []}]}