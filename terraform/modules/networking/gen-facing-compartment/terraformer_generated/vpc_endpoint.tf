# Generated by Terraformer for VPC vpc-05e0e8104b3321c40
# Source: /Users/<USER>/Documents/Projects/infovault-infrastructure/terraform/modules/networking/gen-facing-compartment/raw_terraformer/aws/vpc_endpoint/vpc_endpoint.tf
# Generated on: 2025-06-09 13:17:18

resource "aws_vpc_endpoint" "tfer--vpce-000d3536c71bcfc70" {
  dns_options {
    dns_record_ip_type                             = "ipv4"
    private_dns_only_for_inbound_resolver_endpoint = "false"
  }

  ip_address_type     = "ipv4"
  policy              = "{\"Statement\":[{\"Action\":\"*\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"*\"}]}"
  private_dns_enabled = "true"
  security_group_ids  = ["sg-0870c466d0db940c4"]
  service_name        = "com.amazonaws.ap-southeast-1.ssmmessages"
  service_region      = "ap-southeast-1"

  subnet_configuration {
    ipv4      = "**********"
    subnet_id = "subnet-0dacd0ef604204014"
  }

  subnet_ids = ["subnet-0dacd0ef604204014"]

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "vpce-infovault-dev-Patching-Compartment-ssmmessages"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "vpce-infovault-dev-Patching-Compartment-ssmmessages"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_endpoint_type = "Interface"
  vpc_id            = "vpc-0ca0480cce82946b3"
}

resource "aws_vpc_endpoint" "tfer--vpce-00634b26f7e325402" {
  dns_options {
    dns_record_ip_type                             = "ipv4"
    private_dns_only_for_inbound_resolver_endpoint = "false"
  }

  ip_address_type     = "ipv4"
  policy              = "{\"Statement\":[{\"Action\":\"*\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"*\"}]}"
  private_dns_enabled = "true"
  security_group_ids  = ["sg-05f09a0dc7ab8c53f"]
  service_name        = "com.amazonaws.ap-southeast-1.ecr.api"
  service_region      = "ap-southeast-1"

  subnet_configuration {
    ipv4      = "***********"
    subnet_id = "subnet-023cafbedbb31d13e"
  }

  subnet_configuration {
    ipv4      = "***********"
    subnet_id = "subnet-015e4b2acb87091d9"
  }

  subnet_ids        = ["subnet-015e4b2acb87091d9", "subnet-023cafbedbb31d13e"]
  vpc_endpoint_type = "Interface"
  vpc_id            = "vpc-05e0e8104b3321c40"
}

resource "aws_vpc_endpoint" "tfer--vpce-025b48dac964f67b4" {
  dns_options {
    dns_record_ip_type                             = "ipv4"
    private_dns_only_for_inbound_resolver_endpoint = "false"
  }

  ip_address_type     = "ipv4"
  policy              = "{\"Statement\":[{\"Action\":\"*\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"*\"}]}"
  private_dns_enabled = "true"
  security_group_ids  = ["sg-066975c77a66a64d9"]
  service_name        = "com.amazonaws.ap-southeast-1.ec2messages"
  service_region      = "ap-southeast-1"

  subnet_configuration {
    ipv4      = "**********"
    subnet_id = "subnet-0a1802eb99e96cbed"
  }

  subnet_ids = ["subnet-0a1802eb99e96cbed"]

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-gen-facing-compartment-ec2messages-endpoint"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-gen-facing-compartment-ec2messages-endpoint"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_endpoint_type = "Interface"
  vpc_id            = "vpc-05e0e8104b3321c40"
}

resource "aws_vpc_endpoint" "tfer--vpce-03557a8cac26c8917" {
  dns_options {
    dns_record_ip_type                             = "ipv4"
    private_dns_only_for_inbound_resolver_endpoint = "false"
  }

  ip_address_type     = "ipv4"
  policy              = "{\"Statement\":[{\"Action\":\"*\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"*\"}]}"
  private_dns_enabled = "true"
  security_group_ids  = ["sg-0870c466d0db940c4"]
  service_name        = "com.amazonaws.ap-southeast-1.ec2messages"
  service_region      = "ap-southeast-1"

  subnet_configuration {
    ipv4      = "***********"
    subnet_id = "subnet-0dacd0ef604204014"
  }

  subnet_ids = ["subnet-0dacd0ef604204014"]

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "vpce-infovault-dev-Patching-Compartment-ec2messages"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "vpce-infovault-dev-Patching-Compartment-ec2messages"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_endpoint_type = "Interface"
  vpc_id            = "vpc-0ca0480cce82946b3"
}

resource "aws_vpc_endpoint" "tfer--vpce-0366281d6822bac46" {
  dns_options {
    dns_record_ip_type                             = "ipv4"
    private_dns_only_for_inbound_resolver_endpoint = "false"
  }

  ip_address_type     = "ipv4"
  policy              = "{\"Statement\":[{\"Action\":\"*\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"*\"}]}"
  private_dns_enabled = "true"
  security_group_ids  = ["sg-082d3fc2b26b1004e"]
  service_name        = "com.amazonaws.ap-southeast-1.ssm"
  service_region      = "ap-southeast-1"

  subnet_configuration {
    ipv4      = "*********"
    subnet_id = "subnet-0c6066202240c38be"
  }

  subnet_ids = ["subnet-0c6066202240c38be"]

  tags = {
    AutoShutdown    = "true"
    Compartment     = "ABLRHF"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-ablrhf-ssm-endpoint"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Compartment     = "ABLRHF"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-ablrhf-ssm-endpoint"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_endpoint_type = "Interface"
  vpc_id            = "vpc-0e75036a2ad9e5b4f"
}

resource "aws_vpc_endpoint" "tfer--vpce-04c5af7d63bb323fb" {
  dns_options {
    dns_record_ip_type                             = "ipv4"
    private_dns_only_for_inbound_resolver_endpoint = "false"
  }

  ip_address_type     = "ipv4"
  policy              = "{\"Statement\":[{\"Action\":\"*\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"*\"}]}"
  private_dns_enabled = "true"
  security_group_ids  = ["sg-00df94fe66c7464f5"]
  service_name        = "com.amazonaws.ap-southeast-1.ecr.dkr"
  service_region      = "ap-southeast-1"

  subnet_configuration {
    ipv4      = "***********"
    subnet_id = "subnet-07ab4b059d69e4044"
  }

  subnet_ids        = ["subnet-07ab4b059d69e4044"]
  vpc_endpoint_type = "Interface"
  vpc_id            = "vpc-05f1b20a6634c6a38"
}

resource "aws_vpc_endpoint" "tfer--vpce-04d5a7ce83abf07b5" {
  dns_options {
    dns_record_ip_type                             = "ipv4"
    private_dns_only_for_inbound_resolver_endpoint = "false"
  }

  ip_address_type     = "ipv4"
  policy              = "{\"Statement\":[{\"Action\":\"*\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"*\"}]}"
  private_dns_enabled = "true"
  security_group_ids  = ["sg-0d28b5ada1bb60c9a"]
  service_name        = "com.amazonaws.ap-southeast-1.ssmmessages"
  service_region      = "ap-southeast-1"

  subnet_configuration {
    ipv4      = "***********"
    subnet_id = "subnet-04910ced717a5584a"
  }

  subnet_configuration {
    ipv4      = "**********0"
    subnet_id = "subnet-0ebbc016c2fc6b3db"
  }

  subnet_ids = ["subnet-04910ced717a5584a", "subnet-0ebbc016c2fc6b3db"]

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-ssmmessages-endpoint"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-ssmmessages-endpoint"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_endpoint_type = "Interface"
  vpc_id            = "vpc-05f1b20a6634c6a38"
}

resource "aws_vpc_endpoint" "tfer--vpce-0538ad39973dc4aae" {
  dns_options {
    dns_record_ip_type                             = "ipv4"
    private_dns_only_for_inbound_resolver_endpoint = "false"
  }

  ip_address_type     = "ipv4"
  policy              = "{\"Statement\":[{\"Action\":\"*\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"*\"}]}"
  private_dns_enabled = "false"
  security_group_ids  = ["sg-066975c77a66a64d9"]
  service_name        = "com.amazonaws.ap-southeast-1.ec2messages"
  service_region      = "ap-southeast-1"

  subnet_configuration {
    ipv4      = "**********"
    subnet_id = "subnet-0a1802eb99e96cbed"
  }

  subnet_ids        = ["subnet-0a1802eb99e96cbed"]
  vpc_endpoint_type = "Interface"
  vpc_id            = "vpc-05e0e8104b3321c40"
}

resource "aws_vpc_endpoint" "tfer--vpce-0657101f2b067804d" {
  policy              = "{\"Statement\":[{\"Action\":\"*\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"*\"}],\"Version\":\"2008-10-17\"}"
  private_dns_enabled = "false"
  route_table_ids     = ["rtb-089ed5e648707e2d9", "rtb-0aa2547a225d317bd"]
  service_name        = "com.amazonaws.ap-southeast-1.s3"
  service_region      = "ap-southeast-1"

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-s3-endpoint"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-s3-endpoint"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_endpoint_type = "Gateway"
  vpc_id            = "vpc-0e75036a2ad9e5b4f"
}

resource "aws_vpc_endpoint" "tfer--vpce-06c55fec3359d3a98" {
  policy              = "{\"Statement\":[{\"Action\":\"*\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"*\"}],\"Version\":\"2008-10-17\"}"
  private_dns_enabled = "false"
  route_table_ids     = ["rtb-015ae5889ca4aaadd", "rtb-038d861f618d80392", "rtb-04dea60d5aeca9def", "rtb-06ca622706fb0676d", "rtb-0795b2e3d3e3f88ed", "rtb-08edcd5b7304aa2aa", "rtb-098958cd03a46c219", "rtb-0a3ed8d3c391e4cca", "rtb-0c7da122446383cc5", "rtb-0d508af52f5965f6a", "rtb-0e47b2c134f9ec024", "rtb-0f6307e8c214b6a71"]
  service_name        = "com.amazonaws.ap-southeast-1.s3"
  service_region      = "ap-southeast-1"
  vpc_endpoint_type   = "Gateway"
  vpc_id              = "vpc-05e0e8104b3321c40"
}

resource "aws_vpc_endpoint" "tfer--vpce-084596619ed02fac1" {
  dns_options {
    dns_record_ip_type                             = "ipv4"
    private_dns_only_for_inbound_resolver_endpoint = "false"
  }

  ip_address_type     = "ipv4"
  policy              = "{\"Statement\":[{\"Action\":\"*\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"*\"}]}"
  private_dns_enabled = "true"
  security_group_ids  = ["sg-0870c466d0db940c4"]
  service_name        = "com.amazonaws.ap-southeast-1.ssm"
  service_region      = "ap-southeast-1"

  subnet_configuration {
    ipv4      = "**********"
    subnet_id = "subnet-0dacd0ef604204014"
  }

  subnet_ids = ["subnet-0dacd0ef604204014"]

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "vpce-infovault-dev-Patching-Compartment-ssm"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "vpce-infovault-dev-Patching-Compartment-ssm"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_endpoint_type = "Interface"
  vpc_id            = "vpc-0ca0480cce82946b3"
}

resource "aws_vpc_endpoint" "tfer--vpce-08c51d0d6582b0aa6" {
  dns_options {
    dns_record_ip_type                             = "ipv4"
    private_dns_only_for_inbound_resolver_endpoint = "false"
  }

  ip_address_type     = "ipv4"
  policy              = "{\"Statement\":[{\"Action\":\"*\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"*\"}]}"
  private_dns_enabled = "true"
  security_group_ids  = ["sg-066975c77a66a64d9"]
  service_name        = "com.amazonaws.ap-southeast-1.ssmmessages"
  service_region      = "ap-southeast-1"

  subnet_configuration {
    ipv4      = "**********"
    subnet_id = "subnet-0a1802eb99e96cbed"
  }

  subnet_ids = ["subnet-0a1802eb99e96cbed"]

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-gen-facing-compartment-ssmmessages-endpoint"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-gen-facing-compartment-ssmmessages-endpoint"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_endpoint_type = "Interface"
  vpc_id            = "vpc-05e0e8104b3321c40"
}

resource "aws_vpc_endpoint" "tfer--vpce-094e78f395ad5f6f7" {
  dns_options {
    dns_record_ip_type                             = "ipv4"
    private_dns_only_for_inbound_resolver_endpoint = "false"
  }

  ip_address_type     = "ipv4"
  policy              = "{\"Statement\":[{\"Action\":\"*\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"*\"}]}"
  private_dns_enabled = "true"
  security_group_ids  = ["sg-066975c77a66a64d9"]
  service_name        = "com.amazonaws.ap-southeast-1.ssm"
  service_region      = "ap-southeast-1"

  subnet_configuration {
    ipv4      = "**********"
    subnet_id = "subnet-0a1802eb99e96cbed"
  }

  subnet_ids = ["subnet-0a1802eb99e96cbed"]

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-gen-facing-compartment-ssm-endpoint"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-gen-facing-compartment-ssm-endpoint"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_endpoint_type = "Interface"
  vpc_id            = "vpc-05e0e8104b3321c40"
}

resource "aws_vpc_endpoint" "tfer--vpce-099452683bdb0a349" {
  dns_options {
    dns_record_ip_type                             = "ipv4"
    private_dns_only_for_inbound_resolver_endpoint = "false"
  }

  ip_address_type     = "ipv4"
  policy              = "{\"Statement\":[{\"Action\":\"*\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"*\"}]}"
  private_dns_enabled = "true"
  security_group_ids  = ["sg-05f09a0dc7ab8c53f"]
  service_name        = "com.amazonaws.ap-southeast-1.ecr.dkr"
  service_region      = "ap-southeast-1"

  subnet_configuration {
    ipv4      = "***********"
    subnet_id = "subnet-023cafbedbb31d13e"
  }

  subnet_configuration {
    ipv4      = "************"
    subnet_id = "subnet-015e4b2acb87091d9"
  }

  subnet_ids        = ["subnet-015e4b2acb87091d9", "subnet-023cafbedbb31d13e"]
  vpc_endpoint_type = "Interface"
  vpc_id            = "vpc-05e0e8104b3321c40"
}

resource "aws_vpc_endpoint" "tfer--vpce-0a2d395de8c81f0f9" {
  policy              = "{\"Statement\":[{\"Action\":\"*\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"*\"}],\"Version\":\"2008-10-17\"}"
  private_dns_enabled = "false"
  route_table_ids     = ["rtb-0445ba29313210ac5", "rtb-05df1c4be4e435f04"]
  service_name        = "com.amazonaws.ap-southeast-1.s3"
  service_region      = "ap-southeast-1"

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-s3-endpoint"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-s3-endpoint"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_endpoint_type = "Gateway"
  vpc_id            = "vpc-05f1b20a6634c6a38"
}

resource "aws_vpc_endpoint" "tfer--vpce-0aab6fc33b265ed8d" {
  ip_address_type     = "ipv4"
  private_dns_enabled = "false"
  service_name        = "com.amazonaws.vpce.ap-southeast-1.vpce-svc-00e0c13e3d8260a6f"
  service_region      = "ap-southeast-1"

  subnet_configuration {
    ipv4      = "***********"
    subnet_id = "subnet-0aec855b39a7be02e"
  }

  subnet_ids = ["subnet-0aec855b39a7be02e"]

  tags = {
    AWSNetworkFirewallManaged = "true"
    Firewall                  = "arn:aws:network-firewall:ap-southeast-1:************:firewall/dev-net-anfw"
    Name                      = "dev-net-anfw (ap-southeast-1a)"
  }

  tags_all = {
    AWSNetworkFirewallManaged = "true"
    Firewall                  = "arn:aws:network-firewall:ap-southeast-1:************:firewall/dev-net-anfw"
    Name                      = "dev-net-anfw (ap-southeast-1a)"
  }

  vpc_endpoint_type = "GatewayLoadBalancer"
  vpc_id            = "vpc-037f619602fa293a1"
}

resource "aws_vpc_endpoint" "tfer--vpce-0ac5502093940b08f" {
  dns_options {
    dns_record_ip_type                             = "ipv4"
    private_dns_only_for_inbound_resolver_endpoint = "false"
  }

  ip_address_type     = "ipv4"
  policy              = "{\"Statement\":[{\"Action\":\"*\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"*\"}]}"
  private_dns_enabled = "true"
  security_group_ids  = ["sg-082d3fc2b26b1004e"]
  service_name        = "com.amazonaws.ap-southeast-1.ssmmessages"
  service_region      = "ap-southeast-1"

  subnet_configuration {
    ipv4      = "*********"
    subnet_id = "subnet-0c6066202240c38be"
  }

  subnet_ids = ["subnet-0c6066202240c38be"]

  tags = {
    AutoShutdown    = "true"
    Compartment     = "ABLRHF"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-ablrhf-ssmmessages-endpoint"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Compartment     = "ABLRHF"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-ablrhf-ssmmessages-endpoint"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_endpoint_type = "Interface"
  vpc_id            = "vpc-0e75036a2ad9e5b4f"
}

resource "aws_vpc_endpoint" "tfer--vpce-0bf3a106d192c71cd" {
  dns_options {
    dns_record_ip_type                             = "ipv4"
    private_dns_only_for_inbound_resolver_endpoint = "false"
  }

  ip_address_type     = "ipv4"
  policy              = "{\"Statement\":[{\"Action\":\"*\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"*\"},{\"Action\":\"*\",\"Condition\":{\"StringNotEquals\":{\"aws:PrincipalAccount\":\"************\"}},\"Effect\":\"Deny\",\"Principal\":\"*\",\"Resource\":\"*\"}],\"Version\":\"2012-10-17\"}"
  private_dns_enabled = "true"
  security_group_ids  = ["sg-0455dd83ca1b155d3"]
  service_name        = "com.amazonaws.ap-southeast-1.guardduty-data"
  service_region      = "ap-southeast-1"

  subnet_configuration {
    ipv4      = "***********"
    subnet_id = "subnet-01e871c49dc4b9b1f"
  }

  subnet_ids = ["subnet-01e871c49dc4b9b1f"]

  tags = {
    GuardDutyManaged = "true"
  }

  tags_all = {
    GuardDutyManaged = "true"
  }

  vpc_endpoint_type = "Interface"
  vpc_id            = "vpc-037f619602fa293a1"
}

resource "aws_vpc_endpoint" "tfer--vpce-0c5ff88c30a3e428d" {
  dns_options {
    dns_record_ip_type                             = "ipv4"
    private_dns_only_for_inbound_resolver_endpoint = "false"
  }

  ip_address_type     = "ipv4"
  policy              = "{\"Statement\":[{\"Action\":\"*\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"*\"},{\"Action\":\"*\",\"Condition\":{\"StringNotEquals\":{\"aws:PrincipalAccount\":\"************\"}},\"Effect\":\"Deny\",\"Principal\":\"*\",\"Resource\":\"*\"}],\"Version\":\"2012-10-17\"}"
  private_dns_enabled = "true"
  security_group_ids  = ["sg-065d1e24d43126489"]
  service_name        = "com.amazonaws.ap-southeast-1.guardduty-data"
  service_region      = "ap-southeast-1"

  subnet_configuration {
    ipv4      = "***********"
    subnet_id = "subnet-07ab4b059d69e4044"
  }

  subnet_configuration {
    ipv4      = "***********"
    subnet_id = "subnet-04910ced717a5584a"
  }

  subnet_ids = ["subnet-04910ced717a5584a", "subnet-07ab4b059d69e4044"]

  tags = {
    GuardDutyManaged = "true"
  }

  tags_all = {
    GuardDutyManaged = "true"
  }

  vpc_endpoint_type = "Interface"
  vpc_id            = "vpc-05f1b20a6634c6a38"
}

resource "aws_vpc_endpoint" "tfer--vpce-0c7798e909bd72ff8" {
  dns_options {
    dns_record_ip_type                             = "ipv4"
    private_dns_only_for_inbound_resolver_endpoint = "false"
  }

  ip_address_type     = "ipv4"
  policy              = "{\"Statement\":[{\"Action\":\"*\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"*\"}]}"
  private_dns_enabled = "true"
  security_group_ids  = ["sg-05f09a0dc7ab8c53f"]
  service_name        = "com.amazonaws.ap-southeast-1.eks"
  service_region      = "ap-southeast-1"

  subnet_configuration {
    ipv4      = "***********"
    subnet_id = "subnet-023cafbedbb31d13e"
  }

  subnet_configuration {
    ipv4      = "************"
    subnet_id = "subnet-015e4b2acb87091d9"
  }

  subnet_ids        = ["subnet-015e4b2acb87091d9", "subnet-023cafbedbb31d13e"]
  vpc_endpoint_type = "Interface"
  vpc_id            = "vpc-05e0e8104b3321c40"
}

resource "aws_vpc_endpoint" "tfer--vpce-0cb9adf21ebb65e3b" {
  policy              = "{\"Statement\":[{\"Action\":\"*\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"*\"}],\"Version\":\"2012-10-17\"}"
  private_dns_enabled = "false"
  route_table_ids     = ["rtb-02a9859d16aa6e04b", "rtb-048f93d202df7cd40", "rtb-09e7342b3a3bc5507"]
  service_name        = "com.amazonaws.ap-southeast-1.s3"
  service_region      = "ap-southeast-1"
  vpc_endpoint_type   = "Gateway"
  vpc_id              = "vpc-02282d8d4c9c7764c"
}

resource "aws_vpc_endpoint" "tfer--vpce-0cd55ff1d8ac0f61f" {
  dns_options {
    dns_record_ip_type                             = "ipv4"
    private_dns_only_for_inbound_resolver_endpoint = "false"
  }

  ip_address_type     = "ipv4"
  policy              = "{\"Statement\":[{\"Action\":\"*\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"*\"}]}"
  private_dns_enabled = "true"
  security_group_ids  = ["sg-0d28b5ada1bb60c9a"]
  service_name        = "com.amazonaws.ap-southeast-1.ssm"
  service_region      = "ap-southeast-1"

  subnet_configuration {
    ipv4      = "***********"
    subnet_id = "subnet-04910ced717a5584a"
  }

  subnet_configuration {
    ipv4      = "***********"
    subnet_id = "subnet-0ebbc016c2fc6b3db"
  }

  subnet_ids = ["subnet-04910ced717a5584a", "subnet-0ebbc016c2fc6b3db"]

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-ssm-endpoint"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-ssm-endpoint"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_endpoint_type = "Interface"
  vpc_id            = "vpc-05f1b20a6634c6a38"
}

resource "aws_vpc_endpoint" "tfer--vpce-0ce3dc776f7700c04" {
  dns_options {
    dns_record_ip_type                             = "ipv4"
    private_dns_only_for_inbound_resolver_endpoint = "false"
  }

  ip_address_type     = "ipv4"
  policy              = "{\"Statement\":[{\"Action\":\"*\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"*\"}]}"
  private_dns_enabled = "true"
  security_group_ids  = ["sg-0d28b5ada1bb60c9a"]
  service_name        = "com.amazonaws.ap-southeast-1.ec2messages"
  service_region      = "ap-southeast-1"

  subnet_configuration {
    ipv4      = "***********"
    subnet_id = "subnet-04910ced717a5584a"
  }

  subnet_configuration {
    ipv4      = "***********"
    subnet_id = "subnet-0ebbc016c2fc6b3db"
  }

  subnet_ids = ["subnet-04910ced717a5584a", "subnet-0ebbc016c2fc6b3db"]

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-ec2messages-endpoint"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-ec2messages-endpoint"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_endpoint_type = "Interface"
  vpc_id            = "vpc-05f1b20a6634c6a38"
}

resource "aws_vpc_endpoint" "tfer--vpce-0d5ebafd2bcf055bf" {
  dns_options {
    dns_record_ip_type                             = "ipv4"
    private_dns_only_for_inbound_resolver_endpoint = "false"
  }

  ip_address_type     = "ipv4"
  policy              = "{\"Statement\":[{\"Action\":\"*\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"*\"},{\"Action\":\"*\",\"Condition\":{\"StringNotEquals\":{\"aws:PrincipalAccount\":\"************\"}},\"Effect\":\"Deny\",\"Principal\":\"*\",\"Resource\":\"*\"}],\"Version\":\"2012-10-17\"}"
  private_dns_enabled = "true"
  security_group_ids  = ["sg-0650a95e2c0c1aaf5"]
  service_name        = "com.amazonaws.ap-southeast-1.guardduty-data"
  service_region      = "ap-southeast-1"

  subnet_configuration {
    ipv4      = "**********"
    subnet_id = "subnet-0a1802eb99e96cbed"
  }

  subnet_ids = ["subnet-0a1802eb99e96cbed"]

  tags = {
    GuardDutyManaged = "true"
  }

  tags_all = {
    GuardDutyManaged = "true"
  }

  vpc_endpoint_type = "Interface"
  vpc_id            = "vpc-05e0e8104b3321c40"
}

resource "aws_vpc_endpoint" "tfer--vpce-0ece27853f422d3a6" {
  dns_options {
    dns_record_ip_type                             = "ipv4"
    private_dns_only_for_inbound_resolver_endpoint = "false"
  }

  ip_address_type     = "ipv4"
  policy              = "{\"Statement\":[{\"Action\":\"*\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"*\"}]}"
  private_dns_enabled = "true"
  security_group_ids  = ["sg-082d3fc2b26b1004e"]
  service_name        = "com.amazonaws.ap-southeast-1.ec2messages"
  service_region      = "ap-southeast-1"

  subnet_configuration {
    ipv4      = "*********"
    subnet_id = "subnet-0c6066202240c38be"
  }

  subnet_ids = ["subnet-0c6066202240c38be"]

  tags = {
    AutoShutdown    = "true"
    Compartment     = "ABLRHF"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-ablrhf-ec2messages-endpoint"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Compartment     = "ABLRHF"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-ablrhf-ec2messages-endpoint"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_endpoint_type = "Interface"
  vpc_id            = "vpc-0e75036a2ad9e5b4f"
}

resource "aws_vpc_endpoint" "tfer--vpce-0ed0b1bdb378589bc" {
  dns_options {
    dns_record_ip_type                             = "ipv4"
    private_dns_only_for_inbound_resolver_endpoint = "false"
  }

  ip_address_type     = "ipv4"
  policy              = "{\"Statement\":[{\"Action\":\"*\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"*\"}]}"
  private_dns_enabled = "true"
  security_group_ids  = ["sg-05f09a0dc7ab8c53f"]
  service_name        = "com.amazonaws.ap-southeast-1.ec2"
  service_region      = "ap-southeast-1"

  subnet_configuration {
    ipv4      = "************"
    subnet_id = "subnet-023cafbedbb31d13e"
  }

  subnet_configuration {
    ipv4      = "************"
    subnet_id = "subnet-015e4b2acb87091d9"
  }

  subnet_ids        = ["subnet-015e4b2acb87091d9", "subnet-023cafbedbb31d13e"]
  vpc_endpoint_type = "Interface"
  vpc_id            = "vpc-05e0e8104b3321c40"
}

resource "aws_vpc_endpoint" "tfer--vpce-0f0b9eaa555525680" {
  dns_options {
    dns_record_ip_type                             = "ipv4"
    private_dns_only_for_inbound_resolver_endpoint = "false"
  }

  ip_address_type     = "ipv4"
  policy              = "{\"Statement\":[{\"Action\":\"*\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"*\"}]}"
  private_dns_enabled = "false"
  security_group_ids  = ["sg-066975c77a66a64d9"]
  service_name        = "com.amazonaws.ap-southeast-1.ssm"
  service_region      = "ap-southeast-1"

  subnet_configuration {
    ipv4      = "**********"
    subnet_id = "subnet-0a1802eb99e96cbed"
  }

  subnet_ids        = ["subnet-0a1802eb99e96cbed"]
  vpc_endpoint_type = "Interface"
  vpc_id            = "vpc-05e0e8104b3321c40"
}

resource "aws_vpc_endpoint" "tfer--vpce-0f629db35d2992350" {
  dns_options {
    dns_record_ip_type                             = "ipv4"
    private_dns_only_for_inbound_resolver_endpoint = "false"
  }

  ip_address_type     = "ipv4"
  policy              = "{\"Statement\":[{\"Action\":\"*\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"*\"},{\"Action\":\"*\",\"Condition\":{\"StringNotEquals\":{\"aws:PrincipalAccount\":\"************\"}},\"Effect\":\"Deny\",\"Principal\":\"*\",\"Resource\":\"*\"}],\"Version\":\"2012-10-17\"}"
  private_dns_enabled = "true"
  security_group_ids  = ["sg-0330a127b43c54ab2"]
  service_name        = "com.amazonaws.ap-southeast-1.guardduty-data"
  service_region      = "ap-southeast-1"

  subnet_configuration {
    ipv4      = "***********"
    subnet_id = "subnet-0dacd0ef604204014"
  }

  subnet_ids = ["subnet-0dacd0ef604204014"]

  tags = {
    GuardDutyManaged = "true"
  }

  tags_all = {
    GuardDutyManaged = "true"
  }

  vpc_endpoint_type = "Interface"
  vpc_id            = "vpc-0ca0480cce82946b3"
}

resource "aws_vpc_endpoint" "tfer--vpce-0fe5ae48246ae8a9d" {
  dns_options {
    dns_record_ip_type                             = "ipv4"
    private_dns_only_for_inbound_resolver_endpoint = "false"
  }

  ip_address_type     = "ipv4"
  policy              = "{\"Statement\":[{\"Action\":\"*\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"*\"}]}"
  private_dns_enabled = "true"
  security_group_ids  = ["sg-00df94fe66c7464f5"]
  service_name        = "com.amazonaws.ap-southeast-1.ecr.api"
  service_region      = "ap-southeast-1"

  subnet_configuration {
    ipv4      = "**********"
    subnet_id = "subnet-07ab4b059d69e4044"
  }

  subnet_ids        = ["subnet-07ab4b059d69e4044"]
  vpc_endpoint_type = "Interface"
  vpc_id            = "vpc-05f1b20a6634c6a38"
}
