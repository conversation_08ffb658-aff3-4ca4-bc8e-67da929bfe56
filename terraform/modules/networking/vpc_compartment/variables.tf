# VPC Compartment Module Variables

# VPC Configuration
variable "vpc_cidr" {
  description = "CIDR block for the VPC"
  type        = string
}

variable "vpc_name" {
  description = "Name for the VPC"
  type        = string
}

variable "enable_dns_hostnames" {
  description = "Enable DNS hostnames in the VPC"
  type        = bool
  default     = true
}

variable "enable_dns_support" {
  description = "Enable DNS support in the VPC"
  type        = bool
  default     = true
}

variable "enable_network_address_usage_metrics" {
  description = "Enable network address usage metrics"
  type        = bool
  default     = false
}

variable "instance_tenancy" {
  description = "Instance tenancy for the VPC"
  type        = string
  default     = "default"
}

variable "create_internet_gateway" {
  description = "Whether to create an internet gateway"
  type        = bool
  default     = true
}

variable "secondary_cidr_blocks" {
  description = "List of secondary CIDR blocks for the VPC"
  type        = list(string)
  default     = []
}

# Public Subnets
variable "public_subnets" {
  description = "Map of public subnet configurations"
  type = map(object({
    cidr_block                      = string
    availability_zone               = string
    map_public_ip_on_launch         = bool
    assign_ipv6_address_on_creation = bool
    name                            = string
  }))
  default = {}
}

# Private Subnets
variable "private_subnets" {
  description = "Map of private subnet configurations"
  type = map(object({
    cidr_block                      = string
    availability_zone               = string
    map_public_ip_on_launch         = bool
    assign_ipv6_address_on_creation = bool
    name                            = string
  }))
  default = {}
}

# NAT Gateways
variable "nat_gateways" {
  description = "Map of NAT gateway configurations"
  type = map(object({
    name       = string
    subnet_key = string
  }))
  default = {}
}

# Route Tables
variable "public_route_tables" {
  description = "Map of public route table configurations"
  type = map(object({
    name = string
  }))
  default = {}
}

variable "private_route_tables" {
  description = "Map of private route table configurations"
  type = map(object({
    name = string
  }))
  default = {}
}

# Route Table Associations
variable "public_subnet_route_associations" {
  description = "Map of public subnet to route table associations"
  type = map(object({
    subnet_key      = string
    route_table_key = string
  }))
  default = {}
}

variable "private_subnet_route_associations" {
  description = "Map of private subnet to route table associations"
  type = map(object({
    subnet_key      = string
    route_table_key = string
  }))
  default = {}
}

# NAT Routes
variable "private_nat_routes" {
  description = "Map of private routes to NAT gateways"
  type = map(object({
    route_table_key        = string
    destination_cidr_block = string
    nat_gateway_key        = string
  }))
  default = {}
}

# VPC Peering Routes
variable "vpc_peering_routes" {
  description = "Map of VPC peering routes"
  type = map(object({
    route_table_key           = string
    route_table_type          = string # "public" or "private"
    destination_cidr_block    = string
    vpc_peering_connection_id = string
  }))
  default = {}
}

# Security Groups
variable "security_groups" {
  description = "Map of security group configurations"
  type = map(object({
    name_prefix = string
    description = string
    name        = string
  }))
  default = {}
}

# Security Group Rules
variable "security_group_ingress_rules" {
  description = "Map of security group ingress rules"
  type = map(object({
    security_group_key       = string
    from_port                = number
    to_port                  = number
    protocol                 = string
    cidr_blocks              = list(string)
    source_security_group_id = string
    description              = string
  }))
  default = {}
}

variable "security_group_egress_rules" {
  description = "Map of security group egress rules"
  type = map(object({
    security_group_key       = string
    from_port                = number
    to_port                  = number
    protocol                 = string
    cidr_blocks              = list(string)
    source_security_group_id = string
    description              = string
  }))
  default = {}
}

# Network ACLs
variable "network_acls" {
  description = "Map of network ACL configurations"
  type = map(object({
    name        = string
    subnet_keys = list(string)
  }))
  default = {}
}

variable "network_acl_ingress_rules" {
  description = "Map of network ACL ingress rules"
  type = map(object({
    network_acl_key = string
    rule_number     = number
    protocol        = string
    rule_action     = string
    cidr_block      = string
    from_port       = number
    to_port         = number
  }))
  default = {}
}

variable "network_acl_egress_rules" {
  description = "Map of network ACL egress rules"
  type = map(object({
    network_acl_key = string
    rule_number     = number
    protocol        = string
    rule_action     = string
    cidr_block      = string
    from_port       = number
    to_port         = number
  }))
  default = {}
}

# VPC Endpoints
variable "vpc_endpoints" {
  description = "Map of VPC endpoint configurations"
  type = map(object({
    name                = string
    service_name        = string
    vpc_endpoint_type   = string
    route_table_ids     = list(string)
    subnet_ids          = list(string)
    security_group_ids  = list(string)
    private_dns_enabled = bool
  }))
  default = {}
}

# Common Tags
variable "common_tags" {
  description = "Common tags for all resources"
  type        = map(string)
  default     = {}
}

