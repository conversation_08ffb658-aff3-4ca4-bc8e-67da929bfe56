# Generic VPC Compartment Module
# This module creates a complete VPC compartment with all networking components

terraform {
  required_version = ">= 1.3.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

# VPC
resource "aws_vpc" "main" {
  cidr_block                           = var.vpc_cidr
  enable_dns_hostnames                 = var.enable_dns_hostnames
  enable_dns_support                   = var.enable_dns_support
  enable_network_address_usage_metrics = var.enable_network_address_usage_metrics
  instance_tenancy                     = var.instance_tenancy

  tags = merge(var.common_tags, {
    Name = var.vpc_name
  })
}

# Secondary CIDR Blocks
resource "aws_vpc_ipv4_cidr_block_association" "secondary" {
  for_each = toset(var.secondary_cidr_blocks)

  vpc_id     = aws_vpc.main.id
  cidr_block = each.value
}

# Internet Gateway
resource "aws_internet_gateway" "main" {
  count = var.create_internet_gateway ? 1 : 0

  vpc_id = aws_vpc.main.id

  tags = merge(var.common_tags, {
    Name = "${var.vpc_name}-igw"
  })
}

# Subnets
resource "aws_subnet" "public" {
  for_each = var.public_subnets

  vpc_id                          = aws_vpc.main.id
  cidr_block                      = each.value.cidr_block
  availability_zone               = each.value.availability_zone
  map_public_ip_on_launch         = each.value.map_public_ip_on_launch
  assign_ipv6_address_on_creation = each.value.assign_ipv6_address_on_creation

  # Ensure secondary CIDR blocks are associated before creating subnets
  depends_on = [aws_vpc_ipv4_cidr_block_association.secondary]

  tags = merge(var.common_tags, {
    Name = each.value.name
    Type = "Public"
  })
}

resource "aws_subnet" "private" {
  for_each = var.private_subnets

  vpc_id                          = aws_vpc.main.id
  cidr_block                      = each.value.cidr_block
  availability_zone               = each.value.availability_zone
  map_public_ip_on_launch         = each.value.map_public_ip_on_launch
  assign_ipv6_address_on_creation = each.value.assign_ipv6_address_on_creation

  # Ensure secondary CIDR blocks are associated before creating subnets
  depends_on = [aws_vpc_ipv4_cidr_block_association.secondary]

  tags = merge(var.common_tags, {
    Name = each.value.name
    Type = "Private"
  })
}

# Elastic IPs for NAT Gateways
resource "aws_eip" "nat" {
  for_each = var.nat_gateways

  domain = "vpc"

  tags = merge(var.common_tags, {
    Name = "${var.vpc_name}-nat-eip-${each.key}"
  })

  depends_on = [aws_internet_gateway.main]
}

# NAT Gateways
resource "aws_nat_gateway" "main" {
  for_each = var.nat_gateways

  allocation_id = aws_eip.nat[each.key].id
  subnet_id     = aws_subnet.public[each.value.subnet_key].id

  tags = merge(var.common_tags, {
    Name = each.value.name
  })

  depends_on = [aws_internet_gateway.main]
}

# Route Tables
resource "aws_route_table" "public" {
  for_each = var.public_route_tables

  vpc_id = aws_vpc.main.id

  tags = merge(var.common_tags, {
    Name = each.value.name
    Type = "Public"
  })
}

resource "aws_route_table" "private" {
  for_each = var.private_route_tables

  vpc_id = aws_vpc.main.id

  tags = merge(var.common_tags, {
    Name = each.value.name
    Type = "Private"
  })
}

# Public Routes
resource "aws_route" "public_internet" {
  for_each = var.create_internet_gateway ? var.public_route_tables : {}

  route_table_id         = aws_route_table.public[each.key].id
  destination_cidr_block = "0.0.0.0/0"
  gateway_id             = aws_internet_gateway.main[0].id
}

# Private Routes to NAT Gateways
resource "aws_route" "private_nat" {
  for_each = var.private_nat_routes

  route_table_id         = aws_route_table.private[each.value.route_table_key].id
  destination_cidr_block = each.value.destination_cidr_block
  nat_gateway_id         = aws_nat_gateway.main[each.value.nat_gateway_key].id
}

# VPC Peering Routes
resource "aws_route" "vpc_peering" {
  for_each = var.vpc_peering_routes

  route_table_id            = each.value.route_table_type == "public" ? aws_route_table.public[each.value.route_table_key].id : aws_route_table.private[each.value.route_table_key].id
  destination_cidr_block    = each.value.destination_cidr_block
  vpc_peering_connection_id = each.value.vpc_peering_connection_id
}

# Route Table Associations
resource "aws_route_table_association" "public" {
  for_each = var.public_subnet_route_associations

  subnet_id      = aws_subnet.public[each.value.subnet_key].id
  route_table_id = aws_route_table.public[each.value.route_table_key].id
}

resource "aws_route_table_association" "private" {
  for_each = var.private_subnet_route_associations

  subnet_id      = aws_subnet.private[each.value.subnet_key].id
  route_table_id = aws_route_table.private[each.value.route_table_key].id
}

# Security Groups
resource "aws_security_group" "main" {
  for_each = var.security_groups

  name_prefix = each.value.name_prefix
  description = each.value.description
  vpc_id      = aws_vpc.main.id

  tags = merge(var.common_tags, {
    Name = each.value.name
  })
}

# Security Group Rules - Ingress
resource "aws_security_group_rule" "ingress" {
  for_each = var.security_group_ingress_rules

  type                     = "ingress"
  security_group_id        = aws_security_group.main[each.value.security_group_key].id
  from_port                = each.value.from_port
  to_port                  = each.value.to_port
  protocol                 = each.value.protocol
  cidr_blocks              = each.value.cidr_blocks
  source_security_group_id = each.value.source_security_group_id
  description              = each.value.description
}

# Security Group Rules - Egress
resource "aws_security_group_rule" "egress" {
  for_each = var.security_group_egress_rules

  type                     = "egress"
  security_group_id        = aws_security_group.main[each.value.security_group_key].id
  from_port                = each.value.from_port
  to_port                  = each.value.to_port
  protocol                 = each.value.protocol
  cidr_blocks              = each.value.cidr_blocks
  source_security_group_id = each.value.source_security_group_id
  description              = each.value.description
}

# Network ACLs
resource "aws_network_acl" "main" {
  for_each = var.network_acls

  vpc_id = aws_vpc.main.id
  subnet_ids = [for subnet_key in each.value.subnet_keys :
    contains(keys(var.public_subnets), subnet_key) ? aws_subnet.public[subnet_key].id : aws_subnet.private[subnet_key].id
  ]

  tags = merge(var.common_tags, {
    Name = each.value.name
  })
}

# Network ACL Rules - Ingress
resource "aws_network_acl_rule" "ingress" {
  for_each = var.network_acl_ingress_rules

  network_acl_id = aws_network_acl.main[each.value.network_acl_key].id
  rule_number    = each.value.rule_number
  protocol       = each.value.protocol
  rule_action    = each.value.rule_action
  cidr_block     = each.value.cidr_block
  from_port      = each.value.from_port
  to_port        = each.value.to_port
}

# Network ACL Rules - Egress
resource "aws_network_acl_rule" "egress" {
  for_each = var.network_acl_egress_rules

  network_acl_id = aws_network_acl.main[each.value.network_acl_key].id
  rule_number    = each.value.rule_number
  protocol       = each.value.protocol
  rule_action    = each.value.rule_action
  cidr_block     = each.value.cidr_block
  from_port      = each.value.from_port
  to_port        = each.value.to_port
}

# VPC Endpoints
resource "aws_vpc_endpoint" "main" {
  for_each = var.vpc_endpoints

  vpc_id              = aws_vpc.main.id
  service_name        = each.value.service_name
  vpc_endpoint_type   = each.value.vpc_endpoint_type
  route_table_ids     = each.value.route_table_ids
  subnet_ids          = each.value.subnet_ids
  security_group_ids  = each.value.security_group_ids
  private_dns_enabled = each.value.private_dns_enabled

  tags = merge(var.common_tags, {
    Name = each.value.name
  })
}

