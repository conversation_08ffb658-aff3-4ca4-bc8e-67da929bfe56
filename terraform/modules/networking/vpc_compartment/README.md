# VPC Compartment Module

This module creates a complete VPC compartment with all networking components including VPC, subnets, internet gateway, NAT gateways, route tables, security groups, network ACLs, and VPC endpoints.

## Features

- **VPC**: Creates a VPC with configurable CIDR block and DNS settings
- **Subnets**: Creates public and private subnets across multiple availability zones
- **Internet Gateway**: Optional internet gateway for public internet access
- **NAT Gateways**: NAT gateways for private subnet internet access
- **Route Tables**: Public and private route tables with configurable routes
- **Security Groups**: Security groups with configurable ingress and egress rules
- **Network ACLs**: Network ACLs with configurable rules for additional security
- **VPC Endpoints**: VPC endpoints for private access to AWS services
- **VPC Peering**: Support for VPC peering connection routes

## Usage

```hcl
module "vpc_compartment" {
  source = "../../../modules/networking/vpc_compartment"
  
  # VPC Configuration
  vpc_cidr = "10.0.0.0/16"
  vpc_name = "my-vpc"
  
  # Subnets
  public_subnets = {
    public_az1 = {
      cidr_block                      = "********/24"
      availability_zone               = "us-east-1a"
      map_public_ip_on_launch         = true
      assign_ipv6_address_on_creation = false
      name                            = "Public Subnet AZ1"
    }
  }
  
  private_subnets = {
    private_az1 = {
      cidr_block                      = "**********/24"
      availability_zone               = "us-east-1a"
      map_public_ip_on_launch         = false
      assign_ipv6_address_on_creation = false
      name                            = "Private Subnet AZ1"
    }
  }
  
  # Route Tables
  public_route_tables = {
    public_rt = {
      name = "Public Route Table"
    }
  }
  
  private_route_tables = {
    private_rt = {
      name = "Private Route Table"
    }
  }
  
  # Route Table Associations
  public_subnet_route_associations = {
    public_az1_assoc = {
      subnet_key      = "public_az1"
      route_table_key = "public_rt"
    }
  }
  
  private_subnet_route_associations = {
    private_az1_assoc = {
      subnet_key      = "private_az1"
      route_table_key = "private_rt"
    }
  }
  
  # NAT Gateways
  nat_gateways = {
    nat_az1 = {
      name       = "NAT Gateway AZ1"
      subnet_key = "public_az1"
    }
  }
  
  # NAT Routes
  private_nat_routes = {
    private_default = {
      route_table_key        = "private_rt"
      destination_cidr_block = "0.0.0.0/0"
      nat_gateway_key        = "nat_az1"
    }
  }
  
  # Common Tags
  common_tags = {
    Environment = "dev"
    Project     = "example"
    ManagedBy   = "Terraform"
  }
}
```

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| vpc_cidr | CIDR block for the VPC | `string` | n/a | yes |
| vpc_name | Name for the VPC | `string` | n/a | yes |
| enable_dns_hostnames | Enable DNS hostnames in the VPC | `bool` | `true` | no |
| enable_dns_support | Enable DNS support in the VPC | `bool` | `true` | no |
| enable_network_address_usage_metrics | Enable network address usage metrics | `bool` | `false` | no |
| instance_tenancy | Instance tenancy for the VPC | `string` | `"default"` | no |
| create_internet_gateway | Whether to create an internet gateway | `bool` | `true` | no |
| public_subnets | Map of public subnet configurations | `map(object)` | `{}` | no |
| private_subnets | Map of private subnet configurations | `map(object)` | `{}` | no |
| nat_gateways | Map of NAT gateway configurations | `map(object)` | `{}` | no |
| public_route_tables | Map of public route table configurations | `map(object)` | `{}` | no |
| private_route_tables | Map of private route table configurations | `map(object)` | `{}` | no |
| public_subnet_route_associations | Map of public subnet to route table associations | `map(object)` | `{}` | no |
| private_subnet_route_associations | Map of private subnet to route table associations | `map(object)` | `{}` | no |
| private_nat_routes | Map of private routes to NAT gateways | `map(object)` | `{}` | no |
| vpc_peering_routes | Map of VPC peering routes | `map(object)` | `{}` | no |
| security_groups | Map of security group configurations | `map(object)` | `{}` | no |
| security_group_ingress_rules | Map of security group ingress rules | `map(object)` | `{}` | no |
| security_group_egress_rules | Map of security group egress rules | `map(object)` | `{}` | no |
| network_acls | Map of network ACL configurations | `map(object)` | `{}` | no |
| network_acl_ingress_rules | Map of network ACL ingress rules | `map(object)` | `{}` | no |
| network_acl_egress_rules | Map of network ACL egress rules | `map(object)` | `{}` | no |
| vpc_endpoints | Map of VPC endpoint configurations | `map(object)` | `{}` | no |
| common_tags | Common tags for all resources | `map(string)` | `{}` | no |

## Outputs

| Name | Description |
|------|-------------|
| vpc_id | ID of the VPC |
| vpc_cidr_block | CIDR block of the VPC |
| vpc_arn | ARN of the VPC |
| internet_gateway_id | ID of the internet gateway |
| internet_gateway_arn | ARN of the internet gateway |
| public_subnet_ids | IDs of the public subnets |
| private_subnet_ids | IDs of the private subnets |
| public_subnet_arns | ARNs of the public subnets |
| private_subnet_arns | ARNs of the private subnets |
| public_subnet_cidr_blocks | CIDR blocks of the public subnets |
| private_subnet_cidr_blocks | CIDR blocks of the private subnets |
| nat_gateway_ids | IDs of the NAT gateways |
| nat_gateway_public_ips | Public IPs of the NAT gateways |
| elastic_ip_ids | IDs of the Elastic IPs for NAT gateways |
| elastic_ip_public_ips | Public IPs of the Elastic IPs |
| public_route_table_ids | IDs of the public route tables |
| private_route_table_ids | IDs of the private route tables |
| security_group_ids | IDs of the security groups |
| security_group_arns | ARNs of the security groups |
| network_acl_ids | IDs of the network ACLs |
| vpc_endpoint_ids | IDs of the VPC endpoints |
| vpc_endpoint_dns_entries | DNS entries of the VPC endpoints |

## Requirements

| Name | Version |
|------|---------|
| terraform | >= 1.3.0 |
| aws | ~> 5.0 |

## Providers

| Name | Version |
|------|---------|
| aws | ~> 5.0 |

## Resources

- aws_vpc
- aws_internet_gateway
- aws_subnet
- aws_eip
- aws_nat_gateway
- aws_route_table
- aws_route
- aws_route_table_association
- aws_security_group
- aws_security_group_rule
- aws_network_acl
- aws_network_acl_rule
- aws_vpc_endpoint

## Notes

- This module is designed to be flexible and reusable across different VPC compartments
- All resources are created using maps/objects to allow for dynamic configuration
- Security groups are created with name_prefix to avoid naming conflicts
- VPC peering routes can be configured for inter-VPC connectivity
- Network ACLs provide an additional layer of security beyond security groups
- VPC endpoints enable private access to AWS services without internet gateway

