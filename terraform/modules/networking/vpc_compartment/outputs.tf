# VPC Compartment Module Outputs

# VPC Outputs
output "vpc_id" {
  description = "ID of the VPC"
  value       = aws_vpc.main.id
}

output "vpc_cidr_block" {
  description = "CIDR block of the VPC"
  value       = aws_vpc.main.cidr_block
}

output "vpc_arn" {
  description = "ARN of the VPC"
  value       = aws_vpc.main.arn
}

# Internet Gateway Outputs
output "internet_gateway_id" {
  description = "ID of the internet gateway"
  value       = var.create_internet_gateway ? aws_internet_gateway.main[0].id : null
}

output "internet_gateway_arn" {
  description = "ARN of the internet gateway"
  value       = var.create_internet_gateway ? aws_internet_gateway.main[0].arn : null
}

# Subnet Outputs
output "public_subnet_ids" {
  description = "IDs of the public subnets"
  value       = { for k, v in aws_subnet.public : k => v.id }
}

output "private_subnet_ids" {
  description = "IDs of the private subnets"
  value       = { for k, v in aws_subnet.private : k => v.id }
}

output "public_subnet_arns" {
  description = "ARNs of the public subnets"
  value       = { for k, v in aws_subnet.public : k => v.arn }
}

output "private_subnet_arns" {
  description = "ARNs of the private subnets"
  value       = { for k, v in aws_subnet.private : k => v.arn }
}

output "public_subnet_cidr_blocks" {
  description = "CIDR blocks of the public subnets"
  value       = { for k, v in aws_subnet.public : k => v.cidr_block }
}

output "private_subnet_cidr_blocks" {
  description = "CIDR blocks of the private subnets"
  value       = { for k, v in aws_subnet.private : k => v.cidr_block }
}

# NAT Gateway Outputs
output "nat_gateway_ids" {
  description = "IDs of the NAT gateways"
  value       = { for k, v in aws_nat_gateway.main : k => v.id }
}

output "nat_gateway_public_ips" {
  description = "Public IPs of the NAT gateways"
  value       = { for k, v in aws_nat_gateway.main : k => v.public_ip }
}

output "elastic_ip_ids" {
  description = "IDs of the Elastic IPs for NAT gateways"
  value       = { for k, v in aws_eip.nat : k => v.id }
}

output "elastic_ip_public_ips" {
  description = "Public IPs of the Elastic IPs"
  value       = { for k, v in aws_eip.nat : k => v.public_ip }
}

# Route Table Outputs
output "public_route_table_ids" {
  description = "IDs of the public route tables"
  value       = { for k, v in aws_route_table.public : k => v.id }
}

output "private_route_table_ids" {
  description = "IDs of the private route tables"
  value       = { for k, v in aws_route_table.private : k => v.id }
}

# Security Group Outputs
output "security_group_ids" {
  description = "IDs of the security groups"
  value       = { for k, v in aws_security_group.main : k => v.id }
}

output "security_group_arns" {
  description = "ARNs of the security groups"
  value       = { for k, v in aws_security_group.main : k => v.arn }
}

# Network ACL Outputs
output "network_acl_ids" {
  description = "IDs of the network ACLs"
  value       = { for k, v in aws_network_acl.main : k => v.id }
}

# VPC Endpoint Outputs
output "vpc_endpoint_ids" {
  description = "IDs of the VPC endpoints"
  value       = { for k, v in aws_vpc_endpoint.main : k => v.id }
}

output "vpc_endpoint_dns_entries" {
  description = "DNS entries of the VPC endpoints"
  value       = { for k, v in aws_vpc_endpoint.main : k => v.dns_entry }
}

