# infovault-dev-Internet-facing-compartment Module

Generated by terraformer_vpc_fetch.py on 2025-06-09 13:17:22

## Overview

This module manages the infrastructure for VPC vpc-037f619602fa293a1 (infovault-dev-Internet-facing-compartment).

## Resources

The following AWS resources are managed by this module:
- VPC and networking components
- Subnets and route tables
- Security groups and NACLs
- Gateways and endpoints

## Usage

```hcl
module "Internet-facing-compartment" {
  source = "../../modules/networking/Internet-facing-compartment"
  
  environment  = var.environment
  common_tags  = var.common_tags
  
  # Add other variables as needed
}
```

## Generated Files

- `terraformer_generated/`: Raw output from Terraformer
- `main.tf`: Main resource definitions (to be organized)
- `variables.tf`: Input variables
- `outputs.tf`: Output values
- `versions.tf`: Provider version requirements

## Next Steps

1. Review and organize resources from `terraformer_generated/`
2. Add proper variable references
3. Define outputs
4. Test with `terraform plan`
