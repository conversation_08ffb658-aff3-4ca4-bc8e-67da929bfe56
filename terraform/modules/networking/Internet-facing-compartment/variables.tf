# VPC Module Variables
# Generated by terraformer_vpc_fetch.py

variable "common_tags" {
  description = "Common tags for all resources"
  type        = map(string)
  default     = {}
}

variable "environment" {
  description = "Environment name (dev, staging, prod)"
  type        = string
}

variable "vpc_cidr" {
  description = "CIDR block for the Internet Facing VPC"
  type        = string
  default     = "**********/27"
}

variable "vpc_name" {
  description = "Name for the Internet Facing VPC"
  type        = string
  default     = "infovault-dev-internet-facing"
}

variable "enable_dns_hostnames" {
  description = "Enable DNS hostnames in the VPC"
  type        = bool
  default     = true
}

variable "enable_dns_support" {
  description = "Enable DNS support in the VPC"
  type        = bool
  default     = true
}

variable "enable_network_address_usage_metrics" {
  description = "Enable network address usage metrics"
  type        = bool
  default     = false
}

variable "instance_tenancy" {
  description = "Instance tenancy for the VPC"
  type        = string
  default     = "default"
}

variable "public_subnets" {
  description = "Map of public subnets to create"
  type = map(object({
    cidr_block        = string
    availability_zone = string
    name              = string
  }))
  default = {}
}

variable "create_internet_gateway" {
  description = "Whether to create an Internet Gateway"
  type        = bool
  default     = true
}

variable "internet_gateway_name" {
  description = "Name for the Internet Gateway"
  type        = string
  default     = "infovault-dev-igw"
}

variable "public_route_table_name" {
  description = "Name for the public route table"
  type        = string
  default     = "infovault-dev-public-rt"
}
