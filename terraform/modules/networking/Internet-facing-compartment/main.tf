# VPC Module Main Configuration
# Internet Facing Compartment VPC

# VPC Resource
resource "aws_vpc" "main" {
  cidr_block                           = var.vpc_cidr
  enable_dns_hostnames                 = var.enable_dns_hostnames
  enable_dns_support                   = var.enable_dns_support
  enable_network_address_usage_metrics = var.enable_network_address_usage_metrics
  instance_tenancy                     = var.instance_tenancy

  tags = merge(var.common_tags, {
    Name        = var.vpc_name
    Environment = var.environment
    Type        = "InternetFacing"
    Module      = "networking"
  })
}

# Internet Gateway
resource "aws_internet_gateway" "main" {
  count = var.create_internet_gateway ? 1 : 0

  vpc_id = aws_vpc.main.id

  tags = merge(var.common_tags, {
    Name        = var.internet_gateway_name
    Environment = var.environment
    Type        = "InternetGateway"
    Module      = "networking"
  })
}

# Public Subnets
resource "aws_subnet" "public" {
  for_each = var.public_subnets

  vpc_id                  = aws_vpc.main.id
  cidr_block              = each.value.cidr_block
  availability_zone       = each.value.availability_zone
  map_public_ip_on_launch = true

  tags = merge(var.common_tags, {
    Name        = each.value.name
    Environment = var.environment
    Type        = "Public"
    Tier        = "Public"
    Module      = "networking"
    AZ          = each.value.availability_zone
  })
}

# Public Route Table
resource "aws_route_table" "public" {
  count = var.create_internet_gateway && length(var.public_subnets) > 0 ? 1 : 0

  vpc_id = aws_vpc.main.id

  tags = merge(var.common_tags, {
    Name        = var.public_route_table_name
    Environment = var.environment
    Type        = "Public"
    Module      = "networking"
  })
}

# Route to Internet Gateway
resource "aws_route" "public_internet" {
  count = var.create_internet_gateway && length(var.public_subnets) > 0 ? 1 : 0

  route_table_id         = aws_route_table.public[0].id
  destination_cidr_block = "0.0.0.0/0"
  gateway_id             = aws_internet_gateway.main[0].id

  depends_on = [aws_internet_gateway.main]
}

# Route Table Associations for Public Subnets
resource "aws_route_table_association" "public" {
  for_each = var.public_subnets

  subnet_id      = aws_subnet.public[each.key].id
  route_table_id = aws_route_table.public[0].id

  depends_on = [aws_route_table.public]
}
