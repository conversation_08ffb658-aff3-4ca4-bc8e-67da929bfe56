# Generated by Terraformer for VPC vpc-037f619602fa293a1
# Source: /Users/<USER>/Documents/Projects/infovault-infrastructure/terraform/modules/networking/Internet-facing-compartment/raw_terraformer/aws/eip/eip.tf
# Generated on: 2025-06-09 13:17:22

resource "aws_eip" "tfer--eipalloc-01c7e7d9a87bf6629" {
  domain               = "vpc"
  network_border_group = "ap-southeast-1"
  public_ipv4_pool     = "amazon"

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-app-db-nat-eip"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-app-db-nat-eip"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc = "true"
}

resource "aws_eip" "tfer--eipalloc-05b293b1c50d5ff91" {
  domain               = "vpc"
  network_border_group = "ap-southeast-1"
  network_interface    = "eni-0d6e441784201ed85"
  public_ipv4_pool     = "amazon"

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-nat-eip"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-nat-eip"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc = "true"
}

resource "aws_eip" "tfer--eipalloc-073d1259e09e3694d" {
  domain               = "vpc"
  network_border_group = "ap-southeast-1"
  network_interface    = "eni-066838eeb94b2abd3"
  public_ipv4_pool     = "amazon"

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-gen-facing-compartment-nat-eip"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-gen-facing-compartment-nat-eip"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc = "true"
}
