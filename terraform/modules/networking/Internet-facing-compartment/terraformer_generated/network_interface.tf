# Generated by Terraformer for VPC vpc-037f619602fa293a1
# Source: /Users/<USER>/Documents/Projects/infovault-infrastructure/terraform/modules/networking/Internet-facing-compartment/raw_terraformer/aws/eni/network_interface.tf
# Generated on: 2025-06-09 13:17:22

resource "aws_network_interface" "tfer--eni-0000fc2c82b0ad41e" {
  description        = "VPC Endpoint Interface vpce-0cd55ff1d8ac0f61f"
  interface_type     = "vpc_endpoint"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "**********4"
  private_ip_list    = ["**********4"]
  private_ips        = ["**********4"]
  private_ips_count  = "0"
  security_groups    = ["sg-0d28b5ada1bb60c9a"]
  source_dest_check  = "true"
  subnet_id          = "subnet-04910ced717a5584a"
}

resource "aws_network_interface" "tfer--eni-001322889d8fcc4d5" {
  description        = "ELB app/k8s-dev-albingre-ec7cdc5798/eae51a76a0223b29"
  interface_type     = "interface"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "************"
  private_ip_list    = ["************"]
  private_ips        = ["************"]
  private_ips_count  = "0"
  security_groups    = ["sg-0353836b1b373e27d", "sg-0950397297ecb203f"]
  source_dest_check  = "true"
  subnet_id          = "subnet-015e4b2acb87091d9"
}

resource "aws_network_interface" "tfer--eni-001dd84b73a7b022c" {
  description        = "ELB app/gen-alb-internet-facing/7cdf809c994f03cb"
  interface_type     = "interface"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "**********"
  private_ip_list    = ["**********"]
  private_ips        = ["**********"]
  private_ips_count  = "0"
  security_groups    = ["sg-0c08c4e2aabf88592"]
  source_dest_check  = "true"
  subnet_id          = "subnet-0e2a201f2503f59ee"
}

resource "aws_network_interface" "tfer--eni-0035a1c563125e610" {
  attachment {
    device_index = "0"
    instance     = "i-01a8b5842ff1bff6a"
  }

  interface_type     = "interface"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "***********"
  private_ip_list    = ["***********"]
  private_ips        = ["***********"]
  private_ips_count  = "0"
  security_groups    = ["sg-0d7508facd515c5ea"]
  source_dest_check  = "true"
  subnet_id          = "subnet-07ab4b059d69e4044"
}

resource "aws_network_interface" "tfer--eni-0039dd184b1ba0efb" {
  attachment {
    device_index = "0"
    instance     = "i-06f3f2b6a31516b31"
  }

  interface_type     = "interface"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "**********8"
  private_ip_list    = ["**********8"]
  private_ips        = ["**********8"]
  private_ips_count  = "0"
  security_groups    = ["sg-0511f8964219f7537"]
  source_dest_check  = "true"
  subnet_id          = "subnet-0ebbc016c2fc6b3db"
}

resource "aws_network_interface" "tfer--eni-00424d22e169cc12d" {
  description        = "ELB app/gen-alb-internet-facing/7cdf809c994f03cb"
  interface_type     = "interface"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "***********"
  private_ip_list    = ["***********"]
  private_ips        = ["***********"]
  private_ips_count  = "0"
  security_groups    = ["sg-0c08c4e2aabf88592"]
  source_dest_check  = "true"
  subnet_id          = "subnet-081b6eb841b4451b3"
}

resource "aws_network_interface" "tfer--eni-0196ea3e075caabb4" {
  description        = "VPC Endpoint Interface vpce-0cd55ff1d8ac0f61f"
  interface_type     = "vpc_endpoint"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "**********7"
  private_ip_list    = ["**********7"]
  private_ips        = ["**********7"]
  private_ips_count  = "0"
  security_groups    = ["sg-0d28b5ada1bb60c9a"]
  source_dest_check  = "true"
  subnet_id          = "subnet-0ebbc016c2fc6b3db"
}

resource "aws_network_interface" "tfer--eni-01983a4e02f2205aa" {
  description        = "VPC Endpoint Interface vpce-0ed0b1bdb378589bc"
  interface_type     = "vpc_endpoint"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "***********7"
  private_ip_list    = ["***********7"]
  private_ips        = ["***********7"]
  private_ips_count  = "0"
  security_groups    = ["sg-05f09a0dc7ab8c53f"]
  source_dest_check  = "true"
  subnet_id          = "subnet-015e4b2acb87091d9"
}

resource "aws_network_interface" "tfer--eni-01cda4f27be0d17ff" {
  description        = "VPC Endpoint Interface vpce-03557a8cac26c8917"
  interface_type     = "vpc_endpoint"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "***********"
  private_ip_list    = ["***********"]
  private_ips        = ["***********"]
  private_ips_count  = "0"
  security_groups    = ["sg-0870c466d0db940c4"]
  source_dest_check  = "true"
  subnet_id          = "subnet-0dacd0ef604204014"
}

resource "aws_network_interface" "tfer--eni-020e308911f0f67e6" {
  description        = "VPC Endpoint Interface vpce-084596619ed02fac1"
  interface_type     = "vpc_endpoint"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "**********"
  private_ip_list    = ["**********"]
  private_ips        = ["**********"]
  private_ips_count  = "0"
  security_groups    = ["sg-0870c466d0db940c4"]
  source_dest_check  = "true"
  subnet_id          = "subnet-0dacd0ef604204014"
}

resource "aws_network_interface" "tfer--eni-025ca62a2a6231a95" {
  attachment {
    device_index = "0"
    instance     = "i-0e9981c8b65f2dd81"
  }

  interface_type     = "interface"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "************"
  private_ip_list    = ["************"]
  private_ips        = ["************"]
  private_ips_count  = "0"
  security_groups    = ["sg-095c31fa3b94c41ae"]
  source_dest_check  = "true"
  subnet_id          = "subnet-023cafbedbb31d13e"
}

resource "aws_network_interface" "tfer--eni-02992b069ff5375fe" {
  description        = "ELB net/infovault-nlb/aa06b8f0bdcc828a"
  interface_type     = "network_load_balancer"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "***********"
  private_ip_list    = ["***********"]
  private_ips        = ["***********"]
  private_ips_count  = "0"
  source_dest_check  = "false"
  subnet_id          = "subnet-0c775a4f57dfa0e61"
}

resource "aws_network_interface" "tfer--eni-02c628803f0bb3480" {
  description        = "VPC Endpoint Interface vpce-099452683bdb0a349"
  interface_type     = "vpc_endpoint"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "************"
  private_ip_list    = ["************"]
  private_ips        = ["************"]
  private_ips_count  = "0"
  security_groups    = ["sg-05f09a0dc7ab8c53f"]
  source_dest_check  = "true"
  subnet_id          = "subnet-015e4b2acb87091d9"
}

resource "aws_network_interface" "tfer--eni-030e4634c1ca971d9" {
  description        = "VPC Endpoint Interface vpce-099452683bdb0a349"
  interface_type     = "vpc_endpoint"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "***********"
  private_ip_list    = ["***********"]
  private_ips        = ["***********"]
  private_ips_count  = "0"
  security_groups    = ["sg-05f09a0dc7ab8c53f"]
  source_dest_check  = "true"
  subnet_id          = "subnet-023cafbedbb31d13e"
}

resource "aws_network_interface" "tfer--eni-03394e0cbba7b7f65" {
  description        = "VPC Endpoint Interface vpce-0ed0b1bdb378589bc"
  interface_type     = "vpc_endpoint"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "************"
  private_ip_list    = ["************"]
  private_ips        = ["************"]
  private_ips_count  = "0"
  security_groups    = ["sg-05f09a0dc7ab8c53f"]
  source_dest_check  = "true"
  subnet_id          = "subnet-023cafbedbb31d13e"
}

resource "aws_network_interface" "tfer--eni-03622b623475ba86b" {
  attachment {
    device_index = "0"
    instance     = "i-0a68d4d8b08ad6fab"
  }

  interface_type     = "interface"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "*********"
  private_ip_list    = ["*********"]
  private_ips        = ["*********"]
  private_ips_count  = "0"
  security_groups    = ["sg-011e29a277dde9f4a"]
  source_dest_check  = "true"
  subnet_id          = "subnet-043dd23a18b80a280"
}

resource "aws_network_interface" "tfer--eni-03ac92a8fb4055b11" {
  attachment {
    device_index = "0"
    instance     = "i-0fd3c2460891779a9"
  }

  interface_type     = "interface"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "**********"
  private_ip_list    = ["**********"]
  private_ips        = ["**********"]
  private_ips_count  = "0"
  security_groups    = ["sg-00df94fe66c7464f5"]
  source_dest_check  = "true"
  subnet_id          = "subnet-07ab4b059d69e4044"
}

resource "aws_network_interface" "tfer--eni-045645a59befccdf5" {
  attachment {
    device_index = "0"
    instance     = "i-062061c311cde5ef0"
  }

  interface_type     = "interface"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "**********"
  private_ip_list    = ["**********"]
  private_ips        = ["**********"]
  private_ips_count  = "0"
  security_groups    = ["sg-0b67c6b7904908adf"]
  source_dest_check  = "true"
  subnet_id          = "subnet-07ab4b059d69e4044"
}

resource "aws_network_interface" "tfer--eni-0488dabede95ced8b" {
  description        = "VPC Endpoint Interface vpce-0aab6fc33b265ed8d"
  interface_type     = "gateway_load_balancer_endpoint"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "***********"
  private_ip_list    = ["***********"]
  private_ips        = ["***********"]
  private_ips_count  = "0"
  source_dest_check  = "false"
  subnet_id          = "subnet-0aec855b39a7be02e"
}

resource "aws_network_interface" "tfer--eni-04f8f5201882ec24e" {
  description        = "VPC Endpoint Interface vpce-0bf3a106d192c71cd"
  interface_type     = "vpc_endpoint"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "***********"
  private_ip_list    = ["***********"]
  private_ips        = ["***********"]
  private_ips_count  = "0"
  security_groups    = ["sg-0455dd83ca1b155d3"]
  source_dest_check  = "true"
  subnet_id          = "subnet-01e871c49dc4b9b1f"
}

resource "aws_network_interface" "tfer--eni-05073405d05bfbf47" {
  description        = "AWS created network interface for directory d-9667b2da13"
  interface_type     = "interface"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "***********"
  private_ip_list    = ["***********"]
  private_ips        = ["***********"]
  private_ips_count  = "0"
  security_groups    = ["sg-0f48a9bd414cfd781"]
  source_dest_check  = "true"
  subnet_id          = "subnet-04910ced717a5584a"
}

resource "aws_network_interface" "tfer--eni-056e371c87c8ead5f" {
  attachment {
    device_index = "0"
    instance     = "i-0a490aa59b0fb4688"
  }

  interface_type     = "interface"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "**********"
  private_ip_list    = ["**********"]
  private_ips        = ["**********"]
  private_ips_count  = "0"
  security_groups    = ["sg-0f2fdd192120dbf17"]
  source_dest_check  = "true"
  subnet_id          = "subnet-07ab4b059d69e4044"
}

resource "aws_network_interface" "tfer--eni-056fdfcc740d248fe" {
  attachment {
    device_index = "0"
    instance     = "i-0a9b2de4a3fc29869"
  }

  interface_type     = "interface"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "**********"
  private_ip_list    = ["**********"]
  private_ips        = ["**********"]
  private_ips_count  = "0"
  security_groups    = ["sg-095117b1a793c2ce0"]
  source_dest_check  = "true"
  subnet_id          = "subnet-07ab4b059d69e4044"
}

resource "aws_network_interface" "tfer--eni-05708b1c080c287b1" {
  description        = "VPC Endpoint Interface vpce-0c5ff88c30a3e428d"
  interface_type     = "vpc_endpoint"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "***********"
  private_ip_list    = ["***********"]
  private_ips        = ["***********"]
  private_ips_count  = "0"
  security_groups    = ["sg-065d1e24d43126489"]
  source_dest_check  = "true"
  subnet_id          = "subnet-04910ced717a5584a"
}

resource "aws_network_interface" "tfer--eni-0574685d488017b52" {
  description        = "AWS created network interface for directory d-9667b2da13"
  interface_type     = "interface"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "***********"
  private_ip_list    = ["***********"]
  private_ips        = ["***********"]
  private_ips_count  = "0"
  security_groups    = ["sg-0f48a9bd414cfd781"]
  source_dest_check  = "true"
  subnet_id          = "subnet-0ebbc016c2fc6b3db"
}

resource "aws_network_interface" "tfer--eni-058e6cbf33ccafc06" {
  description        = "ELB app/k8s-dev-albingre-ec7cdc5798/eae51a76a0223b29"
  interface_type     = "interface"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "************"
  private_ip_list    = ["************"]
  private_ips        = ["************"]
  private_ips_count  = "0"
  security_groups    = ["sg-0353836b1b373e27d", "sg-0950397297ecb203f"]
  source_dest_check  = "true"
  subnet_id          = "subnet-023cafbedbb31d13e"
}

resource "aws_network_interface" "tfer--eni-05b03ded8d9e22c98" {
  attachment {
    device_index = "0"
    instance     = "i-0617cabf61d1e4692"
  }

  interface_type     = "interface"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "***********"
  private_ip_list    = ["***********"]
  private_ips        = ["***********"]
  private_ips_count  = "0"
  security_groups    = ["sg-0950397297ecb203f"]
  source_dest_check  = "true"
  subnet_id          = "subnet-0d4f42cf30b20c42d"
}

resource "aws_network_interface" "tfer--eni-05d697cc2b6059220" {
  attachment {
    device_index = "0"
    instance     = "i-046cd06b02397e146"
  }

  interface_type     = "interface"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "**********"
  private_ip_list    = ["**********"]
  private_ips        = ["**********"]
  private_ips_count  = "0"
  security_groups    = ["sg-01f0c371bd11aa6f1"]
  source_dest_check  = "true"
  subnet_id          = "subnet-0dacd0ef604204014"
}

resource "aws_network_interface" "tfer--eni-05f08707c0961ac34" {
  description        = "VPC Endpoint Interface vpce-0fe5ae48246ae8a9d"
  interface_type     = "vpc_endpoint"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "**********"
  private_ip_list    = ["**********"]
  private_ips        = ["**********"]
  private_ips_count  = "0"
  security_groups    = ["sg-00df94fe66c7464f5"]
  source_dest_check  = "true"
  subnet_id          = "subnet-07ab4b059d69e4044"
}

resource "aws_network_interface" "tfer--eni-06081ded7801e8023" {
  attachment {
    device_index = "0"
    instance     = "i-0691943f24194b6ae"
  }

  interface_type     = "interface"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "***********"
  private_ip_list    = ["***********"]
  private_ips        = ["***********"]
  private_ips_count  = "0"
  security_groups    = ["sg-0950397297ecb203f"]
  source_dest_check  = "true"
  subnet_id          = "subnet-0d4f42cf30b20c42d"
}

resource "aws_network_interface" "tfer--eni-066838eeb94b2abd3" {
  description        = "Interface for NAT Gateway nat-0431bfe8618055bd8"
  interface_type     = "nat_gateway"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "**********"
  private_ip_list    = ["**********"]
  private_ips        = ["**********"]
  private_ips_count  = "0"
  source_dest_check  = "false"
  subnet_id          = "subnet-043dd23a18b80a280"
}

resource "aws_network_interface" "tfer--eni-06a248b1f83a2a3fb" {
  attachment {
    device_index = "0"
    instance     = "i-03ddfe5f32292e59d"
  }

  interface_type     = "interface"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "***********0"
  private_ip_list    = ["***********", "***********6", "***********0", "***********2", "************", "************", "************", "***********", "************", "************", "************", "***********1", "************", "************", "************", "************", "************", "************", "************", "***********", "***********", "**********", "**********3", "***********", "***********", "***********", "***********", "***********", "***********", "***********"]
  private_ips        = ["***********", "***********6", "***********0", "***********2", "************", "************", "************", "***********", "************", "************", "************", "***********1", "************", "************", "************", "************", "************", "************", "************", "***********", "***********", "**********", "**********3", "***********", "***********", "***********", "***********", "***********", "***********", "***********"]
  private_ips_count  = "29"
  security_groups    = ["sg-0353836b1b373e27d"]
  source_dest_check  = "true"
  subnet_id          = "subnet-015e4b2acb87091d9"

  tags = {
    "cluster.k8s.amazonaws.com/name"     = "infovault-dev-eks-cluster-v130"
    "eks:cluster-name"                   = "infovault-dev-eks-cluster-v130"
    "eks:nodegroup-name"                 = "v130-node-group-3"
    "node.k8s.amazonaws.com/instance_id" = "i-03ddfe5f32292e59d"
  }

  tags_all = {
    "cluster.k8s.amazonaws.com/name"     = "infovault-dev-eks-cluster-v130"
    "eks:cluster-name"                   = "infovault-dev-eks-cluster-v130"
    "eks:nodegroup-name"                 = "v130-node-group-3"
    "node.k8s.amazonaws.com/instance_id" = "i-03ddfe5f32292e59d"
  }
}

resource "aws_network_interface" "tfer--eni-0767288679be0f910" {
  attachment {
    device_index = "0"
    instance     = "i-060584b2a431698f9"
  }

  interface_type     = "interface"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "***********"
  private_ip_list    = ["***********"]
  private_ips        = ["***********"]
  private_ips_count  = "0"
  security_groups    = ["sg-0950397297ecb203f"]
  source_dest_check  = "true"
  subnet_id          = "subnet-0c775a4f57dfa0e61"
}

resource "aws_network_interface" "tfer--eni-0770cc77805897af3" {
  attachment {
    device_index = "1"
    instance     = "i-047eb48a87cdabc96"
  }

  description        = "aws-K8S-i-047eb48a87cdabc96"
  interface_type     = "interface"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "100.67.1.131"
  private_ip_list    = ["***********7", "100.67.1.117", "100.67.1.131", "100.67.1.135", "100.67.1.139", "100.67.1.142", "100.67.1.148", "100.67.1.149", "100.67.1.16", "100.67.1.164", "100.67.1.167", "100.67.1.169", "100.67.1.180", "100.67.1.188", "100.67.1.196", "100.67.1.204", "100.67.1.207", "100.67.1.21", "100.67.1.219", "100.67.1.222", "100.67.1.233", "100.67.1.250", "************", "***********", "***********", "***********", "***********", "**********3", "***********", "***********"]
  private_ips        = ["***********7", "100.67.1.117", "100.67.1.131", "100.67.1.135", "100.67.1.139", "100.67.1.142", "100.67.1.148", "100.67.1.149", "100.67.1.16", "100.67.1.164", "100.67.1.167", "100.67.1.169", "100.67.1.180", "100.67.1.188", "100.67.1.196", "100.67.1.204", "100.67.1.207", "100.67.1.21", "100.67.1.219", "100.67.1.222", "100.67.1.233", "100.67.1.250", "************", "***********", "***********", "***********", "***********", "**********3", "***********", "***********"]
  private_ips_count  = "29"
  security_groups    = ["sg-0353836b1b373e27d"]
  source_dest_check  = "true"
  subnet_id          = "subnet-023cafbedbb31d13e"

  tags = {
    "cluster.k8s.amazonaws.com/name"     = "infovault-dev-eks-cluster-v130"
    "node.k8s.amazonaws.com/createdAt"   = "2025-05-21T10:24:08Z"
    "node.k8s.amazonaws.com/instance_id" = "i-047eb48a87cdabc96"
  }

  tags_all = {
    "cluster.k8s.amazonaws.com/name"     = "infovault-dev-eks-cluster-v130"
    "node.k8s.amazonaws.com/createdAt"   = "2025-05-21T10:24:08Z"
    "node.k8s.amazonaws.com/instance_id" = "i-047eb48a87cdabc96"
  }
}

resource "aws_network_interface" "tfer--eni-07765172bcd661056" {
  description        = "VPC Endpoint Interface vpce-0538ad39973dc4aae"
  interface_type     = "vpc_endpoint"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "**********"
  private_ip_list    = ["**********"]
  private_ips        = ["**********"]
  private_ips_count  = "0"
  security_groups    = ["sg-066975c77a66a64d9"]
  source_dest_check  = "true"
  subnet_id          = "subnet-0a1802eb99e96cbed"
}

resource "aws_network_interface" "tfer--eni-07b95a8cc49b899ea" {
  description        = "VPC Endpoint Interface vpce-04d5a7ce83abf07b5"
  interface_type     = "vpc_endpoint"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "**********0"
  private_ip_list    = ["**********0"]
  private_ips        = ["**********0"]
  private_ips_count  = "0"
  security_groups    = ["sg-0d28b5ada1bb60c9a"]
  source_dest_check  = "true"
  subnet_id          = "subnet-0ebbc016c2fc6b3db"
}

resource "aws_network_interface" "tfer--eni-07e82bc1231965e15" {
  description        = "VPC Endpoint Interface vpce-0f0b9eaa555525680"
  interface_type     = "vpc_endpoint"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "**********"
  private_ip_list    = ["**********"]
  private_ips        = ["**********"]
  private_ips_count  = "0"
  security_groups    = ["sg-066975c77a66a64d9"]
  source_dest_check  = "true"
  subnet_id          = "subnet-0a1802eb99e96cbed"
}

resource "aws_network_interface" "tfer--eni-07f709056a9c94268" {
  description        = "VPC Endpoint Interface vpce-0d5ebafd2bcf055bf"
  interface_type     = "vpc_endpoint"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "**********"
  private_ip_list    = ["**********"]
  private_ips        = ["**********"]
  private_ips_count  = "0"
  security_groups    = ["sg-0650a95e2c0c1aaf5"]
  source_dest_check  = "true"
  subnet_id          = "subnet-0a1802eb99e96cbed"
}

resource "aws_network_interface" "tfer--eni-0811737db6ea8b294" {
  description        = "ELB app/k8s-dev-albingre-741d5ef5b1/071846906f6f3677"
  interface_type     = "interface"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "************"
  private_ip_list    = ["************"]
  private_ips        = ["************"]
  private_ips_count  = "0"
  security_groups    = ["sg-0353836b1b373e27d", "sg-0950397297ecb203f"]
  source_dest_check  = "true"
  subnet_id          = "subnet-023cafbedbb31d13e"
}

resource "aws_network_interface" "tfer--eni-082053ae8b1f25e1a" {
  description        = "ELB app/k8s-dev-albingre-741d5ef5b1/071846906f6f3677"
  interface_type     = "interface"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "***********"
  private_ip_list    = ["***********"]
  private_ips        = ["***********"]
  private_ips_count  = "0"
  security_groups    = ["sg-0353836b1b373e27d", "sg-0950397297ecb203f"]
  source_dest_check  = "true"
  subnet_id          = "subnet-015e4b2acb87091d9"
}

resource "aws_network_interface" "tfer--eni-082f6d6b35faea6c6" {
  attachment {
    device_index = "0"
    instance     = "i-0d619a369131c9627"
  }

  interface_type     = "interface"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "***********"
  private_ip_list    = ["***********"]
  private_ips        = ["***********"]
  private_ips_count  = "0"
  security_groups    = ["sg-02794d7f8eaa42fea"]
  source_dest_check  = "true"
  subnet_id          = "subnet-0c775a4f57dfa0e61"
}

resource "aws_network_interface" "tfer--eni-0837bf55019d46b31" {
  attachment {
    device_index = "0"
    instance     = "i-03df1dee532d60a31"
  }

  interface_type     = "interface"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "***********"
  private_ip_list    = ["***********"]
  private_ips        = ["***********"]
  private_ips_count  = "0"
  security_groups    = ["sg-0f2fdd192120dbf17"]
  source_dest_check  = "true"
  subnet_id          = "subnet-07ab4b059d69e4044"
}

resource "aws_network_interface" "tfer--eni-0842c653b0dad4fbd" {
  attachment {
    device_index = "0"
    instance     = "i-068a4053b9c1f23f0"
  }

  interface_type     = "interface"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "**********"
  private_ip_list    = ["**********"]
  private_ips        = ["**********"]
  private_ips_count  = "0"
  security_groups    = ["sg-095117b1a793c2ce0"]
  source_dest_check  = "true"
  subnet_id          = "subnet-07ab4b059d69e4044"
}

resource "aws_network_interface" "tfer--eni-08593c53b89a60ee4" {
  description        = "VPC Endpoint Interface vpce-0366281d6822bac46"
  interface_type     = "vpc_endpoint"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "*********"
  private_ip_list    = ["*********"]
  private_ips        = ["*********"]
  private_ips_count  = "0"
  security_groups    = ["sg-082d3fc2b26b1004e"]
  source_dest_check  = "true"
  subnet_id          = "subnet-0c6066202240c38be"
}

resource "aws_network_interface" "tfer--eni-09379042c909d9d32" {
  description        = "VPC Endpoint Interface vpce-00634b26f7e325402"
  interface_type     = "vpc_endpoint"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "***********"
  private_ip_list    = ["***********"]
  private_ips        = ["***********"]
  private_ips_count  = "0"
  security_groups    = ["sg-05f09a0dc7ab8c53f"]
  source_dest_check  = "true"
  subnet_id          = "subnet-023cafbedbb31d13e"
}

resource "aws_network_interface" "tfer--eni-0958f29b852894cd2" {
  description        = "VPC Endpoint Interface vpce-0ece27853f422d3a6"
  interface_type     = "vpc_endpoint"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "*********"
  private_ip_list    = ["*********"]
  private_ips        = ["*********"]
  private_ips_count  = "0"
  security_groups    = ["sg-082d3fc2b26b1004e"]
  source_dest_check  = "true"
  subnet_id          = "subnet-0c6066202240c38be"
}

resource "aws_network_interface" "tfer--eni-096d098e8a09d429b" {
  description        = "VPC Endpoint Interface vpce-04c5af7d63bb323fb"
  interface_type     = "vpc_endpoint"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "***********"
  private_ip_list    = ["***********"]
  private_ips        = ["***********"]
  private_ips_count  = "0"
  security_groups    = ["sg-00df94fe66c7464f5"]
  source_dest_check  = "true"
  subnet_id          = "subnet-07ab4b059d69e4044"
}

resource "aws_network_interface" "tfer--eni-09dec8943b53ed058" {
  description        = "ELB net/infovault-nlb/aa06b8f0bdcc828a"
  interface_type     = "network_load_balancer"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "***********"
  private_ip_list    = ["***********"]
  private_ips        = ["***********"]
  private_ips_count  = "0"
  source_dest_check  = "false"
  subnet_id          = "subnet-0d4f42cf30b20c42d"
}

resource "aws_network_interface" "tfer--eni-09e6836ed6735715e" {
  description        = "VPC Endpoint Interface vpce-000d3536c71bcfc70"
  interface_type     = "vpc_endpoint"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "**********"
  private_ip_list    = ["**********"]
  private_ips        = ["**********"]
  private_ips_count  = "0"
  security_groups    = ["sg-0870c466d0db940c4"]
  source_dest_check  = "true"
  subnet_id          = "subnet-0dacd0ef604204014"
}

resource "aws_network_interface" "tfer--eni-0a08f25491f59d58e" {
  description        = "VPC Endpoint Interface vpce-08c51d0d6582b0aa6"
  interface_type     = "vpc_endpoint"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "**********"
  private_ip_list    = ["**********"]
  private_ips        = ["**********"]
  private_ips_count  = "0"
  security_groups    = ["sg-066975c77a66a64d9"]
  source_dest_check  = "true"
  subnet_id          = "subnet-0a1802eb99e96cbed"
}

resource "aws_network_interface" "tfer--eni-0a181a6d7acba6b00" {
  description        = "VPC Endpoint Interface vpce-04d5a7ce83abf07b5"
  interface_type     = "vpc_endpoint"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "**********3"
  private_ip_list    = ["**********3"]
  private_ips        = ["**********3"]
  private_ips_count  = "0"
  security_groups    = ["sg-0d28b5ada1bb60c9a"]
  source_dest_check  = "true"
  subnet_id          = "subnet-04910ced717a5584a"
}

resource "aws_network_interface" "tfer--eni-0a4a1d4b782759c8d" {
  description        = "VPC Endpoint Interface vpce-0c5ff88c30a3e428d"
  interface_type     = "vpc_endpoint"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "***********"
  private_ip_list    = ["***********"]
  private_ips        = ["***********"]
  private_ips_count  = "0"
  security_groups    = ["sg-065d1e24d43126489"]
  source_dest_check  = "true"
  subnet_id          = "subnet-07ab4b059d69e4044"
}

resource "aws_network_interface" "tfer--eni-0a5fb5d8d833ef844" {
  description        = "Amazon EKS infovault-dev-eks-cluster-v130"
  interface_type     = "interface"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "************"
  private_ip_list    = ["************"]
  private_ips        = ["************"]
  private_ips_count  = "0"
  security_groups    = ["sg-0353836b1b373e27d", "sg-0950397297ecb203f"]
  source_dest_check  = "true"
  subnet_id          = "subnet-015e4b2acb87091d9"
}

resource "aws_network_interface" "tfer--eni-0b0c10f4ff924ca83" {
  description        = "VPC Endpoint Interface vpce-0ce3dc776f7700c04"
  interface_type     = "vpc_endpoint"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "***********"
  private_ip_list    = ["***********"]
  private_ips        = ["***********"]
  private_ips_count  = "0"
  security_groups    = ["sg-0d28b5ada1bb60c9a"]
  source_dest_check  = "true"
  subnet_id          = "subnet-0ebbc016c2fc6b3db"
}

resource "aws_network_interface" "tfer--eni-0bb4d63b69f41cce3" {
  attachment {
    device_index = "0"
    instance     = "i-03b6f949999d93642"
  }

  interface_type     = "interface"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "***********"
  private_ip_list    = ["***********"]
  private_ips        = ["***********"]
  private_ips_count  = "0"
  security_groups    = ["sg-0c081d79227db7d86"]
  source_dest_check  = "true"
  subnet_id          = "subnet-023cafbedbb31d13e"
}

resource "aws_network_interface" "tfer--eni-0bfc335183ba65d53" {
  attachment {
    device_index = "1"
    instance     = "i-03ddfe5f32292e59d"
  }

  description        = "aws-K8S-i-03ddfe5f32292e59d"
  interface_type     = "interface"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "***********"
  private_ip_list    = ["************", "************", "************", "***********", "************", "************", "************", "************", "************", "************", "************", "************", "************", "***********", "***********0", "***********7", "************", "************", "************", "************", "************", "************", "************", "************", "***********", "***********", "***********", "***********", "***********", "***********"]
  private_ips        = ["************", "************", "************", "***********", "************", "************", "************", "************", "************", "************", "************", "************", "************", "***********", "***********0", "***********7", "************", "************", "************", "************", "************", "************", "************", "************", "***********", "***********", "***********", "***********", "***********", "***********"]
  private_ips_count  = "29"
  security_groups    = ["sg-0353836b1b373e27d"]
  source_dest_check  = "true"
  subnet_id          = "subnet-015e4b2acb87091d9"

  tags = {
    "cluster.k8s.amazonaws.com/name"     = "infovault-dev-eks-cluster-v130"
    "node.k8s.amazonaws.com/createdAt"   = "2025-05-21T10:24:10Z"
    "node.k8s.amazonaws.com/instance_id" = "i-03ddfe5f32292e59d"
  }

  tags_all = {
    "cluster.k8s.amazonaws.com/name"     = "infovault-dev-eks-cluster-v130"
    "node.k8s.amazonaws.com/createdAt"   = "2025-05-21T10:24:10Z"
    "node.k8s.amazonaws.com/instance_id" = "i-03ddfe5f32292e59d"
  }
}

resource "aws_network_interface" "tfer--eni-0c3359775b007e6ed" {
  attachment {
    device_index = "0"
    instance     = "i-0edc7073f7666d047"
  }

  interface_type     = "interface"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "***********"
  private_ip_list    = ["***********"]
  private_ips        = ["***********"]
  private_ips_count  = "0"
  security_groups    = ["sg-043af8ab2fe4ec50f"]
  source_dest_check  = "true"
  subnet_id          = "subnet-0d4f42cf30b20c42d"
}

resource "aws_network_interface" "tfer--eni-0c4e492f96b742a6f" {
  description        = "VPC Endpoint Interface vpce-0c7798e909bd72ff8"
  interface_type     = "vpc_endpoint"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "***********"
  private_ip_list    = ["***********"]
  private_ips        = ["***********"]
  private_ips_count  = "0"
  security_groups    = ["sg-05f09a0dc7ab8c53f"]
  source_dest_check  = "true"
  subnet_id          = "subnet-023cafbedbb31d13e"
}

resource "aws_network_interface" "tfer--eni-0c7ac735edacfcf5e" {
  description        = "ELB net/app-nlb-internal/e4f71be0ff87ebcc"
  interface_type     = "network_load_balancer"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "***********"
  private_ip_list    = ["***********"]
  private_ips        = ["***********"]
  private_ips_count  = "0"
  source_dest_check  = "false"
  subnet_id          = "subnet-0c775a4f57dfa0e61"
}

resource "aws_network_interface" "tfer--eni-0cbabfcd450ce5061" {
  attachment {
    device_index = "0"
    instance     = "i-0b7a54f938f84affa"
  }

  interface_type     = "interface"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "***********"
  private_ip_list    = ["***********"]
  private_ips        = ["***********"]
  private_ips_count  = "0"
  security_groups    = ["sg-0d8a5fa845dca93c9"]
  source_dest_check  = "true"
  subnet_id          = "subnet-0d4f42cf30b20c42d"
}

resource "aws_network_interface" "tfer--eni-0cd213280801297e2" {
  description        = "VPC Endpoint Interface vpce-0f629db35d2992350"
  interface_type     = "vpc_endpoint"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "***********"
  private_ip_list    = ["***********"]
  private_ips        = ["***********"]
  private_ips_count  = "0"
  security_groups    = ["sg-0330a127b43c54ab2"]
  source_dest_check  = "true"
  subnet_id          = "subnet-0dacd0ef604204014"
}

resource "aws_network_interface" "tfer--eni-0cd3deb8661a82b94" {
  description        = "Amazon EKS infovault-dev-eks-cluster-v130"
  interface_type     = "interface"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "***********"
  private_ip_list    = ["***********"]
  private_ips        = ["***********"]
  private_ips_count  = "0"
  security_groups    = ["sg-0353836b1b373e27d", "sg-0950397297ecb203f"]
  source_dest_check  = "true"
  subnet_id          = "subnet-023cafbedbb31d13e"
}

resource "aws_network_interface" "tfer--eni-0d4bd2f33d18876ea" {
  description        = "ELB net/app-nlb-internal/e4f71be0ff87ebcc"
  interface_type     = "network_load_balancer"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "************"
  private_ip_list    = ["************"]
  private_ips        = ["************"]
  private_ips_count  = "0"
  source_dest_check  = "false"
  subnet_id          = "subnet-023cafbedbb31d13e"
}

resource "aws_network_interface" "tfer--eni-0d6e441784201ed85" {
  description        = "Interface for NAT Gateway nat-0faca993cb72fc7d6"
  interface_type     = "nat_gateway"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "**********"
  private_ip_list    = ["**********"]
  private_ips        = ["**********"]
  private_ips_count  = "0"
  source_dest_check  = "false"
  subnet_id          = "subnet-01e871c49dc4b9b1f"
}

resource "aws_network_interface" "tfer--eni-0de844e6b4a90ea21" {
  attachment {
    device_index = "0"
    instance     = "i-09e9e4c01cabdc2ba"
  }

  interface_type     = "interface"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "**********"
  private_ip_list    = ["**********"]
  private_ips        = ["**********"]
  private_ips_count  = "0"
  security_groups    = ["sg-0764227cd7e054e0a"]
  source_dest_check  = "true"
  subnet_id          = "subnet-0a5d4aa5489b9ab3a"
}

resource "aws_network_interface" "tfer--eni-0e4892cdad21d3d27" {
  description        = "VPC Endpoint Interface vpce-025b48dac964f67b4"
  interface_type     = "vpc_endpoint"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "**********"
  private_ip_list    = ["**********"]
  private_ips        = ["**********"]
  private_ips_count  = "0"
  security_groups    = ["sg-066975c77a66a64d9"]
  source_dest_check  = "true"
  subnet_id          = "subnet-0a1802eb99e96cbed"
}

resource "aws_network_interface" "tfer--eni-0e961cb5275d963d7" {
  description        = "VPC Endpoint Interface vpce-094e78f395ad5f6f7"
  interface_type     = "vpc_endpoint"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "*********0"
  private_ip_list    = ["*********0"]
  private_ips        = ["*********0"]
  private_ips_count  = "0"
  security_groups    = ["sg-066975c77a66a64d9"]
  source_dest_check  = "true"
  subnet_id          = "subnet-0a1802eb99e96cbed"
}

resource "aws_network_interface" "tfer--eni-0edd3b7c8064ca22e" {
  description        = "VPC Endpoint Interface vpce-0c7798e909bd72ff8"
  interface_type     = "vpc_endpoint"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "100.67.2.250"
  private_ip_list    = ["100.67.2.250"]
  private_ips        = ["100.67.2.250"]
  private_ips_count  = "0"
  security_groups    = ["sg-05f09a0dc7ab8c53f"]
  source_dest_check  = "true"
  subnet_id          = "subnet-015e4b2acb87091d9"
}

resource "aws_network_interface" "tfer--eni-0f2f4d8868d50f09c" {
  attachment {
    device_index = "0"
    instance     = "i-026e961cb556a37aa"
  }

  interface_type     = "interface"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "10.66.0.25"
  private_ip_list    = ["10.66.0.25"]
  private_ips        = ["10.66.0.25"]
  private_ips_count  = "0"
  security_groups    = ["sg-029c5f3f1ddf2855e"]
  source_dest_check  = "true"
  subnet_id          = "subnet-0e2a201f2503f59ee"
}

resource "aws_network_interface" "tfer--eni-0f4f360a445263ef1" {
  description        = "VPC Endpoint Interface vpce-0ce3dc776f7700c04"
  interface_type     = "vpc_endpoint"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "***********"
  private_ip_list    = ["***********"]
  private_ips        = ["***********"]
  private_ips_count  = "0"
  security_groups    = ["sg-0d28b5ada1bb60c9a"]
  source_dest_check  = "true"
  subnet_id          = "subnet-04910ced717a5584a"
}

resource "aws_network_interface" "tfer--eni-0f51e5d7d6ccb7f1b" {
  description        = "VPC Endpoint Interface vpce-0ac5502093940b08f"
  interface_type     = "vpc_endpoint"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "*********"
  private_ip_list    = ["*********"]
  private_ips        = ["*********"]
  private_ips_count  = "0"
  security_groups    = ["sg-082d3fc2b26b1004e"]
  source_dest_check  = "true"
  subnet_id          = "subnet-0c6066202240c38be"
}

resource "aws_network_interface" "tfer--eni-0f5e018aa1e689f80" {
  attachment {
    device_index = "0"
    instance     = "i-041e4356012a67cb5"
  }

  interface_type     = "interface"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "**********"
  private_ip_list    = ["**********"]
  private_ips        = ["**********"]
  private_ips_count  = "0"
  security_groups    = ["sg-00b66d34fb63637f3"]
  source_dest_check  = "true"
  subnet_id          = "subnet-01e871c49dc4b9b1f"
}

resource "aws_network_interface" "tfer--eni-0f769cda654ffb0c2" {
  attachment {
    device_index = "0"
    instance     = "i-0e6aace81e99d8af9"
  }

  interface_type     = "interface"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "**********"
  private_ip_list    = ["**********"]
  private_ips        = ["**********"]
  private_ips_count  = "0"
  security_groups    = ["sg-04080d467ff2ca6c4"]
  source_dest_check  = "true"
  subnet_id          = "subnet-0dacd0ef604204014"
}

resource "aws_network_interface" "tfer--eni-0f8b1e2c7cde7b2d6" {
  attachment {
    device_index = "0"
    instance     = "i-047eb48a87cdabc96"
  }

  interface_type     = "interface"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "***********9"
  private_ip_list    = ["************", "************", "************", "************", "************", "***********", "***********0", "************", "************", "***********", "***********3", "***********5", "***********8", "***********9", "************", "************", "************", "************", "***********", "***********", "***********", "***********", "***********", "**********", "**********5", "**********7", "**********9", "***********", "***********", "***********"]
  private_ips        = ["************", "************", "************", "************", "************", "***********", "***********0", "************", "************", "***********", "***********3", "***********5", "***********8", "***********9", "************", "************", "************", "************", "***********", "***********", "***********", "***********", "***********", "**********", "**********5", "**********7", "**********9", "***********", "***********", "***********"]
  private_ips_count  = "29"
  security_groups    = ["sg-0353836b1b373e27d"]
  source_dest_check  = "true"
  subnet_id          = "subnet-023cafbedbb31d13e"

  tags = {
    "cluster.k8s.amazonaws.com/name"     = "infovault-dev-eks-cluster-v130"
    "eks:cluster-name"                   = "infovault-dev-eks-cluster-v130"
    "eks:nodegroup-name"                 = "v130-node-group-3"
    "node.k8s.amazonaws.com/instance_id" = "i-047eb48a87cdabc96"
  }

  tags_all = {
    "cluster.k8s.amazonaws.com/name"     = "infovault-dev-eks-cluster-v130"
    "eks:cluster-name"                   = "infovault-dev-eks-cluster-v130"
    "eks:nodegroup-name"                 = "v130-node-group-3"
    "node.k8s.amazonaws.com/instance_id" = "i-047eb48a87cdabc96"
  }
}

resource "aws_network_interface" "tfer--eni-0fc71be00817c5145" {
  attachment {
    device_index = "0"
    instance     = "i-063edc7bb33841b7c"
  }

  interface_type     = "interface"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "***********"
  private_ip_list    = ["***********"]
  private_ips        = ["***********"]
  private_ips_count  = "0"
  security_groups    = ["sg-0950397297ecb203f"]
  source_dest_check  = "true"
  subnet_id          = "subnet-0d4f42cf30b20c42d"
}

resource "aws_network_interface" "tfer--eni-0ffcf9a54002de3ca" {
  description        = "VPC Endpoint Interface vpce-00634b26f7e325402"
  interface_type     = "vpc_endpoint"
  ipv4_prefix_count  = "0"
  ipv6_address_count = "0"
  ipv6_prefix_count  = "0"
  private_ip         = "***********"
  private_ip_list    = ["***********"]
  private_ips        = ["***********"]
  private_ips_count  = "0"
  security_groups    = ["sg-05f09a0dc7ab8c53f"]
  source_dest_check  = "true"
  subnet_id          = "subnet-015e4b2acb87091d9"
}
