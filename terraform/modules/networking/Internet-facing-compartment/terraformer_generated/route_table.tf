# Generated by Terraformer for VPC vpc-037f619602fa293a1
# Source: /Users/<USER>/Documents/Projects/infovault-infrastructure/terraform/modules/networking/Internet-facing-compartment/raw_terraformer/aws/route_table/route_table.tf
# Generated on: 2025-06-09 13:17:22

resource "aws_route_table" "tfer--rtb-00c4597c163a0ed96" {
  route {
    cidr_block     = "0.0.0.0/0"
    nat_gateway_id = "nat-0faca993cb72fc7d6"
  }

  route {
    cidr_block                = "100.14.0.0/27"
    vpc_peering_connection_id = "pcx-02dd2ea276cb24bb3"
  }

  route {
    cidr_block                = "100.15.0.0/26"
    vpc_peering_connection_id = "pcx-0f0db258d11626f4c"
  }

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "dev-net-rtb-priv-az1"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "dev-net-rtb-priv-az1"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_id = data.terraform_remote_state.vpc.outputs.aws_vpc_tfer--vpc-037f619602fa293a1_id
}

resource "aws_route_table" "tfer--rtb-00c5fd2f2cd25c1f8" {
  route {
    cidr_block                = "100.67.0.0/27"
    vpc_peering_connection_id = "pcx-02dd2ea276cb24bb3"
  }

  vpc_id = "vpc-0ca0480cce82946b3"
}

resource "aws_route_table" "tfer--rtb-015ae5889ca4aaadd" {
  route {
    cidr_block = "0.0.0.0/0"
    gateway_id = "igw-05beaf9de3b1e3902"
  }

  route {
    cidr_block                = "100.15.0.0/26"
    vpc_peering_connection_id = "pcx-00306eabadaf4a547"
  }

  route {
    cidr_block                = "100.66.0.0/22"
    vpc_peering_connection_id = "pcx-07364bf66a2a53a7d"
  }

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "rtb-gen-ingress"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "rtb-gen-ingress"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_id = "vpc-05e0e8104b3321c40"
}

resource "aws_route_table" "tfer--rtb-02a850249ccfbe80b" {
  route {
    cidr_block                = "10.66.0.0/26"
    vpc_peering_connection_id = "pcx-00306eabadaf4a547"
  }

  route {
    cidr_block                = "100.67.0.0/22"
    vpc_peering_connection_id = "pcx-00306eabadaf4a547"
  }

  route {
    cidr_block                = "100.67.0.0/27"
    vpc_peering_connection_id = "pcx-0f0db258d11626f4c"
  }

  vpc_id = "vpc-05f1b20a6634c6a38"
}

resource "aws_route_table" "tfer--rtb-02a9859d16aa6e04b" {
  tags = {
    Name    = "aws-controltower-PrivateSubnet1ARouteTable"
    Network = "Private"
  }

  tags_all = {
    Name    = "aws-controltower-PrivateSubnet1ARouteTable"
    Network = "Private"
  }

  vpc_id = "vpc-02282d8d4c9c7764c"
}

resource "aws_route_table" "tfer--rtb-0355b1ea7a0ec320a" {
  route {
    cidr_block                = "100.14.0.0/27"
    vpc_peering_connection_id = "pcx-02dd2ea276cb24bb3"
  }

  route {
    cidr_block                = "100.15.0.0/26"
    vpc_peering_connection_id = "pcx-0f0db258d11626f4c"
  }

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "dev-net-rtb-tgw"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "dev-net-rtb-tgw"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_id = data.terraform_remote_state.vpc.outputs.aws_vpc_tfer--vpc-037f619602fa293a1_id
}

resource "aws_route_table" "tfer--rtb-038d861f618d80392" {
  route {
    cidr_block     = "0.0.0.0/0"
    nat_gateway_id = "nat-0431bfe8618055bd8"
  }

  route {
    cidr_block                = "100.15.0.0/26"
    vpc_peering_connection_id = "pcx-00306eabadaf4a547"
  }

  tags = {
    AutoShutdown = "true"
    Environment  = "dev"
    Name         = "app-subnet-az2-rt"
    Owner        = "Temus"
    Project      = "InfoVault"
  }

  tags_all = {
    AutoShutdown = "true"
    Environment  = "dev"
    Name         = "app-subnet-az2-rt"
    Owner        = "Temus"
    Project      = "InfoVault"
  }

  vpc_id = "vpc-05e0e8104b3321c40"
}

resource "aws_route_table" "tfer--rtb-03b5487f3d5603599" {
  route {
    cidr_block                = "10.66.0.0/26"
    vpc_peering_connection_id = "pcx-00306eabadaf4a547"
  }

  route {
    cidr_block                = "100.67.0.0/22"
    vpc_peering_connection_id = "pcx-00306eabadaf4a547"
  }

  route {
    cidr_block                = "100.67.0.0/27"
    vpc_peering_connection_id = "pcx-0f0db258d11626f4c"
  }

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "dev-mgmt-rtb-tgw"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "dev-mgmt-rtb-tgw"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_id = "vpc-05f1b20a6634c6a38"
}

resource "aws_route_table" "tfer--rtb-0445ba29313210ac5" {
  route {
    cidr_block = "0.0.0.0/0"
    gateway_id = "igw-066231fa2c240e55d"
  }

  route {
    cidr_block                = "10.66.0.0/26"
    vpc_peering_connection_id = "pcx-00306eabadaf4a547"
  }

  route {
    cidr_block                = "100.67.0.0/22"
    vpc_peering_connection_id = "pcx-00306eabadaf4a547"
  }

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-public-rt"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-public-rt"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_id = "vpc-05f1b20a6634c6a38"
}

resource "aws_route_table" "tfer--rtb-048f93d202df7cd40" {
  tags = {
    Name    = "aws-controltower-PrivateSubnet3ARouteTable"
    Network = "Private"
  }

  tags_all = {
    Name    = "aws-controltower-PrivateSubnet3ARouteTable"
    Network = "Private"
  }

  vpc_id = "vpc-02282d8d4c9c7764c"
}

resource "aws_route_table" "tfer--rtb-04dea60d5aeca9def" {
  route {
    cidr_block = "0.0.0.0/0"
    gateway_id = "igw-05beaf9de3b1e3902"
  }

  route {
    cidr_block                = "100.15.0.0/26"
    vpc_peering_connection_id = "pcx-00306eabadaf4a547"
  }

  route {
    cidr_block                = "100.66.0.0/22"
    vpc_peering_connection_id = "pcx-07364bf66a2a53a7d"
  }

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-public-rt"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-public-rt"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_id = "vpc-05e0e8104b3321c40"
}

resource "aws_route_table" "tfer--rtb-04e5bfdb40e860b95" {
  vpc_id = "vpc-02282d8d4c9c7764c"
}

resource "aws_route_table" "tfer--rtb-05df1c4be4e435f04" {
  route {
    cidr_block                = "10.66.0.0/26"
    vpc_peering_connection_id = "pcx-00306eabadaf4a547"
  }

  route {
    cidr_block                = "100.67.0.0/22"
    vpc_peering_connection_id = "pcx-00306eabadaf4a547"
  }

  route {
    cidr_block                = "100.67.0.0/27"
    vpc_peering_connection_id = "pcx-0f0db258d11626f4c"
  }

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "rtb-mgmt-main"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "rtb-mgmt-main"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_id = "vpc-05f1b20a6634c6a38"
}

resource "aws_route_table" "tfer--rtb-06ca622706fb0676d" {
  route {
    cidr_block     = "0.0.0.0/0"
    nat_gateway_id = "nat-0431bfe8618055bd8"
  }

  route {
    cidr_block                = "100.15.0.0/26"
    vpc_peering_connection_id = "pcx-00306eabadaf4a547"
  }

  tags = {
    AutoShutdown = "true"
    Environment  = "dev"
    Name         = "db-subnet-az2-rt"
    Owner        = "Temus"
    Project      = "InfoVault"
  }

  tags_all = {
    AutoShutdown = "true"
    Environment  = "dev"
    Name         = "db-subnet-az2-rt"
    Owner        = "Temus"
    Project      = "InfoVault"
  }

  vpc_id = "vpc-05e0e8104b3321c40"
}

resource "aws_route_table" "tfer--rtb-0795b2e3d3e3f88ed" {
  route {
    cidr_block     = "0.0.0.0/0"
    nat_gateway_id = "nat-0431bfe8618055bd8"
  }

  route {
    cidr_block                = "100.15.0.0/26"
    vpc_peering_connection_id = "pcx-00306eabadaf4a547"
  }

  tags = {
    AutoShutdown = "true"
    Environment  = "dev"
    Name         = "app-subnet-az1-rt"
    Owner        = "Temus"
    Project      = "InfoVault"
  }

  tags_all = {
    AutoShutdown = "true"
    Environment  = "dev"
    Name         = "app-subnet-az1-rt"
    Owner        = "Temus"
    Project      = "InfoVault"
  }

  vpc_id = "vpc-05e0e8104b3321c40"
}

resource "aws_route_table" "tfer--rtb-07eb7e9c7e982fdba" {
  route {
    cidr_block = "0.0.0.0/0"
    gateway_id = "igw-00a312b81674ba618"
  }

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "dev-net-rtb-fw-az1"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "dev-net-rtb-fw-az1"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_id = data.terraform_remote_state.vpc.outputs.aws_vpc_tfer--vpc-037f619602fa293a1_id
}

resource "aws_route_table" "tfer--rtb-089ed5e648707e2d9" {
  route {
    cidr_block = "0.0.0.0/0"
    gateway_id = "igw-0b197ebf6057fbe90"
  }

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-public-rt"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-public-rt"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_id = "vpc-0e75036a2ad9e5b4f"
}

resource "aws_route_table" "tfer--rtb-08edcd5b7304aa2aa" {
  route {
    cidr_block                = "100.15.0.0/26"
    vpc_peering_connection_id = "pcx-00306eabadaf4a547"
  }

  route {
    cidr_block                = "100.66.0.0/22"
    vpc_peering_connection_id = "pcx-07364bf66a2a53a7d"
  }

  vpc_id = "vpc-05e0e8104b3321c40"
}

resource "aws_route_table" "tfer--rtb-098958cd03a46c219" {
  route {
    cidr_block     = "0.0.0.0/0"
    nat_gateway_id = "nat-0431bfe8618055bd8"
  }

  route {
    cidr_block                = "100.15.0.0/26"
    vpc_peering_connection_id = "pcx-00306eabadaf4a547"
  }

  tags = {
    AutoShutdown = "true"
    Environment  = "dev"
    Name         = "db-subnet-az1-rt"
    Owner        = "Temus"
    Project      = "InfoVault"
  }

  tags_all = {
    AutoShutdown = "true"
    Environment  = "dev"
    Name         = "db-subnet-az1-rt"
    Owner        = "Temus"
    Project      = "InfoVault"
  }

  vpc_id = "vpc-05e0e8104b3321c40"
}

resource "aws_route_table" "tfer--rtb-09a828af05f32b0cf" {
  route {
    cidr_block                = "100.67.0.0/27"
    vpc_peering_connection_id = "pcx-02dd2ea276cb24bb3"
  }

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-patching-subnet-az1-rt"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-patching-subnet-az1-rt"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_id = "vpc-0ca0480cce82946b3"
}

resource "aws_route_table" "tfer--rtb-09e7342b3a3bc5507" {
  tags = {
    Name    = "aws-controltower-PrivateSubnet2ARouteTable"
    Network = "Private"
  }

  tags_all = {
    Name    = "aws-controltower-PrivateSubnet2ARouteTable"
    Network = "Private"
  }

  vpc_id = "vpc-02282d8d4c9c7764c"
}

resource "aws_route_table" "tfer--rtb-0a3ed8d3c391e4cca" {
  route {
    cidr_block                = "100.15.0.0/26"
    vpc_peering_connection_id = "pcx-00306eabadaf4a547"
  }

  route {
    cidr_block                = "100.66.0.0/22"
    vpc_peering_connection_id = "pcx-07364bf66a2a53a7d"
  }

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-private-rt"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-private-rt"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_id = "vpc-05e0e8104b3321c40"
}

resource "aws_route_table" "tfer--rtb-0aa2547a225d317bd" {
  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-private-rt"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-private-rt"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_id = "vpc-0e75036a2ad9e5b4f"
}

resource "aws_route_table" "tfer--rtb-0c5895a403cd0d50e" {
  tags = {
    AutoShutdown    = "true"
    Compartment     = "ABLRHF"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-ablrhf-rt"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Compartment     = "ABLRHF"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-ablrhf-rt"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_id = "vpc-0e75036a2ad9e5b4f"
}

resource "aws_route_table" "tfer--rtb-0c7da122446383cc5" {
  route {
    cidr_block     = "0.0.0.0/0"
    nat_gateway_id = "nat-0431bfe8618055bd8"
  }

  route {
    cidr_block                = "100.15.0.0/26"
    vpc_peering_connection_id = "pcx-00306eabadaf4a547"
  }

  tags = {
    AutoShutdown = "true"
    Environment  = "dev"
    Name         = "smtp-subnet-dlz1-private-rt"
    Owner        = "Temus"
    Project      = "InfoVault"
    Type         = "Private"
  }

  tags_all = {
    AutoShutdown = "true"
    Environment  = "dev"
    Name         = "smtp-subnet-dlz1-private-rt"
    Owner        = "Temus"
    Project      = "InfoVault"
    Type         = "Private"
  }

  vpc_id = "vpc-05e0e8104b3321c40"
}

resource "aws_route_table" "tfer--rtb-0cb7cabf4d81ee7bd" {
  vpc_id = "vpc-0e75036a2ad9e5b4f"
}

resource "aws_route_table" "tfer--rtb-0d508af52f5965f6a" {
  route {
    cidr_block = "0.0.0.0/0"
    gateway_id = "igw-05beaf9de3b1e3902"
  }

  route {
    cidr_block                = "100.15.0.0/26"
    vpc_peering_connection_id = "pcx-00306eabadaf4a547"
  }

  route {
    cidr_block                = "100.66.0.0/22"
    vpc_peering_connection_id = "pcx-07364bf66a2a53a7d"
  }

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "rtb-gen-egress"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "rtb-gen-egress"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_id = "vpc-05e0e8104b3321c40"
}

resource "aws_route_table" "tfer--rtb-0de7189dd22690311" {
  route {
    cidr_block                = "100.15.0.0/26"
    vpc_peering_connection_id = "pcx-0f0db258d11626f4c"
  }

  vpc_id = data.terraform_remote_state.vpc.outputs.aws_vpc_tfer--vpc-037f619602fa293a1_id
}

resource "aws_route_table" "tfer--rtb-0e47b2c134f9ec024" {
  route {
    cidr_block     = "0.0.0.0/0"
    nat_gateway_id = "nat-0431bfe8618055bd8"
  }

  route {
    cidr_block                = "100.15.0.0/26"
    vpc_peering_connection_id = "pcx-00306eabadaf4a547"
  }

  route {
    cidr_block                = "100.66.0.0/22"
    vpc_peering_connection_id = "pcx-07364bf66a2a53a7d"
  }

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "rtb-gen-migration"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "rtb-gen-migration"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_id = "vpc-05e0e8104b3321c40"
}

resource "aws_route_table" "tfer--rtb-0eb045ebd5d6d7479" {
  route {
    cidr_block = "0.0.0.0/0"
    gateway_id = "igw-00a312b81674ba618"
  }

  route {
    cidr_block                = "100.14.0.0/27"
    vpc_peering_connection_id = "pcx-02dd2ea276cb24bb3"
  }

  route {
    cidr_block                = "100.15.0.0/26"
    vpc_peering_connection_id = "pcx-0f0db258d11626f4c"
  }

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "dev-net-rtb-pub"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "dev-net-rtb-pub"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_id = data.terraform_remote_state.vpc.outputs.aws_vpc_tfer--vpc-037f619602fa293a1_id
}

resource "aws_route_table" "tfer--rtb-0f04c8976ea037652" {
  route {
    cidr_block                = "100.15.0.0/26"
    vpc_peering_connection_id = "pcx-00306eabadaf4a547"
  }

  vpc_id = "vpc-05e0e8104b3321c40"
}

resource "aws_route_table" "tfer--rtb-0f6307e8c214b6a71" {
  route {
    cidr_block     = "0.0.0.0/0"
    nat_gateway_id = "nat-0431bfe8618055bd8"
  }

  route {
    cidr_block                = "100.15.0.0/26"
    vpc_peering_connection_id = "pcx-00306eabadaf4a547"
  }

  tags = {
    AutoShutdown = "true"
    Environment  = "dev"
    Name         = "smtp-subnet-dlz1-private-rt"
    Owner        = "Temus"
    Project      = "InfoVault"
    Type         = "Private"
  }

  tags_all = {
    AutoShutdown = "true"
    Environment  = "dev"
    Name         = "smtp-subnet-dlz1-private-rt"
    Owner        = "Temus"
    Project      = "InfoVault"
    Type         = "Private"
  }

  vpc_id = "vpc-05e0e8104b3321c40"
}
