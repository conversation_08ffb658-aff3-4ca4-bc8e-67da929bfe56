# Generated by Terraformer for VPC vpc-037f619602fa293a1
# Source: /Users/<USER>/Documents/Projects/infovault-infrastructure/terraform/modules/networking/Internet-facing-compartment/raw_terraformer/aws/vpc/vpc.tf
# Generated on: 2025-06-09 13:17:22

resource "aws_vpc" "tfer--vpc-037f619602fa293a1" {
  assign_generated_ipv6_cidr_block     = "false"
  cidr_block                           = "**********/27"
  enable_dns_hostnames                 = "true"
  enable_dns_support                   = "true"
  enable_network_address_usage_metrics = "false"
  instance_tenancy                     = "default"
  ipv6_netmask_length                  = "0"

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-Internet-facing-compartment"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-Internet-facing-compartment"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }
}
