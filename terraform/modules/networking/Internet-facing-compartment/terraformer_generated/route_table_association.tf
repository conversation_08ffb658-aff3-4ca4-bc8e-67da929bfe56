# Generated by Terraformer for VPC vpc-037f619602fa293a1
# Source: /Users/<USER>/Documents/Projects/infovault-infrastructure/terraform/modules/networking/Internet-facing-compartment/raw_terraformer/aws/route_table/route_table_association.tf
# Generated on: 2025-06-09 13:17:22

resource "aws_route_table_association" "tfer--subnet-015e4b2acb87091d9" {
  route_table_id = data.terraform_remote_state.route_table.outputs.aws_route_table_tfer--rtb-038d861f618d80392_id
  subnet_id      = data.terraform_remote_state.subnet.outputs.aws_subnet_tfer--subnet-015e4b2acb87091d9_id
}

resource "aws_route_table_association" "tfer--subnet-01e871c49dc4b9b1f" {
  route_table_id = data.terraform_remote_state.route_table.outputs.aws_route_table_tfer--rtb-0eb045ebd5d6d7479_id
  subnet_id      = data.terraform_remote_state.subnet.outputs.aws_subnet_tfer--subnet-01e871c49dc4b9b1f_id
}

resource "aws_route_table_association" "tfer--subnet-023cafbedbb31d13e" {
  route_table_id = data.terraform_remote_state.route_table.outputs.aws_route_table_tfer--rtb-0795b2e3d3e3f88ed_id
  subnet_id      = data.terraform_remote_state.subnet.outputs.aws_subnet_tfer--subnet-023cafbedbb31d13e_id
}

resource "aws_route_table_association" "tfer--subnet-043dd23a18b80a280" {
  route_table_id = data.terraform_remote_state.route_table.outputs.aws_route_table_tfer--rtb-0d508af52f5965f6a_id
  subnet_id      = data.terraform_remote_state.subnet.outputs.aws_subnet_tfer--subnet-043dd23a18b80a280_id
}

resource "aws_route_table_association" "tfer--subnet-056cc33d6674e5027" {
  route_table_id = data.terraform_remote_state.route_table.outputs.aws_route_table_tfer--rtb-09e7342b3a3bc5507_id
  subnet_id      = data.terraform_remote_state.subnet.outputs.aws_subnet_tfer--subnet-056cc33d6674e5027_id
}

resource "aws_route_table_association" "tfer--subnet-0669d84e56a7dc53e" {
  route_table_id = data.terraform_remote_state.route_table.outputs.aws_route_table_tfer--rtb-02a9859d16aa6e04b_id
  subnet_id      = data.terraform_remote_state.subnet.outputs.aws_subnet_tfer--subnet-0669d84e56a7dc53e_id
}

resource "aws_route_table_association" "tfer--subnet-07ab4b059d69e4044" {
  route_table_id = data.terraform_remote_state.route_table.outputs.aws_route_table_tfer--rtb-05df1c4be4e435f04_id
  subnet_id      = data.terraform_remote_state.subnet.outputs.aws_subnet_tfer--subnet-07ab4b059d69e4044_id
}

resource "aws_route_table_association" "tfer--subnet-080d8fdb4e9add74c" {
  route_table_id = data.terraform_remote_state.route_table.outputs.aws_route_table_tfer--rtb-0c7da122446383cc5_id
  subnet_id      = data.terraform_remote_state.subnet.outputs.aws_subnet_tfer--subnet-080d8fdb4e9add74c_id
}

resource "aws_route_table_association" "tfer--subnet-081b6eb841b4451b3" {
  route_table_id = data.terraform_remote_state.route_table.outputs.aws_route_table_tfer--rtb-04dea60d5aeca9def_id
  subnet_id      = data.terraform_remote_state.subnet.outputs.aws_subnet_tfer--subnet-081b6eb841b4451b3_id
}

resource "aws_route_table_association" "tfer--subnet-0a1802eb99e96cbed" {
  route_table_id = data.terraform_remote_state.route_table.outputs.aws_route_table_tfer--rtb-0e47b2c134f9ec024_id
  subnet_id      = data.terraform_remote_state.subnet.outputs.aws_subnet_tfer--subnet-0a1802eb99e96cbed_id
}

resource "aws_route_table_association" "tfer--subnet-0a5d4aa5489b9ab3a" {
  route_table_id = data.terraform_remote_state.route_table.outputs.aws_route_table_tfer--rtb-0f6307e8c214b6a71_id
  subnet_id      = data.terraform_remote_state.subnet.outputs.aws_subnet_tfer--subnet-0a5d4aa5489b9ab3a_id
}

resource "aws_route_table_association" "tfer--subnet-0aec855b39a7be02e" {
  route_table_id = data.terraform_remote_state.route_table.outputs.aws_route_table_tfer--rtb-00c4597c163a0ed96_id
  subnet_id      = data.terraform_remote_state.subnet.outputs.aws_subnet_tfer--subnet-0aec855b39a7be02e_id
}

resource "aws_route_table_association" "tfer--subnet-0b9afa4ae103de5b5" {
  route_table_id = data.terraform_remote_state.route_table.outputs.aws_route_table_tfer--rtb-048f93d202df7cd40_id
  subnet_id      = data.terraform_remote_state.subnet.outputs.aws_subnet_tfer--subnet-0b9afa4ae103de5b5_id
}

resource "aws_route_table_association" "tfer--subnet-0c6066202240c38be" {
  route_table_id = data.terraform_remote_state.route_table.outputs.aws_route_table_tfer--rtb-0c5895a403cd0d50e_id
  subnet_id      = data.terraform_remote_state.subnet.outputs.aws_subnet_tfer--subnet-0c6066202240c38be_id
}

resource "aws_route_table_association" "tfer--subnet-0c775a4f57dfa0e61" {
  route_table_id = data.terraform_remote_state.route_table.outputs.aws_route_table_tfer--rtb-06ca622706fb0676d_id
  subnet_id      = data.terraform_remote_state.subnet.outputs.aws_subnet_tfer--subnet-0c775a4f57dfa0e61_id
}

resource "aws_route_table_association" "tfer--subnet-0d4f42cf30b20c42d" {
  route_table_id = data.terraform_remote_state.route_table.outputs.aws_route_table_tfer--rtb-098958cd03a46c219_id
  subnet_id      = data.terraform_remote_state.subnet.outputs.aws_subnet_tfer--subnet-0d4f42cf30b20c42d_id
}

resource "aws_route_table_association" "tfer--subnet-0dacd0ef604204014" {
  route_table_id = data.terraform_remote_state.route_table.outputs.aws_route_table_tfer--rtb-09a828af05f32b0cf_id
  subnet_id      = data.terraform_remote_state.subnet.outputs.aws_subnet_tfer--subnet-0dacd0ef604204014_id
}

resource "aws_route_table_association" "tfer--subnet-0e2a201f2503f59ee" {
  route_table_id = data.terraform_remote_state.route_table.outputs.aws_route_table_tfer--rtb-015ae5889ca4aaadd_id
  subnet_id      = data.terraform_remote_state.subnet.outputs.aws_subnet_tfer--subnet-0e2a201f2503f59ee_id
}
