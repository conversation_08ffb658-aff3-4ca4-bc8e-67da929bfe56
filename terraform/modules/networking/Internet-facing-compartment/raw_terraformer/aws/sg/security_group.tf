resource "aws_security_group" "tfer--Database-SQL-infovault-gen-facing-compartment_sg-0d8a5fa845dca93c9" {
  description = "Database Security for SQL Server Database"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    cidr_blocks = ["0.0.0.0/0", "**********/24"]
    from_port   = "443"
    protocol    = "tcp"
    self        = "false"
    to_port     = "443"
  }

  ingress {
    cidr_blocks = ["**********/26"]
    description = "HTTPS from Management VPC"
    from_port   = "443"
    protocol    = "tcp"
    self        = "false"
    to_port     = "443"
  }

  ingress {
    cidr_blocks = ["**********/16"]
    from_port   = "10000"
    protocol    = "tcp"
    self        = "false"
    to_port     = "10000"
  }

  ingress {
    cidr_blocks = ["**********/16"]
    from_port   = "1111"
    protocol    = "tcp"
    self        = "false"
    to_port     = "1111"
  }

  ingress {
    cidr_blocks = ["**********/16"]
    from_port   = "6379"
    protocol    = "tcp"
    self        = "false"
    to_port     = "6379"
  }

  ingress {
    cidr_blocks = ["**********/24", "**********/24"]
    from_port   = "1433"
    protocol    = "tcp"
    self        = "false"
    to_port     = "1433"
  }

  ingress {
    cidr_blocks = ["**********/24"]
    from_port   = "80"
    protocol    = "tcp"
    self        = "false"
    to_port     = "80"
  }

  ingress {
    cidr_blocks = ["*************/32"]
    description = "SSH access from user IP"
    from_port   = "22"
    protocol    = "tcp"
    self        = "false"
    to_port     = "22"
  }

  ingress {
    cidr_blocks = ["*************/32"]
    description = "SSH access from Candor office"
    from_port   = "22"
    protocol    = "tcp"
    self        = "false"
    to_port     = "22"
  }

  ingress {
    cidr_blocks = ["************/32"]
    description = "SSH access from B-19 office"
    from_port   = "22"
    protocol    = "tcp"
    self        = "false"
    to_port     = "22"
  }

  ingress {
    description     = "EKS Cluster"
    from_port       = "0"
    protocol        = "-1"
    security_groups = ["${data.terraform_remote_state.sg.outputs.aws_security_group_tfer--eks-cluster-sg-infovault-dev-eks-cluster-v130-2076719636_sg-0950397297ecb203f_id}"]
    self            = "false"
    to_port         = "0"
  }

  ingress {
    description     = "ToolingServer-Win"
    from_port       = "1433"
    protocol        = "tcp"
    security_groups = ["${data.terraform_remote_state.sg.outputs.aws_security_group_tfer--infovault-mgmt-servers-sg_sg-095117b1a793c2ce0_id}"]
    self            = "false"
    to_port         = "1433"
  }

  name   = "Database-SQL-infovault-gen-facing-compartment"
  vpc_id = "vpc-05e0e8104b3321c40"
}

resource "aws_security_group" "tfer--GuardDutyManagedSecurityGroup-vpc-037f619602fa293a1_sg-0455dd83ca1b155d3" {
  description = "Associated with VPC-vpc-037f619602fa293a1 and tagged as GuardDutyManaged"

  ingress {
    cidr_blocks = ["**********/27"]
    description = "GuardDuty managed security group inbound rule associated with VPC vpc-037f619602fa293a1"
    from_port   = "443"
    protocol    = "tcp"
    self        = "false"
    to_port     = "443"
  }

  name = "GuardDutyManagedSecurityGroup-vpc-037f619602fa293a1"

  tags = {
    GuardDutyManaged = "true"
  }

  tags_all = {
    GuardDutyManaged = "true"
  }

  vpc_id = "vpc-037f619602fa293a1"
}

resource "aws_security_group" "tfer--GuardDutyManagedSecurityGroup-vpc-05e0e8104b3321c40_sg-0650a95e2c0c1aaf5" {
  description = "Associated with VPC-vpc-05e0e8104b3321c40 and tagged as GuardDutyManaged"

  ingress {
    cidr_blocks = ["*********/26", "**********/22"]
    description = "GuardDuty managed security group inbound rule associated with VPC vpc-05e0e8104b3321c40"
    from_port   = "443"
    protocol    = "tcp"
    self        = "false"
    to_port     = "443"
  }

  name = "GuardDutyManagedSecurityGroup-vpc-05e0e8104b3321c40"

  tags = {
    GuardDutyManaged = "true"
  }

  tags_all = {
    GuardDutyManaged = "true"
  }

  vpc_id = "vpc-05e0e8104b3321c40"
}

resource "aws_security_group" "tfer--GuardDutyManagedSecurityGroup-vpc-05f1b20a6634c6a38_sg-065d1e24d43126489" {
  description = "Associated with VPC-vpc-05f1b20a6634c6a38 and tagged as GuardDutyManaged"

  ingress {
    cidr_blocks = ["**********/26"]
    description = "GuardDuty managed security group inbound rule associated with VPC vpc-05f1b20a6634c6a38"
    from_port   = "443"
    protocol    = "tcp"
    self        = "false"
    to_port     = "443"
  }

  name = "GuardDutyManagedSecurityGroup-vpc-05f1b20a6634c6a38"

  tags = {
    GuardDutyManaged = "true"
  }

  tags_all = {
    GuardDutyManaged = "true"
  }

  vpc_id = "vpc-05f1b20a6634c6a38"
}

resource "aws_security_group" "tfer--GuardDutyManagedSecurityGroup-vpc-0ca0480cce82946b3_sg-0330a127b43c54ab2" {
  description = "Associated with VPC-vpc-0ca0480cce82946b3 and tagged as GuardDutyManaged"

  ingress {
    cidr_blocks = ["**********/27"]
    description = "GuardDuty managed security group inbound rule associated with VPC vpc-0ca0480cce82946b3"
    from_port   = "443"
    protocol    = "tcp"
    self        = "false"
    to_port     = "443"
  }

  name = "GuardDutyManagedSecurityGroup-vpc-0ca0480cce82946b3"

  tags = {
    GuardDutyManaged = "true"
  }

  tags_all = {
    GuardDutyManaged = "true"
  }

  vpc_id = "vpc-0ca0480cce82946b3"
}

resource "aws_security_group" "tfer--InfoVault-MigrationServer-SG_sg-0c081d79227db7d86" {
  description = "Security group for Migration Server"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "3389"
    protocol    = "tcp"
    self        = "false"
    to_port     = "3389"
  }

  ingress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "443"
    protocol    = "tcp"
    self        = "false"
    to_port     = "443"
  }

  ingress {
    cidr_blocks = ["10.0.0.0/8"]
    from_port   = "1433"
    protocol    = "tcp"
    self        = "false"
    to_port     = "1433"
  }

  ingress {
    cidr_blocks = ["10.0.0.0/8"]
    from_port   = "5985"
    protocol    = "tcp"
    self        = "false"
    to_port     = "5986"
  }

  name   = "InfoVault-MigrationServer-SG"
  vpc_id = "vpc-05e0e8104b3321c40"
}

resource "aws_security_group" "tfer--InfoVault-SPS2016-SG_sg-08b7a467b37acf0e7" {
  description = "Security group for SharePoint 2016 server"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "3389"
    protocol    = "tcp"
    self        = "false"
    to_port     = "3389"
  }

  ingress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "443"
    protocol    = "tcp"
    self        = "false"
    to_port     = "443"
  }

  ingress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "80"
    protocol    = "tcp"
    self        = "false"
    to_port     = "80"
  }

  ingress {
    cidr_blocks = ["10.0.0.0/8"]
    from_port   = "1433"
    protocol    = "tcp"
    self        = "false"
    to_port     = "1433"
  }

  ingress {
    cidr_blocks = ["10.0.0.0/8"]
    from_port   = "5985"
    protocol    = "tcp"
    self        = "false"
    to_port     = "5986"
  }

  name   = "InfoVault-SPS2016-SG"
  vpc_id = "vpc-037f619602fa293a1"
}

resource "aws_security_group" "tfer--ablrhf-general_sg-0a6d2925236dcbd18" {
  description = "Security group for general resources in the ABLRHF compartment"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    description = "Allow all outbound traffic"
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    cidr_blocks = ["*********/27"]
    description = "Allow all internal VPC traffic"
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  name = "ablrhf-general"

  tags = {
    AutoShutdown    = "true"
    Compartment     = "ABLRHF"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "ablrhf-general"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Compartment     = "ABLRHF"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "ablrhf-general"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_id = "vpc-0e75036a2ad9e5b4f"
}

resource "aws_security_group" "tfer--ablrhf-vpce_sg-082d3fc2b26b1004e" {
  description = "Security group for VPC endpoints in the ABLRHF compartment"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    description = "Allow all outbound traffic"
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    cidr_blocks = ["*********/27"]
    description = "Allow HTTPS from VPC CIDR"
    from_port   = "443"
    protocol    = "tcp"
    self        = "false"
    to_port     = "443"
  }

  name = "ablrhf-vpce"

  tags = {
    AutoShutdown    = "true"
    Compartment     = "ABLRHF"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "ablrhf-vpce"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Compartment     = "ABLRHF"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "ablrhf-vpce"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_id = "vpc-0e75036a2ad9e5b4f"
}

resource "aws_security_group" "tfer--d-9667b2da13_controllers_sg-0f48a9bd414cfd781" {
  description = "AWS created security group for d-9667b2da13 directory controllers"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    cidr_blocks = ["**********/26"]
    from_port   = "-1"
    protocol    = "icmp"
    self        = "false"
    to_port     = "-1"
  }

  ingress {
    cidr_blocks = ["**********/26"]
    from_port   = "1024"
    protocol    = "tcp"
    self        = "false"
    to_port     = "65535"
  }

  ingress {
    cidr_blocks = ["**********/26"]
    from_port   = "123"
    protocol    = "udp"
    self        = "false"
    to_port     = "123"
  }

  ingress {
    cidr_blocks = ["**********/26"]
    from_port   = "135"
    protocol    = "tcp"
    self        = "false"
    to_port     = "135"
  }

  ingress {
    cidr_blocks = ["**********/26"]
    from_port   = "138"
    protocol    = "udp"
    self        = "false"
    to_port     = "138"
  }

  ingress {
    cidr_blocks = ["**********/26"]
    from_port   = "3268"
    protocol    = "tcp"
    self        = "false"
    to_port     = "3269"
  }

  ingress {
    cidr_blocks = ["**********/26"]
    from_port   = "389"
    protocol    = "tcp"
    self        = "false"
    to_port     = "389"
  }

  ingress {
    cidr_blocks = ["**********/26"]
    from_port   = "389"
    protocol    = "udp"
    self        = "false"
    to_port     = "389"
  }

  ingress {
    cidr_blocks = ["**********/26"]
    from_port   = "445"
    protocol    = "tcp"
    self        = "false"
    to_port     = "445"
  }

  ingress {
    cidr_blocks = ["**********/26"]
    from_port   = "445"
    protocol    = "udp"
    self        = "false"
    to_port     = "445"
  }

  ingress {
    cidr_blocks = ["**********/26"]
    from_port   = "464"
    protocol    = "tcp"
    self        = "false"
    to_port     = "464"
  }

  ingress {
    cidr_blocks = ["**********/26"]
    from_port   = "464"
    protocol    = "udp"
    self        = "false"
    to_port     = "464"
  }

  ingress {
    cidr_blocks = ["**********/26"]
    from_port   = "53"
    protocol    = "tcp"
    self        = "false"
    to_port     = "53"
  }

  ingress {
    cidr_blocks = ["**********/26"]
    from_port   = "53"
    protocol    = "udp"
    self        = "false"
    to_port     = "53"
  }

  ingress {
    cidr_blocks = ["**********/26"]
    from_port   = "636"
    protocol    = "tcp"
    self        = "false"
    to_port     = "636"
  }

  ingress {
    cidr_blocks = ["**********/26"]
    from_port   = "88"
    protocol    = "tcp"
    self        = "false"
    to_port     = "88"
  }

  ingress {
    cidr_blocks = ["**********/26"]
    from_port   = "88"
    protocol    = "udp"
    self        = "false"
    to_port     = "88"
  }

  ingress {
    from_port = "0"
    protocol  = "-1"
    self      = "true"
    to_port   = "0"
  }

  name   = "d-9667b2da13_controllers"
  vpc_id = "vpc-05f1b20a6634c6a38"
}

resource "aws_security_group" "tfer--dam-agent-gateway-sg_sg-043af8ab2fe4ec50f" {
  description = "Security group for DAM Agent Gateway"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    cidr_blocks = ["**********/22"]
    from_port   = "22"
    protocol    = "tcp"
    self        = "false"
    to_port     = "22"
  }

  ingress {
    cidr_blocks = ["**********/22"]
    from_port   = "2812"
    protocol    = "tcp"
    self        = "false"
    to_port     = "2812"
  }

  ingress {
    cidr_blocks = ["**********/22"]
    from_port   = "443"
    protocol    = "tcp"
    self        = "false"
    to_port     = "443"
  }

  ingress {
    cidr_blocks = ["**********/22"]
    from_port   = "514"
    protocol    = "udp"
    self        = "false"
    to_port     = "514"
  }

  ingress {
    cidr_blocks = ["**********/22"]
    from_port   = "8083"
    protocol    = "tcp"
    self        = "false"
    to_port     = "8083"
  }

  name = "dam-agent-gateway-sg"

  tags = {
    Component = "DAM"
    Name      = "dam-agent-gateway-sg"
  }

  tags_all = {
    Component = "DAM"
    Name      = "dam-agent-gateway-sg"
  }

  vpc_id = "vpc-05e0e8104b3321c40"
}

resource "aws_security_group" "tfer--dam-gateway-dev-az1-sg_sg-0ebab82f4aa7f2a90" {
  description = "Security group for DAM Gateway in AZ1"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "443"
    protocol    = "tcp"
    self        = "false"
    to_port     = "443"
  }

  ingress {
    cidr_blocks = ["10.0.0.0/8"]
    from_port   = "22"
    protocol    = "tcp"
    self        = "false"
    to_port     = "22"
  }

  ingress {
    cidr_blocks = ["*********/24", "**********/26", "***********/24"]
    from_port   = "8083"
    protocol    = "tcp"
    self        = "false"
    to_port     = "8083"
  }

  name = "dam-gateway-dev-az1-sg"

  tags = {
    Name = "dam-gateway-dev-az1-sg"
  }

  tags_all = {
    Name = "dam-gateway-dev-az1-sg"
  }

  vpc_id = "vpc-05e0e8104b3321c40"
}

resource "aws_security_group" "tfer--dam-gateway-dev-az2-sg_sg-06546af93a8931ae6" {
  description = "Security group for DAM Gateway in AZ2"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "443"
    protocol    = "tcp"
    self        = "false"
    to_port     = "443"
  }

  ingress {
    cidr_blocks = ["10.0.0.0/8"]
    from_port   = "22"
    protocol    = "tcp"
    self        = "false"
    to_port     = "22"
  }

  ingress {
    cidr_blocks = ["*********/24", "**********/26", "***********/24"]
    from_port   = "8083"
    protocol    = "tcp"
    self        = "false"
    to_port     = "8083"
  }

  name = "dam-gateway-dev-az2-sg"

  tags = {
    Name = "dam-gateway-dev-az2-sg"
  }

  tags_all = {
    Name = "dam-gateway-dev-az2-sg"
  }

  vpc_id = "vpc-05e0e8104b3321c40"
}

resource "aws_security_group" "tfer--default_sg-010c8f879a2498c96" {
  description = "default VPC security group"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    from_port = "0"
    protocol  = "-1"
    self      = "true"
    to_port   = "0"
  }

  name   = "default"
  vpc_id = "vpc-0e75036a2ad9e5b4f"
}

resource "aws_security_group" "tfer--default_sg-011238e3ae52fd306" {
  description = "default VPC security group"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    from_port = "0"
    protocol  = "-1"
    self      = "true"
    to_port   = "0"
  }

  name   = "default"
  vpc_id = "vpc-02282d8d4c9c7764c"
}

resource "aws_security_group" "tfer--default_sg-019036bd03d215313" {
  description = "default VPC security group"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    from_port = "0"
    protocol  = "-1"
    self      = "true"
    to_port   = "0"
  }

  name   = "default"
  vpc_id = "vpc-05f1b20a6634c6a38"
}

resource "aws_security_group" "tfer--default_sg-0a2302a73783eb575" {
  description = "default VPC security group"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    from_port = "0"
    protocol  = "-1"
    self      = "true"
    to_port   = "0"
  }

  name   = "default"
  vpc_id = "vpc-05e0e8104b3321c40"
}

resource "aws_security_group" "tfer--default_sg-0b4e61e6b8c88c982" {
  description = "default VPC security group"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    from_port = "0"
    protocol  = "-1"
    self      = "true"
    to_port   = "0"
  }

  name   = "default"
  vpc_id = "vpc-037f619602fa293a1"
}

resource "aws_security_group" "tfer--default_sg-0b9bba8ae21bf0bbd" {
  description = "default VPC security group"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    from_port = "0"
    protocol  = "-1"
    self      = "true"
    to_port   = "0"
  }

  name   = "default"
  vpc_id = "vpc-0ca0480cce82946b3"
}

resource "aws_security_group" "tfer--dev-dra-admin-server-sg_sg-0b67c6b7904908adf" {
  description = "Security group for DRA Admin Server"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    description = "Allow all outbound traffic"
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    cidr_blocks = ["**********/26", "**********/22"]
    description = "HTTPS access from management networks"
    from_port   = "443"
    protocol    = "tcp"
    self        = "false"
    to_port     = "443"
  }

  name = "dev-dra-admin-server-sg"

  tags = {
    AutoShutdown    = "true"
    Component       = "DAM"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "dev-dra-admin-server-sg"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Component       = "DAM"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "dev-dra-admin-server-sg"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_id = "vpc-05f1b20a6634c6a38"
}

resource "aws_security_group" "tfer--dev-dra-analytics-server-sg_sg-0d7508facd515c5ea" {
  description = "Security group for DRA Analytics Server"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    description = "Allow all outbound traffic"
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    cidr_blocks = ["**********/26", "**********/22"]
    description = "HTTPS access from management networks"
    from_port   = "443"
    protocol    = "tcp"
    self        = "false"
    to_port     = "443"
  }

  name = "dev-dra-analytics-server-sg"

  tags = {
    AutoShutdown    = "true"
    Component       = "DAM"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "dev-dra-analytics-server-sg"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Component       = "DAM"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "dev-dra-analytics-server-sg"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_id = "vpc-05f1b20a6634c6a38"
}

resource "aws_security_group" "tfer--dev-management-server-sg_sg-0f2fdd192120dbf17" {
  description = "Security group for DAM Management Server (AVM150)"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    description = "Allow all outbound traffic"
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    cidr_blocks = ["**********/26", "**********/22"]
    description = "HTTPS access from management networks"
    from_port   = "443"
    protocol    = "tcp"
    self        = "false"
    to_port     = "443"
  }

  ingress {
    cidr_blocks = ["**********/26", "**********/22"]
    description = "Monit access from management networks"
    from_port   = "2812"
    protocol    = "tcp"
    self        = "false"
    to_port     = "2812"
  }

  ingress {
    cidr_blocks = ["**********/26"]
    description = "Management port 8081 from DB subnet"
    from_port   = "8081"
    protocol    = "tcp"
    self        = "false"
    to_port     = "8081"
  }

  ingress {
    cidr_blocks = ["**********/26"]
    description = "Management port 8083 from DB subnet"
    from_port   = "8083"
    protocol    = "tcp"
    self        = "false"
    to_port     = "8083"
  }

  ingress {
    cidr_blocks = ["**********/26"]
    description = "Management port 8084 from DB subnet"
    from_port   = "8084"
    protocol    = "tcp"
    self        = "false"
    to_port     = "8084"
  }

  ingress {
    cidr_blocks = ["**********/26"]
    description = "Management port 8085 from DB subnet"
    from_port   = "8085"
    protocol    = "tcp"
    self        = "false"
    to_port     = "8085"
  }

  ingress {
    cidr_blocks = ["**********/26"]
    description = "Syslog from DB subnet"
    from_port   = "514"
    protocol    = "udp"
    self        = "false"
    to_port     = "514"
  }

  name = "dev-management-server-sg"

  tags = {
    AutoShutdown    = "true"
    Component       = "DAM"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "dev-management-server-sg"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Component       = "DAM"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "dev-management-server-sg"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_id = "vpc-05f1b20a6634c6a38"
}

resource "aws_security_group" "tfer--dra-agent-gateway-sg_sg-02794d7f8eaa42fea" {
  description = "Security group for DRA Agent Gateway"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    cidr_blocks = ["**********/22"]
    from_port   = "22"
    protocol    = "tcp"
    self        = "false"
    to_port     = "22"
  }

  ingress {
    cidr_blocks = ["**********/22"]
    from_port   = "2812"
    protocol    = "tcp"
    self        = "false"
    to_port     = "2812"
  }

  ingress {
    cidr_blocks = ["**********/22"]
    from_port   = "443"
    protocol    = "tcp"
    self        = "false"
    to_port     = "443"
  }

  ingress {
    cidr_blocks = ["**********/22"]
    from_port   = "514"
    protocol    = "udp"
    self        = "false"
    to_port     = "514"
  }

  ingress {
    cidr_blocks = ["**********/22"]
    from_port   = "8083"
    protocol    = "tcp"
    self        = "false"
    to_port     = "8083"
  }

  name = "dra-agent-gateway-sg"

  tags = {
    Component = "DAM"
    Name      = "dra-agent-gateway-sg"
  }

  tags_all = {
    Component = "DAM"
    Name      = "dra-agent-gateway-sg"
  }

  vpc_id = "vpc-05e0e8104b3321c40"
}

resource "aws_security_group" "tfer--dra-gateway-dev-az1-sg_sg-03ee4d0a2a324d311" {
  description = "Security group for DRA Gateway in AZ1"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "443"
    protocol    = "tcp"
    self        = "false"
    to_port     = "443"
  }

  ingress {
    cidr_blocks = ["10.0.0.0/8"]
    from_port   = "22"
    protocol    = "tcp"
    self        = "false"
    to_port     = "22"
  }

  ingress {
    cidr_blocks = ["*********/24", "**********/26", "***********/24"]
    from_port   = "8084"
    protocol    = "tcp"
    self        = "false"
    to_port     = "8084"
  }

  name = "dra-gateway-dev-az1-sg"

  tags = {
    Name = "dra-gateway-dev-az1-sg"
  }

  tags_all = {
    Name = "dra-gateway-dev-az1-sg"
  }

  vpc_id = "vpc-05e0e8104b3321c40"
}

resource "aws_security_group" "tfer--dra-gateway-dev-az2-sg_sg-09b7938cd606d28b2" {
  description = "Security group for DRA Gateway in AZ2"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "443"
    protocol    = "tcp"
    self        = "false"
    to_port     = "443"
  }

  ingress {
    cidr_blocks = ["10.0.0.0/8"]
    from_port   = "22"
    protocol    = "tcp"
    self        = "false"
    to_port     = "22"
  }

  ingress {
    cidr_blocks = ["*********/24", "**********/26", "***********/24"]
    from_port   = "8084"
    protocol    = "tcp"
    self        = "false"
    to_port     = "8084"
  }

  name = "dra-gateway-dev-az2-sg"

  tags = {
    Name = "dra-gateway-dev-az2-sg"
  }

  tags_all = {
    Name = "dra-gateway-dev-az2-sg"
  }

  vpc_id = "vpc-05e0e8104b3321c40"
}

resource "aws_security_group" "tfer--eks-cluster-sg-infovault-dev-eks-cluster-v130-2076719636_sg-0950397297ecb203f" {
  description = "EKS created security group applied to ENI that is attached to EKS Control Plane master nodes, as well as any managed workloads."

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    cidr_blocks = ["0.0.0.0/0", "**********/24"]
    from_port   = "443"
    protocol    = "tcp"
    self        = "false"
    to_port     = "443"
  }

  ingress {
    cidr_blocks = ["**********/26"]
    description = "HTTPS from Management VPC"
    from_port   = "443"
    protocol    = "tcp"
    self        = "false"
    to_port     = "443"
  }

  ingress {
    cidr_blocks = ["**********/16"]
    from_port   = "10000"
    protocol    = "tcp"
    self        = "false"
    to_port     = "10000"
  }

  ingress {
    cidr_blocks = ["**********/16"]
    from_port   = "1111"
    protocol    = "tcp"
    self        = "false"
    to_port     = "1111"
  }

  ingress {
    cidr_blocks = ["**********/16"]
    from_port   = "6379"
    protocol    = "tcp"
    self        = "false"
    to_port     = "6379"
  }

  ingress {
    cidr_blocks = ["**********/24", "**********/24"]
    from_port   = "1433"
    protocol    = "tcp"
    self        = "false"
    to_port     = "1433"
  }

  ingress {
    cidr_blocks = ["**********/24"]
    from_port   = "80"
    protocol    = "tcp"
    self        = "false"
    to_port     = "80"
  }

  ingress {
    cidr_blocks = ["*************/32"]
    description = "SSH access from user IP"
    from_port   = "22"
    protocol    = "tcp"
    self        = "false"
    to_port     = "22"
  }

  ingress {
    cidr_blocks = ["*************/32"]
    description = "SSH access from Candor office"
    from_port   = "22"
    protocol    = "tcp"
    self        = "false"
    to_port     = "22"
  }

  ingress {
    cidr_blocks = ["************/32"]
    description = "SSH access from B-19 office"
    from_port   = "22"
    protocol    = "tcp"
    self        = "false"
    to_port     = "22"
  }

  ingress {
    description = "EKS Cluster"
    from_port   = "0"
    protocol    = "-1"
    self        = "true"
    to_port     = "0"
  }

  ingress {
    description     = "ToolingServer-Win"
    from_port       = "1433"
    protocol        = "tcp"
    security_groups = ["${data.terraform_remote_state.sg.outputs.aws_security_group_tfer--infovault-mgmt-servers-sg_sg-095117b1a793c2ce0_id}"]
    self            = "false"
    to_port         = "1433"
  }

  name = "eks-cluster-sg-infovault-dev-eks-cluster-v130-2076719636"

  tags = {
    Name                                                   = "eks-cluster-sg-infovault-dev-eks-cluster-v130-2076719636"
    "kubernetes.io/cluster/infovault-dev-eks-cluster-v130" = "owned"
  }

  tags_all = {
    Name                                                   = "eks-cluster-sg-infovault-dev-eks-cluster-v130-2076719636"
    "kubernetes.io/cluster/infovault-dev-eks-cluster-v130" = "owned"
  }

  vpc_id = "vpc-05e0e8104b3321c40"
}

resource "aws_security_group" "tfer--gen-alb-internet-facing-sg_sg-0c08c4e2aabf88592" {
  description = "Security group for internet-facing ALB"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  egress {
    cidr_blocks = ["**********/24"]
    from_port   = "443"
    protocol    = "tcp"
    self        = "false"
    to_port     = "443"
  }

  egress {
    cidr_blocks = ["**********/24"]
    from_port   = "80"
    protocol    = "tcp"
    self        = "false"
    to_port     = "80"
  }

  egress {
    from_port       = "30402"
    protocol        = "tcp"
    security_groups = ["${data.terraform_remote_state.sg.outputs.aws_security_group_tfer--infovault-eks-cluster-sg_sg-0353836b1b373e27d_id}"]
    self            = "false"
    to_port         = "30402"
  }

  egress {
    from_port       = "31460"
    protocol        = "tcp"
    security_groups = ["${data.terraform_remote_state.sg.outputs.aws_security_group_tfer--infovault-eks-cluster-sg_sg-0353836b1b373e27d_id}"]
    self            = "false"
    to_port         = "31460"
  }

  egress {
    from_port       = "31950"
    protocol        = "tcp"
    security_groups = ["${data.terraform_remote_state.sg.outputs.aws_security_group_tfer--infovault-eks-cluster-sg_sg-0353836b1b373e27d_id}"]
    self            = "false"
    to_port         = "31950"
  }

  ingress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "443"
    protocol    = "tcp"
    self        = "false"
    to_port     = "443"
  }

  ingress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "80"
    protocol    = "tcp"
    self        = "false"
    to_port     = "80"
  }

  name = "gen-alb-internet-facing-sg"

  tags = {
    AutoShutdown = "true"
    Environment  = "dev"
    Name         = "gen-alb-internet-facing-sg"
    Project      = "infovault"
  }

  tags_all = {
    AutoShutdown = "true"
    Environment  = "dev"
    Name         = "gen-alb-internet-facing-sg"
    Project      = "infovault"
  }

  vpc_id = "vpc-05e0e8104b3321c40"
}

resource "aws_security_group" "tfer--infovault-ad-sg_sg-03f591c0a35d3af71" {
  description = "Security group for Active Directory server"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    cidr_blocks = ["**********/26"]
    from_port   = "3268"
    protocol    = "tcp"
    self        = "false"
    to_port     = "3268"
  }

  ingress {
    cidr_blocks = ["**********/26"]
    from_port   = "3269"
    protocol    = "tcp"
    self        = "false"
    to_port     = "3269"
  }

  ingress {
    cidr_blocks = ["**********/26"]
    from_port   = "3389"
    protocol    = "tcp"
    self        = "false"
    to_port     = "3389"
  }

  ingress {
    cidr_blocks = ["**********/26"]
    from_port   = "389"
    protocol    = "tcp"
    self        = "false"
    to_port     = "389"
  }

  ingress {
    cidr_blocks = ["**********/26"]
    from_port   = "53"
    protocol    = "tcp"
    self        = "false"
    to_port     = "53"
  }

  ingress {
    cidr_blocks = ["**********/26"]
    from_port   = "53"
    protocol    = "udp"
    self        = "false"
    to_port     = "53"
  }

  ingress {
    cidr_blocks = ["**********/26"]
    from_port   = "5985"
    protocol    = "tcp"
    self        = "false"
    to_port     = "5985"
  }

  ingress {
    cidr_blocks = ["**********/26"]
    from_port   = "5986"
    protocol    = "tcp"
    self        = "false"
    to_port     = "5986"
  }

  ingress {
    cidr_blocks = ["**********/26"]
    from_port   = "636"
    protocol    = "tcp"
    self        = "false"
    to_port     = "636"
  }

  ingress {
    cidr_blocks = ["**********/26"]
    from_port   = "88"
    protocol    = "tcp"
    self        = "false"
    to_port     = "88"
  }

  ingress {
    cidr_blocks = ["**********/26"]
    from_port   = "88"
    protocol    = "udp"
    self        = "false"
    to_port     = "88"
  }

  name   = "infovault-ad-sg"
  vpc_id = "vpc-05f1b20a6634c6a38"
}

resource "aws_security_group" "tfer--infovault-ad-tooling-sg_sg-0511f8964219f7537" {
  description = "Security group for Windows tooling server in AD subnet"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "443"
    protocol    = "tcp"
    self        = "false"
    to_port     = "443"
  }

  ingress {
    cidr_blocks = ["**********/26"]
    from_port   = "-1"
    protocol    = "icmp"
    self        = "false"
    to_port     = "-1"
  }

  ingress {
    cidr_blocks = ["**********/26"]
    from_port   = "3389"
    protocol    = "tcp"
    self        = "false"
    to_port     = "3389"
  }

  ingress {
    cidr_blocks = ["**********/26"]
    from_port   = "5985"
    protocol    = "tcp"
    self        = "false"
    to_port     = "5985"
  }

  ingress {
    cidr_blocks = ["**********/26"]
    from_port   = "5986"
    protocol    = "tcp"
    self        = "false"
    to_port     = "5986"
  }

  name   = "infovault-ad-tooling-sg"
  vpc_id = "vpc-05f1b20a6634c6a38"
}

resource "aws_security_group" "tfer--infovault-alb-sg_sg-0d3cb3a1b9bea45d6" {
  description = "Security group for InfoVault ALB"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    cidr_blocks = ["**********/24", "**********/24"]
    from_port   = "10000"
    protocol    = "tcp"
    self        = "false"
    to_port     = "10000"
  }

  ingress {
    cidr_blocks = ["**********/24", "**********/24"]
    from_port   = "1111"
    protocol    = "tcp"
    self        = "false"
    to_port     = "1111"
  }

  ingress {
    cidr_blocks = ["**********/24", "**********/24"]
    from_port   = "1433"
    protocol    = "tcp"
    self        = "false"
    to_port     = "1433"
  }

  ingress {
    cidr_blocks = ["**********/24", "**********/24"]
    from_port   = "6379"
    protocol    = "tcp"
    self        = "false"
    to_port     = "6379"
  }

  name   = "infovault-alb-sg"
  vpc_id = "vpc-05e0e8104b3321c40"
}

resource "aws_security_group" "tfer--infovault-app-tier-sg_sg-095c31fa3b94c41ae" {
  description = "Security group for InfoVault application tier"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "443"
    protocol    = "tcp"
    self        = "false"
    to_port     = "443"
  }

  ingress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "443"
    protocol    = "tcp"
    self        = "false"
    to_port     = "443"
  }

  ingress {
    cidr_blocks     = ["10.0.0.0/8", "100.0.0.0/8"]
    from_port       = "111"
    protocol        = "tcp"
    security_groups = ["${data.terraform_remote_state.sg.outputs.aws_security_group_tfer--infovault-eks-cluster-sg_sg-0353836b1b373e27d_id}"]
    self            = "false"
    to_port         = "111"
  }

  ingress {
    cidr_blocks     = ["10.0.0.0/8", "100.0.0.0/8"]
    from_port       = "2049"
    protocol        = "tcp"
    security_groups = ["${data.terraform_remote_state.sg.outputs.aws_security_group_tfer--infovault-eks-cluster-sg_sg-0353836b1b373e27d_id}"]
    self            = "false"
    to_port         = "2049"
  }

  ingress {
    cidr_blocks = ["10.0.0.0/8", "*************/32"]
    from_port   = "22"
    protocol    = "tcp"
    self        = "false"
    to_port     = "22"
  }

  ingress {
    cidr_blocks = ["**********/16"]
    description = "NFS TCP from EKS"
    from_port   = "2049"
    protocol    = "tcp"
    self        = "false"
    to_port     = "2049"
  }

  ingress {
    cidr_blocks = ["**********/16"]
    description = "NFS UDP from EKS"
    from_port   = "2049"
    protocol    = "udp"
    self        = "false"
    to_port     = "2049"
  }

  ingress {
    cidr_blocks = ["**********/16"]
    description = "RPC Portmapper TCP from EKS"
    from_port   = "111"
    protocol    = "tcp"
    self        = "false"
    to_port     = "111"
  }

  ingress {
    cidr_blocks = ["**********/16"]
    description = "RPC Portmapper UDP from EKS"
    from_port   = "111"
    protocol    = "udp"
    self        = "false"
    to_port     = "111"
  }

  ingress {
    cidr_blocks = ["*************/32"]
    description = "SSH access from Candor office"
    from_port   = "22"
    protocol    = "tcp"
    self        = "false"
    to_port     = "22"
  }

  ingress {
    cidr_blocks = ["************/32"]
    description = "SSH access from B-19 office"
    from_port   = "22"
    protocol    = "tcp"
    self        = "false"
    to_port     = "22"
  }

  name = "infovault-app-tier-sg"

  tags = {
    AutoShutdown = "true"
    Environment  = "dev"
    Name         = "infovault-app-tier-sg"
    Project      = "InfoVault"
  }

  tags_all = {
    AutoShutdown = "true"
    Environment  = "dev"
    Name         = "infovault-app-tier-sg"
    Project      = "InfoVault"
  }

  vpc_id = "vpc-05e0e8104b3321c40"
}

resource "aws_security_group" "tfer--infovault-dev-alb-sg_sg-0b9856c92519afb2f" {
  description = "Security group for Application Load Balancer"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    description = "Allow all outbound traffic"
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    cidr_blocks = ["0.0.0.0/0"]
    description = "HTTP from anywhere"
    from_port   = "80"
    protocol    = "tcp"
    self        = "false"
    to_port     = "80"
  }

  ingress {
    cidr_blocks = ["0.0.0.0/0"]
    description = "HTTPS from anywhere"
    from_port   = "443"
    protocol    = "tcp"
    self        = "false"
    to_port     = "443"
  }

  name = "infovault-dev-alb-sg"

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-alb-sg"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-alb-sg"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_id = "vpc-0e75036a2ad9e5b4f"
}

resource "aws_security_group" "tfer--infovault-dev-alb-sg_sg-0c2c1a0a80463a69b" {
  description = "Security group for Application Load Balancer"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    description = "Allow all outbound traffic"
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    cidr_blocks = ["0.0.0.0/0"]
    description = "HTTP from anywhere"
    from_port   = "80"
    protocol    = "tcp"
    self        = "false"
    to_port     = "80"
  }

  ingress {
    cidr_blocks = ["0.0.0.0/0"]
    description = "HTTPS from anywhere"
    from_port   = "443"
    protocol    = "tcp"
    self        = "false"
    to_port     = "443"
  }

  name = "infovault-dev-alb-sg"

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-alb-sg"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-alb-sg"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_id = "vpc-037f619602fa293a1"
}

resource "aws_security_group" "tfer--infovault-dev-alb-sg_sg-0e4c62110e9593bfe" {
  description = "Security group for Application Load Balancer"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    description = "Allow all outbound traffic"
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    cidr_blocks = ["0.0.0.0/0"]
    description = "HTTP from anywhere"
    from_port   = "80"
    protocol    = "tcp"
    self        = "false"
    to_port     = "80"
  }

  ingress {
    cidr_blocks = ["0.0.0.0/0"]
    description = "HTTPS from anywhere"
    from_port   = "443"
    protocol    = "tcp"
    self        = "false"
    to_port     = "443"
  }

  name = "infovault-dev-alb-sg"

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-alb-sg"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-alb-sg"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_id = "vpc-05f1b20a6634c6a38"
}

resource "aws_security_group" "tfer--infovault-dev-alb-sg_sg-0e921258047172c67" {
  description = "Security group for Application Load Balancer"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    description = "Allow all outbound traffic"
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    cidr_blocks = ["0.0.0.0/0"]
    description = "HTTP from anywhere"
    from_port   = "80"
    protocol    = "tcp"
    self        = "false"
    to_port     = "80"
  }

  ingress {
    cidr_blocks = ["0.0.0.0/0"]
    description = "HTTPS from anywhere"
    from_port   = "443"
    protocol    = "tcp"
    self        = "false"
    to_port     = "443"
  }

  ingress {
    cidr_blocks = ["**********/26"]
    description = "Allow HTTPS from management VPC"
    from_port   = "443"
    protocol    = "tcp"
    self        = "false"
    to_port     = "443"
  }

  name = "infovault-dev-alb-sg"

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-alb-sg"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-alb-sg"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_id = "vpc-05e0e8104b3321c40"
}

resource "aws_security_group" "tfer--infovault-dev-anfw-endpoint-sg_sg-0015e747db8058575" {
  description = "Security group for AWS Network Firewall endpoints"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    description = "Allow all outbound traffic"
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    cidr_blocks = ["*********/26"]
    description = "Allow all inbound traffic"
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  name = "infovault-dev-anfw-endpoint-sg"

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-anfw-endpoint-sg"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-anfw-endpoint-sg"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_id = "vpc-05e0e8104b3321c40"
}

resource "aws_security_group" "tfer--infovault-dev-anfw-endpoint-sg_sg-02dba0def0203d5a0" {
  description = "Security group for AWS Network Firewall endpoints"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    description = "Allow all outbound traffic"
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    cidr_blocks = ["**********/26"]
    description = "Allow all inbound traffic"
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  name = "infovault-dev-anfw-endpoint-sg"

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-anfw-endpoint-sg"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-anfw-endpoint-sg"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_id = "vpc-05f1b20a6634c6a38"
}

resource "aws_security_group" "tfer--infovault-dev-anfw-endpoint-sg_sg-038c81aff399e6abb" {
  description = "Security group for AWS Network Firewall endpoints"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    description = "Allow all outbound traffic"
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    cidr_blocks = ["*********/27"]
    description = "Allow all inbound traffic"
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  name = "infovault-dev-anfw-endpoint-sg"

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-anfw-endpoint-sg"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-anfw-endpoint-sg"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_id = "vpc-0e75036a2ad9e5b4f"
}

resource "aws_security_group" "tfer--infovault-dev-anfw-endpoint-sg_sg-0e2bbdad5eddfe99c" {
  description = "Security group for AWS Network Firewall endpoints"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    description = "Allow all outbound traffic"
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    cidr_blocks = ["**********/27"]
    description = "Allow all inbound traffic"
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  name = "infovault-dev-anfw-endpoint-sg"

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "dev-sg-net-anfw-endpoint"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "dev-sg-net-anfw-endpoint"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_id = "vpc-037f619602fa293a1"
}

resource "aws_security_group" "tfer--infovault-dev-gen-facing-compartment-alb-sg_sg-0acb991f6917ba443" {
  description = "Allow specific traffic for ALB in infovault-dev-gen-facing-compartment"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "443"
    protocol    = "tcp"
    self        = "false"
    to_port     = "443"
  }

  ingress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "80"
    protocol    = "tcp"
    self        = "false"
    to_port     = "80"
  }

  name = "infovault-dev-gen-facing-compartment-alb-sg"

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-gen-facing-compartment-alb-sg"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-gen-facing-compartment-alb-sg"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_id = "vpc-05e0e8104b3321c40"
}

resource "aws_security_group" "tfer--infovault-dev-gen-facing-compartment-squid-sg_sg-011e29a277dde9f4a" {
  description = "Allow specific traffic for Squid Proxy in infovault-dev-gen-facing-compartment"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    cidr_blocks = ["*********/26"]
    description = "Squid Proxy from GEN-facing VPC"
    from_port   = "3128"
    protocol    = "tcp"
    self        = "false"
    to_port     = "3128"
  }

  ingress {
    cidr_blocks = ["*********/27"]
    description = "Squid Proxy from ABLRHF VPC"
    from_port   = "3128"
    protocol    = "tcp"
    self        = "false"
    to_port     = "3128"
  }

  ingress {
    cidr_blocks = ["**********/22"]
    description = "Squid Proxy from App-DB VPC"
    from_port   = "3128"
    protocol    = "tcp"
    self        = "false"
    to_port     = "3128"
  }

  name = "infovault-dev-gen-facing-compartment-squid-sg"

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-gen-facing-compartment-squid-sg"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-gen-facing-compartment-squid-sg"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_id = "vpc-05e0e8104b3321c40"
}

resource "aws_security_group" "tfer--infovault-dev-gen-facing-compartment-stfp-sg_sg-06936b8418430a397" {
  description = "Allow specific traffic for STFP Server in infovault-dev-gen-facing-compartment"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    description     = "Allow SFTP from ALB SG"
    from_port       = "22"
    protocol        = "tcp"
    security_groups = ["${data.terraform_remote_state.sg.outputs.aws_security_group_tfer--infovault-dev-gen-facing-compartment-alb-sg_sg-0acb991f6917ba443_id}"]
    self            = "false"
    to_port         = "22"
  }

  name = "infovault-dev-gen-facing-compartment-stfp-sg"

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-gen-facing-compartment-stfp-sg"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-gen-facing-compartment-stfp-sg"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_id = "vpc-05e0e8104b3321c40"
}

resource "aws_security_group" "tfer--infovault-dev-gen-facing-compartment-vpce-sg_sg-066975c77a66a64d9" {
  description = "Allow HTTPS for VPC Endpoints in infovault-dev-gen-facing-compartment"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    cidr_blocks = ["*********/26", "**********/22"]
    from_port   = "443"
    protocol    = "tcp"
    self        = "false"
    to_port     = "443"
  }

  name = "infovault-dev-gen-facing-compartment-vpce-sg"

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-gen-facing-compartment-vpce-sg"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-gen-facing-compartment-vpce-sg"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_id = "vpc-05e0e8104b3321c40"
}

resource "aws_security_group" "tfer--infovault-dev-rds-sg_sg-02f66e75b6d635108" {
  description = "Security group for RDS instances"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    description = "Allow all outbound traffic"
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  name = "infovault-dev-rds-sg"

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-rds-sg"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-rds-sg"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_id = "vpc-05e0e8104b3321c40"
}

resource "aws_security_group" "tfer--infovault-dev-rds-sg_sg-09780ff2c4d7753e1" {
  description = "Security group for RDS instances"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    description = "Allow all outbound traffic"
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  name = "infovault-dev-rds-sg"

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-rds-sg"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-rds-sg"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_id = "vpc-0e75036a2ad9e5b4f"
}

resource "aws_security_group" "tfer--infovault-dev-rds-sg_sg-0dd095db031d21cf3" {
  description = "Security group for RDS instances"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    description = "Allow all outbound traffic"
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  name = "infovault-dev-rds-sg"

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-rds-sg"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-rds-sg"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_id = "vpc-05f1b20a6634c6a38"
}

resource "aws_security_group" "tfer--infovault-dev-rds-sg_sg-0e904d365617c1e2d" {
  description = "Security group for RDS instances"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    description = "Allow all outbound traffic"
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  name = "infovault-dev-rds-sg"

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-rds-sg"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-dev-rds-sg"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_id = "vpc-037f619602fa293a1"
}

resource "aws_security_group" "tfer--infovault-eks-cluster-sg_sg-0353836b1b373e27d" {
  description = "Security group for EKS cluster control plane"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "22"
    protocol    = "tcp"
    self        = "false"
    to_port     = "22"
  }

  ingress {
    cidr_blocks = ["**********/26"]
    description = "Allow NodePort access from management server"
    from_port   = "30000"
    protocol    = "tcp"
    self        = "false"
    to_port     = "33000"
  }

  ingress {
    cidr_blocks = ["**********/26"]
    description = "Allow access from management server"
    from_port   = "8080"
    protocol    = "tcp"
    self        = "false"
    to_port     = "8080"
  }

  ingress {
    cidr_blocks = ["**********/24"]
    from_port   = "443"
    protocol    = "tcp"
    self        = "false"
    to_port     = "443"
  }

  ingress {
    cidr_blocks = ["**********/24"]
    from_port   = "80"
    protocol    = "tcp"
    self        = "false"
    to_port     = "80"
  }

  ingress {
    cidr_blocks     = ["**********/26", "***********/26"]
    from_port       = "10000"
    protocol        = "tcp"
    security_groups = ["${data.terraform_remote_state.sg.outputs.aws_security_group_tfer--infovault-alb-sg_sg-0d3cb3a1b9bea45d6_id}"]
    self            = "false"
    to_port         = "10000"
  }

  ingress {
    cidr_blocks     = ["**********/26", "***********/26"]
    from_port       = "1111"
    protocol        = "tcp"
    security_groups = ["${data.terraform_remote_state.sg.outputs.aws_security_group_tfer--infovault-alb-sg_sg-0d3cb3a1b9bea45d6_id}"]
    self            = "false"
    to_port         = "1111"
  }

  ingress {
    cidr_blocks     = ["**********/26", "***********/26"]
    from_port       = "6379"
    protocol        = "tcp"
    security_groups = ["${data.terraform_remote_state.sg.outputs.aws_security_group_tfer--infovault-alb-sg_sg-0d3cb3a1b9bea45d6_id}"]
    self            = "false"
    to_port         = "6379"
  }

  ingress {
    description     = "Allow Automation Studio NodePort access from Windows management server"
    from_port       = "31798"
    protocol        = "tcp"
    security_groups = ["${data.terraform_remote_state.sg.outputs.aws_security_group_tfer--infovault-mgmt-servers-sg_sg-095117b1a793c2ce0_id}"]
    self            = "false"
    to_port         = "31798"
  }

  ingress {
    description     = "Allow HTTP traffic from internet-facing ALB to Automation Studio application"
    from_port       = "31798"
    protocol        = "tcp"
    security_groups = ["${data.terraform_remote_state.sg.outputs.aws_security_group_tfer--gen-alb-internet-facing-sg_sg-0c08c4e2aabf88592_id}"]
    self            = "false"
    to_port         = "31798"
  }

  ingress {
    description     = "Allow HTTP traffic from internet-facing ALB to RMS application"
    from_port       = "31697"
    protocol        = "tcp"
    security_groups = ["${data.terraform_remote_state.sg.outputs.aws_security_group_tfer--gen-alb-internet-facing-sg_sg-0c08c4e2aabf88592_id}"]
    self            = "false"
    to_port         = "31697"
  }

  ingress {
    description     = "Allow HTTP traffic from internet-facing ALB to Workspace Studio application"
    from_port       = "31670"
    protocol        = "tcp"
    security_groups = ["${data.terraform_remote_state.sg.outputs.aws_security_group_tfer--gen-alb-internet-facing-sg_sg-0c08c4e2aabf88592_id}"]
    self            = "false"
    to_port         = "31670"
  }

  ingress {
    description     = "Allow RMS Web NodePort access from Windows management server"
    from_port       = "31697"
    protocol        = "tcp"
    security_groups = ["${data.terraform_remote_state.sg.outputs.aws_security_group_tfer--infovault-mgmt-servers-sg_sg-095117b1a793c2ce0_id}"]
    self            = "false"
    to_port         = "31697"
  }

  ingress {
    description     = "Allow Workspace Studio NodePort access from Windows management server"
    from_port       = "31670"
    protocol        = "tcp"
    security_groups = ["${data.terraform_remote_state.sg.outputs.aws_security_group_tfer--infovault-mgmt-servers-sg_sg-095117b1a793c2ce0_id}"]
    self            = "false"
    to_port         = "31670"
  }

  ingress {
    description     = "ToolingServer-Win"
    from_port       = "443"
    protocol        = "tcp"
    security_groups = ["${data.terraform_remote_state.sg.outputs.aws_security_group_tfer--infovault-mgmt-servers-sg_sg-095117b1a793c2ce0_id}"]
    self            = "false"
    to_port         = "443"
  }

  ingress {
    description     = "ToolingServer-Win"
    from_port       = "80"
    protocol        = "tcp"
    security_groups = ["${data.terraform_remote_state.sg.outputs.aws_security_group_tfer--infovault-mgmt-servers-sg_sg-095117b1a793c2ce0_id}"]
    self            = "false"
    to_port         = "80"
  }

  ingress {
    from_port       = "0"
    protocol        = "-1"
    security_groups = ["${data.terraform_remote_state.sg.outputs.aws_security_group_tfer--eks-cluster-sg-infovault-dev-eks-cluster-v130-2076719636_sg-0950397297ecb203f_id}"]
    self            = "true"
    to_port         = "0"
  }

  ingress {
    from_port       = "30402"
    protocol        = "tcp"
    security_groups = ["${data.terraform_remote_state.sg.outputs.aws_security_group_tfer--gen-alb-internet-facing-sg_sg-0c08c4e2aabf88592_id}"]
    self            = "false"
    to_port         = "30402"
  }

  ingress {
    from_port       = "31460"
    protocol        = "tcp"
    security_groups = ["${data.terraform_remote_state.sg.outputs.aws_security_group_tfer--gen-alb-internet-facing-sg_sg-0c08c4e2aabf88592_id}"]
    self            = "false"
    to_port         = "31460"
  }

  ingress {
    from_port       = "31950"
    protocol        = "tcp"
    security_groups = ["${data.terraform_remote_state.sg.outputs.aws_security_group_tfer--gen-alb-internet-facing-sg_sg-0c08c4e2aabf88592_id}"]
    self            = "false"
    to_port         = "31950"
  }

  name = "infovault-eks-cluster-sg"

  tags = {
    AutoShutdown = "true"
    Environment  = "dev"
    Name         = "infovault-eks-cluster-sg"
    Owner        = "Temus"
    Project      = "InfoVault"
  }

  tags_all = {
    AutoShutdown = "true"
    Environment  = "dev"
    Name         = "infovault-eks-cluster-sg"
    Owner        = "Temus"
    Project      = "InfoVault"
  }

  vpc_id = "vpc-05e0e8104b3321c40"
}

resource "aws_security_group" "tfer--infovault-eks-node-sg_sg-05f09a0dc7ab8c53f" {
  description = "Security group for EKS worker nodes"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "22"
    protocol    = "tcp"
    self        = "false"
    to_port     = "22"
  }

  ingress {
    from_port       = "0"
    protocol        = "-1"
    security_groups = ["${data.terraform_remote_state.sg.outputs.aws_security_group_tfer--infovault-eks-cluster-sg_sg-0353836b1b373e27d_id}"]
    self            = "true"
    to_port         = "0"
  }

  name = "infovault-eks-node-sg"

  tags = {
    AutoShutdown = "true"
    Environment  = "dev"
    Name         = "infovault-eks-node-sg"
    Owner        = "Temus"
    Project      = "InfoVault"
  }

  tags_all = {
    AutoShutdown = "true"
    Environment  = "dev"
    Name         = "infovault-eks-node-sg"
    Owner        = "Temus"
    Project      = "InfoVault"
  }

  vpc_id = "vpc-05e0e8104b3321c40"
}

resource "aws_security_group" "tfer--infovault-mgmt-gitlab-sg_sg-00df94fe66c7464f5" {
  description = "Security group for GitLab Runner"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    description = "HTTP"
    from_port   = "80"
    protocol    = "tcp"
    self        = "false"
    to_port     = "80"
  }

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    description = "HTTPS"
    from_port   = "443"
    protocol    = "tcp"
    self        = "false"
    to_port     = "443"
  }

  egress {
    cidr_blocks = ["**********/27"]
    description = "HTTP Proxy"
    from_port   = "3128"
    protocol    = "tcp"
    self        = "false"
    to_port     = "3128"
  }

  ingress {
    cidr_blocks = ["**********/28", "**********/27"]
    from_port   = "80"
    protocol    = "tcp"
    self        = "false"
    to_port     = "80"
  }

  ingress {
    cidr_blocks = ["**********/28"]
    description = "ICMP from Management subnet"
    from_port   = "-1"
    protocol    = "icmp"
    self        = "false"
    to_port     = "-1"
  }

  ingress {
    cidr_blocks = ["**********/28"]
    description = "SSH from Management subnet"
    from_port   = "22"
    protocol    = "tcp"
    self        = "false"
    to_port     = "22"
  }

  ingress {
    cidr_blocks = ["**********/22"]
    description = "HTTPS from App-DB VPC"
    from_port   = "443"
    protocol    = "tcp"
    self        = "false"
    to_port     = "443"
  }

  ingress {
    from_port = "443"
    protocol  = "tcp"
    self      = "true"
    to_port   = "443"
  }

  name = "infovault-mgmt-gitlab-sg"

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-mgmt-gitlab-sg"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-mgmt-gitlab-sg"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_id = "vpc-05f1b20a6634c6a38"
}

resource "aws_security_group" "tfer--infovault-mgmt-servers-sg_sg-095117b1a793c2ce0" {
  description = "Security group for Management servers"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    description = "HTTP"
    from_port   = "80"
    protocol    = "tcp"
    self        = "false"
    to_port     = "80"
  }

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    description = "HTTPS"
    from_port   = "443"
    protocol    = "tcp"
    self        = "false"
    to_port     = "443"
  }

  egress {
    cidr_blocks = ["*********/26"]
    description = "HTTPS to Gen Facing VPC Primary CIDR"
    from_port   = "443"
    protocol    = "tcp"
    self        = "false"
    to_port     = "443"
  }

  egress {
    cidr_blocks = ["**********/22"]
    description = "Allow access to pod port"
    from_port   = "8080"
    protocol    = "tcp"
    self        = "false"
    to_port     = "8080"
  }

  egress {
    cidr_blocks = ["**********/22"]
    description = "Allow outbound access to EKS NodePorts"
    from_port   = "30000"
    protocol    = "tcp"
    self        = "false"
    to_port     = "33000"
  }

  egress {
    cidr_blocks = ["**********/22"]
    description = "HTTPS to Gen Facing VPC Secondary CIDR"
    from_port   = "443"
    protocol    = "tcp"
    self        = "false"
    to_port     = "443"
  }

  egress {
    cidr_blocks = ["**********/27"]
    description = "HTTP Proxy"
    from_port   = "3128"
    protocol    = "tcp"
    self        = "false"
    to_port     = "3128"
  }

  ingress {
    cidr_blocks = ["**********/26"]
    from_port   = "5985"
    protocol    = "tcp"
    self        = "false"
    to_port     = "5985"
  }

  ingress {
    cidr_blocks = ["**********/26"]
    from_port   = "5986"
    protocol    = "tcp"
    self        = "false"
    to_port     = "5986"
  }

  ingress {
    cidr_blocks = ["**********/22"]
    description = "ICMP from App-DB VPC"
    from_port   = "-1"
    protocol    = "icmp"
    self        = "false"
    to_port     = "-1"
  }

  ingress {
    cidr_blocks = ["**********/22"]
    description = "RDP from App-DB VPC"
    from_port   = "3389"
    protocol    = "tcp"
    self        = "false"
    to_port     = "3389"
  }

  ingress {
    cidr_blocks = ["**********/22"]
    description = "SSH from App-DB VPC"
    from_port   = "22"
    protocol    = "tcp"
    self        = "false"
    to_port     = "22"
  }

  name = "infovault-mgmt-servers-sg"

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-mgmt-servers-sg"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-mgmt-servers-sg"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_id = "vpc-05f1b20a6634c6a38"
}

resource "aws_security_group" "tfer--infovault-mgmt-vpce-sg_sg-0d28b5ada1bb60c9a" {
  description = "Security group for VPC Endpoints"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    description = "HTTPS"
    from_port   = "443"
    protocol    = "tcp"
    self        = "false"
    to_port     = "443"
  }

  ingress {
    cidr_blocks = ["**********/28"]
    description = "HTTPS from Management subnet"
    from_port   = "443"
    protocol    = "tcp"
    self        = "false"
    to_port     = "443"
  }

  ingress {
    cidr_blocks = ["***********/28"]
    description = "HTTPS From AD Client Subnet"
    from_port   = "443"
    protocol    = "tcp"
    self        = "false"
    to_port     = "443"
  }

  name = "infovault-mgmt-vpce-sg"

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-mgmt-vpce-sg"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "infovault-mgmt-vpce-sg"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_id = "vpc-05f1b20a6634c6a38"
}

resource "aws_security_group" "tfer--net-squid-sg_sg-00b66d34fb63637f3" {
  description = "Security group for Squid proxy"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    description = "Allow HTTP outbound traffic"
    from_port   = "80"
    protocol    = "tcp"
    self        = "false"
    to_port     = "80"
  }

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    description = "Allow HTTPS outbound traffic"
    from_port   = "443"
    protocol    = "tcp"
    self        = "false"
    to_port     = "443"
  }

  ingress {
    cidr_blocks = ["*********/22"]
    description = "Allow HTTP proxy from App-DB VPC"
    from_port   = "3128"
    protocol    = "tcp"
    self        = "false"
    to_port     = "3128"
  }

  ingress {
    cidr_blocks = ["**********/27"]
    description = "Allow HTTP proxy from Patching VPC"
    from_port   = "3128"
    protocol    = "tcp"
    self        = "false"
    to_port     = "3128"
  }

  ingress {
    cidr_blocks = ["**********/26"]
    description = "Allow HTTP proxy from Management VPC"
    from_port   = "3128"
    protocol    = "tcp"
    self        = "false"
    to_port     = "3128"
  }

  name = "net-squid-sg"

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "dev-net-squid-sg"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "dev-net-squid-sg"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_id = "vpc-037f619602fa293a1"
}

resource "aws_security_group" "tfer--net-squid-sg_sg-01c71a601fbce5d26" {
  description = "Security group for Squid proxy"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    description = "Allow HTTP outbound traffic"
    from_port   = "80"
    protocol    = "tcp"
    self        = "false"
    to_port     = "80"
  }

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    description = "Allow HTTPS outbound traffic"
    from_port   = "443"
    protocol    = "tcp"
    self        = "false"
    to_port     = "443"
  }

  ingress {
    cidr_blocks = ["*********/22"]
    description = "Allow HTTP proxy from App-DB VPC"
    from_port   = "3128"
    protocol    = "tcp"
    self        = "false"
    to_port     = "3128"
  }

  ingress {
    cidr_blocks = ["**********/27"]
    description = "Allow HTTP proxy from Patching VPC"
    from_port   = "3128"
    protocol    = "tcp"
    self        = "false"
    to_port     = "3128"
  }

  ingress {
    cidr_blocks = ["**********/26"]
    description = "Allow HTTP proxy from Management VPC"
    from_port   = "3128"
    protocol    = "tcp"
    self        = "false"
    to_port     = "3128"
  }

  name = "net-squid-sg"

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "dev-net-squid-sg"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "dev-net-squid-sg"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_id = "vpc-0e75036a2ad9e5b4f"
}

resource "aws_security_group" "tfer--net-squid-sg_sg-0906e124ed285bd7e" {
  description = "Security group for Squid proxy"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    description = "Allow HTTP outbound traffic"
    from_port   = "80"
    protocol    = "tcp"
    self        = "false"
    to_port     = "80"
  }

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    description = "Allow HTTPS outbound traffic"
    from_port   = "443"
    protocol    = "tcp"
    self        = "false"
    to_port     = "443"
  }

  ingress {
    cidr_blocks = ["*********/22"]
    description = "Allow HTTP proxy from App-DB VPC"
    from_port   = "3128"
    protocol    = "tcp"
    self        = "false"
    to_port     = "3128"
  }

  ingress {
    cidr_blocks = ["**********/27"]
    description = "Allow HTTP proxy from Patching VPC"
    from_port   = "3128"
    protocol    = "tcp"
    self        = "false"
    to_port     = "3128"
  }

  ingress {
    cidr_blocks = ["**********/26"]
    description = "Allow HTTP proxy from Management VPC"
    from_port   = "3128"
    protocol    = "tcp"
    self        = "false"
    to_port     = "3128"
  }

  name = "net-squid-sg"

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "dev-net-squid-sg"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "dev-net-squid-sg"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_id = "vpc-05e0e8104b3321c40"
}

resource "aws_security_group" "tfer--net-squid-sg_sg-097db697cb7a5f558" {
  description = "Security group for Squid proxy"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    description = "Allow HTTP outbound traffic"
    from_port   = "80"
    protocol    = "tcp"
    self        = "false"
    to_port     = "80"
  }

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    description = "Allow HTTPS outbound traffic"
    from_port   = "443"
    protocol    = "tcp"
    self        = "false"
    to_port     = "443"
  }

  ingress {
    cidr_blocks = ["*********/22"]
    description = "Allow HTTP proxy from App-DB VPC"
    from_port   = "3128"
    protocol    = "tcp"
    self        = "false"
    to_port     = "3128"
  }

  ingress {
    cidr_blocks = ["**********/27"]
    description = "Allow HTTP proxy from Patching VPC"
    from_port   = "3128"
    protocol    = "tcp"
    self        = "false"
    to_port     = "3128"
  }

  ingress {
    cidr_blocks = ["**********/26"]
    description = "Allow HTTP proxy from Management VPC"
    from_port   = "3128"
    protocol    = "tcp"
    self        = "false"
    to_port     = "3128"
  }

  name = "net-squid-sg"

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "dev-net-squid-sg"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "dev-net-squid-sg"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_id = "vpc-05f1b20a6634c6a38"
}

resource "aws_security_group" "tfer--patching-rhel-repo-sg_sg-01f0c371bd11aa6f1" {
  description = "Security group for RHEL Repo server in Patching Compartment"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    description = "Allow all outbound traffic"
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    cidr_blocks = ["**********/27"]
    description = "Placeholder - Allow HTTP from VPC (for clients - adjust as needed)"
    from_port   = "80"
    protocol    = "tcp"
    self        = "false"
    to_port     = "80"
  }

  ingress {
    cidr_blocks = ["**********/27"]
    description = "Placeholder - Allow HTTPS from VPC (for clients - adjust as needed)"
    from_port   = "443"
    protocol    = "tcp"
    self        = "false"
    to_port     = "443"
  }

  name = "patching-rhel-repo-sg"

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "sg-patching-rhel-repo"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "sg-patching-rhel-repo"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_id = "vpc-0ca0480cce82946b3"
}

resource "aws_security_group" "tfer--patching-vpce-sg_sg-0870c466d0db940c4" {
  description = "Security group for VPC Endpoints in Patching Compartment"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    description = "Allow all outbound traffic"
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    cidr_blocks = ["**********/27"]
    description = "Allow HTTPS from within the VPC for VPC Endpoints"
    from_port   = "443"
    protocol    = "tcp"
    self        = "false"
    to_port     = "443"
  }

  name = "patching-vpce-sg"

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "sg-patching-vpce"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "sg-patching-vpce"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_id = "vpc-0ca0480cce82946b3"
}

resource "aws_security_group" "tfer--patching-wsus-sg_sg-04080d467ff2ca6c4" {
  description = "Security group for WSUS server in Patching Compartment"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    description = "Allow all outbound traffic"
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    cidr_blocks = ["**********/27"]
    description = "Placeholder - Allow HTTP from VPC (for clients - adjust as needed)"
    from_port   = "80"
    protocol    = "tcp"
    self        = "false"
    to_port     = "80"
  }

  ingress {
    cidr_blocks = ["**********/27"]
    description = "Placeholder - Allow HTTPS from VPC (for clients - adjust as needed)"
    from_port   = "443"
    protocol    = "tcp"
    self        = "false"
    to_port     = "443"
  }

  name = "patching-wsus-sg"

  tags = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "sg-patching-wsus"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  tags_all = {
    AutoShutdown    = "true"
    Confidentiality = "internal"
    CostCenter      = "12345"
    Environment     = "dev"
    ManagedBy       = "Terraform"
    Name            = "sg-patching-wsus"
    OwnerTeam       = "DevOps"
    Project         = "infovault"
  }

  vpc_id = "vpc-0ca0480cce82946b3"
}

resource "aws_security_group" "tfer--poc-host-naga_sg-054a1946b939e0eca" {
  description = "launch-wizard-1 created 2025-06-06T23:05:30.621Z"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "22"
    protocol    = "tcp"
    self        = "false"
    to_port     = "22"
  }

  ingress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "443"
    protocol    = "tcp"
    self        = "false"
    to_port     = "443"
  }

  name   = "poc-host-naga"
  vpc_id = "vpc-037f619602fa293a1"
}

resource "aws_security_group" "tfer--sgrp-dev-cam-srvr-01_sg-029c5f3f1ddf2855e" {
  description = "sgrp-dev-cam-srvr-01"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "22"
    protocol    = "tcp"
    self        = "false"
    to_port     = "22"
  }

  name   = "sgrp-dev-cam-srvr-01"
  vpc_id = "vpc-05e0e8104b3321c40"
}

resource "aws_security_group" "tfer--sgrp-dev-smtp-srvr-01_sg-0764227cd7e054e0a" {
  description = "sgrp-dev-smtp-srvr-01"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "22"
    protocol    = "tcp"
    self        = "false"
    to_port     = "22"
  }

  name   = "sgrp-dev-smtp-srvr-01"
  vpc_id = "vpc-05e0e8104b3321c40"
}

resource "aws_security_group" "tfer--sgrp-dev-squid-pxy-iz-01_sg-023130e17836a50d3" {
  description = "sgrp-dev-squid-pxy-iz-01"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "22"
    protocol    = "tcp"
    self        = "false"
    to_port     = "22"
  }

  name   = "sgrp-dev-squid-pxy-iz-01"
  vpc_id = "vpc-05e0e8104b3321c40"
}
