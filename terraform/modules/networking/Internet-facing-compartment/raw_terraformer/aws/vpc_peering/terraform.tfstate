{"version": 3, "terraform_version": "0.12.31", "serial": 1, "lineage": "5ad6a55f-6def-a3ef-8ae9-3774ed09961f", "modules": [{"path": ["root"], "outputs": {"aws_vpc_peering_connection_tfer--pcx-00306eabadaf4a547_id": {"sensitive": false, "type": "string", "value": "pcx-00306eabadaf4a547"}, "aws_vpc_peering_connection_tfer--pcx-02dd2ea276cb24bb3_id": {"sensitive": false, "type": "string", "value": "pcx-02dd2ea276cb24bb3"}, "aws_vpc_peering_connection_tfer--pcx-0f0db258d11626f4c_id": {"sensitive": false, "type": "string", "value": "pcx-0f0db258d11626f4c"}}, "resources": {"aws_vpc_peering_connection.tfer--pcx-00306eabadaf4a547": {"type": "aws_vpc_peering_connection", "depends_on": [], "primary": {"id": "pcx-00306eabadaf4a547", "attributes": {"accept_status": "active", "accepter.#": "1", "accepter.0.allow_remote_vpc_dns_resolution": "false", "id": "pcx-00306eabadaf4a547", "peer_owner_id": "046276255144", "peer_region": "ap-southeast-1", "peer_vpc_id": "vpc-05e0e8104b3321c40", "requester.#": "1", "requester.0.allow_remote_vpc_dns_resolution": "false", "tags.%": "3", "tags.Environment": "dev", "tags.ManagedBy": "DevOps", "tags.Name": "Peering_vpc-05f1b20a6634c6a38_to_vpc-05e0e8104b3321c40", "tags_all.%": "3", "tags_all.Environment": "dev", "tags_all.ManagedBy": "DevOps", "tags_all.Name": "Peering_vpc-05f1b20a6634c6a38_to_vpc-05e0e8104b3321c40", "vpc_id": "vpc-05f1b20a6634c6a38"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_vpc_peering_connection.tfer--pcx-02dd2ea276cb24bb3": {"type": "aws_vpc_peering_connection", "depends_on": [], "primary": {"id": "pcx-02dd2ea276cb24bb3", "attributes": {"accept_status": "active", "accepter.#": "1", "accepter.0.allow_remote_vpc_dns_resolution": "false", "id": "pcx-02dd2ea276cb24bb3", "peer_owner_id": "046276255144", "peer_region": "ap-southeast-1", "peer_vpc_id": "vpc-0ca0480cce82946b3", "requester.#": "1", "requester.0.allow_remote_vpc_dns_resolution": "false", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "infovault-dev-internet-to-patching-peering", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "infovault-dev-internet-to-patching-peering", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_id": "vpc-037f619602fa293a1"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}, "aws_vpc_peering_connection.tfer--pcx-0f0db258d11626f4c": {"type": "aws_vpc_peering_connection", "depends_on": [], "primary": {"id": "pcx-0f0db258d11626f4c", "attributes": {"accept_status": "active", "accepter.#": "1", "accepter.0.allow_remote_vpc_dns_resolution": "false", "id": "pcx-0f0db258d11626f4c", "peer_owner_id": "046276255144", "peer_region": "ap-southeast-1", "peer_vpc_id": "vpc-05f1b20a6634c6a38", "requester.#": "1", "requester.0.allow_remote_vpc_dns_resolution": "false", "tags.%": "8", "tags.AutoShutdown": "true", "tags.Confidentiality": "internal", "tags.CostCenter": "12345", "tags.Environment": "dev", "tags.ManagedBy": "Terraform", "tags.Name": "infovault-dev-internet-to-management-peering", "tags.OwnerTeam": "DevOps", "tags.Project": "infovault", "tags_all.%": "8", "tags_all.AutoShutdown": "true", "tags_all.Confidentiality": "internal", "tags_all.CostCenter": "12345", "tags_all.Environment": "dev", "tags_all.ManagedBy": "Terraform", "tags_all.Name": "infovault-dev-internet-to-management-peering", "tags_all.OwnerTeam": "DevOps", "tags_all.Project": "infovault", "vpc_id": "vpc-037f619602fa293a1"}, "meta": {"schema_version": 0}, "tainted": false}, "deposed": [], "provider": "provider.aws"}}, "depends_on": []}]}