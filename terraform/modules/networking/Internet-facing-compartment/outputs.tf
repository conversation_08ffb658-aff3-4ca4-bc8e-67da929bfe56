# VPC Module Outputs
# Internet Facing Compartment VPC Outputs

output "vpc_id" {
  description = "ID of the Internet Facing VPC"
  value       = aws_vpc.main.id
}

output "vpc_arn" {
  description = "ARN of the Internet Facing VPC"
  value       = aws_vpc.main.arn
}

output "vpc_cidr_block" {
  description = "CIDR block of the Internet Facing VPC"
  value       = aws_vpc.main.cidr_block
}

output "vpc_name" {
  description = "Name of the Internet Facing VPC"
  value       = aws_vpc.main.tags["Name"]
}

# Internet Gateway Outputs
output "internet_gateway_id" {
  description = "ID of the Internet Gateway"
  value       = var.create_internet_gateway ? aws_internet_gateway.main[0].id : null
}

output "internet_gateway_arn" {
  description = "ARN of the Internet Gateway"
  value       = var.create_internet_gateway ? aws_internet_gateway.main[0].arn : null
}

# Public Subnet Outputs
output "public_subnet_ids" {
  description = "Map of public subnet names to their IDs"
  value       = { for k, v in aws_subnet.public : k => v.id }
}

output "public_subnet_arns" {
  description = "Map of public subnet names to their ARNs"
  value       = { for k, v in aws_subnet.public : k => v.arn }
}

output "public_subnet_cidr_blocks" {
  description = "Map of public subnet names to their CIDR blocks"
  value       = { for k, v in aws_subnet.public : k => v.cidr_block }
}

output "public_subnet_availability_zones" {
  description = "Map of public subnet names to their availability zones"
  value       = { for k, v in aws_subnet.public : k => v.availability_zone }
}

# Route Table Outputs
output "public_route_table_id" {
  description = "ID of the public route table"
  value       = length(aws_route_table.public) > 0 ? aws_route_table.public[0].id : null
}

output "public_route_table_arn" {
  description = "ARN of the public route table"
  value       = length(aws_route_table.public) > 0 ? aws_route_table.public[0].arn : null
}
