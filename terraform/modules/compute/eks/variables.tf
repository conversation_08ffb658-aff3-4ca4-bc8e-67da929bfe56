variable "environment" {
  description = "Environment name (dev, staging, prod)"
  type        = string
}

variable "common_tags" {
  description = "Common tags for all resources"
  type        = map(string)
  default     = {}
}

variable "region" {
  description = "AWS region"
  type        = string
}

# EKS Cluster Variables
variable "cluster_name" {
  description = "Name of the EKS cluster"
  type        = string
}

variable "cluster_version" {
  description = "Kubernetes version for the EKS cluster"
  type        = string
}

variable "cluster_role_name" {
  description = "Name of the IAM role for the EKS cluster"
  type        = string
}

variable "node_group_role_name" {
  description = "Name of the IAM role for EKS node groups"
  type        = string
}

variable "subnet_ids" {
  description = "List of subnet IDs for the EKS cluster"
  type        = list(string)
}

variable "cluster_security_group_ids" {
  description = "List of security group IDs for the EKS cluster"
  type        = list(string)
  default     = []
}

variable "endpoint_private_access" {
  description = "Enable private API server endpoint"
  type        = bool
  default     = true
}

variable "endpoint_public_access" {
  description = "Enable public API server endpoint"
  type        = bool
  default     = true
}

variable "public_access_cidrs" {
  description = "List of CIDR blocks for public access"
  type        = list(string)
  default     = ["0.0.0.0/0"]
}

variable "cluster_log_types" {
  description = "List of cluster log types to enable"
  type        = list(string)
  default     = ["api", "audit", "authenticator", "controllerManager", "scheduler"]
}

variable "kms_key_arn" {
  description = "ARN of KMS key for cluster encryption"
  type        = string
  default     = null
}

# Node Group Variables
variable "node_group_name" {
  description = "Name of the EKS node group"
  type        = string
}

variable "node_group_subnet_ids" {
  description = "Subnet IDs for the node group"
  type        = list(string)
}

variable "node_group_instance_types" {
  description = "Instance types for the node group"
  type        = list(string)
}

variable "node_group_ami_type" {
  description = "AMI type for the node group"
  type        = string
  default     = "AL2_x86_64"
}

variable "node_group_capacity_type" {
  description = "Capacity type for the node group"
  type        = string
  default     = "ON_DEMAND"
}

variable "node_group_disk_size" {
  description = "Disk size for the node group"
  type        = number
  default     = 20
}

variable "node_group_desired_size" {
  description = "Desired size for the node group"
  type        = number
}

variable "node_group_max_size" {
  description = "Maximum size for the node group"
  type        = number
}

variable "node_group_min_size" {
  description = "Minimum size for the node group"
  type        = number
}

variable "node_group_max_unavailable" {
  description = "Maximum unavailable nodes during update for the node group"
  type        = number
  default     = 1
}

variable "node_group_labels" {
  description = "Labels for the node group"
  type        = map(string)
  default     = {}
}

variable "addon_amazon_cloudwatch_observability_version" {
  description = "Version for amazon-cloudwatch-observability addon"
  type        = string
}

variable "addon_aws_ebs_csi_driver_version" {
  description = "Version for aws-ebs-csi-driver addon"
  type        = string
}

variable "addon_aws_guardduty_agent_version" {
  description = "Version for aws-guardduty-agent addon"
  type        = string
}

variable "addon_coredns_version" {
  description = "Version for coredns addon"
  type        = string
}

variable "addon_eks_pod_identity_agent_version" {
  description = "Version for eks-pod-identity-agent addon"
  type        = string
}

variable "addon_kube_proxy_version" {
  description = "Version for kube-proxy addon"
  type        = string
}

variable "addon_vpc_cni_version" {
  description = "Version for vpc-cni addon"
  type        = string
}

