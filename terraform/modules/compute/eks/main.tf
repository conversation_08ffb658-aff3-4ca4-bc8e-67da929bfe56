# EKS Cluster
resource "aws_eks_cluster" "this" {
  name     = var.cluster_name
  role_arn = aws_iam_role.cluster.arn
  version  = var.cluster_version

  vpc_config {
    subnet_ids              = var.subnet_ids
    endpoint_private_access = var.endpoint_private_access
    endpoint_public_access  = var.endpoint_public_access
    public_access_cidrs     = var.public_access_cidrs
    security_group_ids      = var.cluster_security_group_ids
  }

  enabled_cluster_log_types = var.cluster_log_types

  dynamic "encryption_config" {
    for_each = var.kms_key_arn != null ? [1] : []
    content {
      provider {
        key_arn = var.kms_key_arn
      }
      resources = ["secrets"]
    }
  }

  depends_on = [
    aws_iam_role_policy_attachment.cluster_AmazonEKSClusterPolicy,
  ]

  tags = var.common_tags
}

# EKS Cluster IAM Role
resource "aws_iam_role" "cluster" {
  name = var.cluster_role_name

  assume_role_policy = jsonencode({
    Statement = [{
      Action = "sts:AssumeRole"
      Effect = "Allow"
      Principal = {
        Service = "eks.amazonaws.com"
      }
    }]
    Version = "2012-10-17"
  })

  tags = var.common_tags
}

resource "aws_iam_role_policy_attachment" "cluster_AmazonEKSClusterPolicy" {
  policy_arn = "arn:aws:iam::aws:policy/AmazonEKSClusterPolicy"
  role       = aws_iam_role.cluster.name
}

# EKS Node Group IAM Role
resource "aws_iam_role" "node_group" {
  name = var.node_group_role_name

  assume_role_policy = jsonencode({
    Statement = [{
      Action = "sts:AssumeRole"
      Effect = "Allow"
      Principal = {
        Service = "ec2.amazonaws.com"
      }
    }]
    Version = "2012-10-17"
  })

  tags = var.common_tags
}

resource "aws_iam_role_policy_attachment" "node_group_AmazonEKSWorkerNodePolicy" {
  policy_arn = "arn:aws:iam::aws:policy/AmazonEKSWorkerNodePolicy"
  role       = aws_iam_role.node_group.name
}

resource "aws_iam_role_policy_attachment" "node_group_AmazonEKS_CNI_Policy" {
  policy_arn = "arn:aws:iam::aws:policy/AmazonEKS_CNI_Policy"
  role       = aws_iam_role.node_group.name
}

resource "aws_iam_role_policy_attachment" "node_group_AmazonEC2ContainerRegistryReadOnly" {
  policy_arn = "arn:aws:iam::aws:policy/AmazonEC2ContainerRegistryReadOnly"
  role       = aws_iam_role.node_group.name
}

# EKS Node Group
resource "aws_eks_node_group" "main" {
  cluster_name    = aws_eks_cluster.this.name
  node_group_name = var.node_group_name
  node_role_arn   = aws_iam_role.node_group.arn
  subnet_ids      = var.node_group_subnet_ids
  instance_types  = var.node_group_instance_types
  ami_type        = var.node_group_ami_type
  capacity_type   = var.node_group_capacity_type
  disk_size       = var.node_group_disk_size

  scaling_config {
    desired_size = var.node_group_desired_size
    max_size     = var.node_group_max_size
    min_size     = var.node_group_min_size
  }

  update_config {
    max_unavailable = var.node_group_max_unavailable
  }

  labels = var.node_group_labels

  depends_on = [
    aws_iam_role_policy_attachment.node_group_AmazonEKSWorkerNodePolicy,
    aws_iam_role_policy_attachment.node_group_AmazonEKS_CNI_Policy,
    aws_iam_role_policy_attachment.node_group_AmazonEC2ContainerRegistryReadOnly,
  ]

  tags = var.common_tags
}

# EKS Addon: amazon-cloudwatch-observability
resource "aws_eks_addon" "amazon_cloudwatch_observability" {
  cluster_name      = aws_eks_cluster.this.name
  addon_name        = "amazon-cloudwatch-observability"
  addon_version     = var.addon_amazon_cloudwatch_observability_version
  resolve_conflicts = "OVERWRITE"

  tags = var.common_tags
}

# EKS Addon: aws-ebs-csi-driver
resource "aws_eks_addon" "aws_ebs_csi_driver" {
  cluster_name      = aws_eks_cluster.this.name
  addon_name        = "aws-ebs-csi-driver"
  addon_version     = var.addon_aws_ebs_csi_driver_version
  resolve_conflicts = "OVERWRITE"

  tags = var.common_tags
}

# EKS Addon: aws-guardduty-agent
resource "aws_eks_addon" "aws_guardduty_agent" {
  cluster_name      = aws_eks_cluster.this.name
  addon_name        = "aws-guardduty-agent"
  addon_version     = var.addon_aws_guardduty_agent_version
  resolve_conflicts = "OVERWRITE"

  tags = var.common_tags
}

# EKS Addon: coredns
resource "aws_eks_addon" "coredns" {
  cluster_name      = aws_eks_cluster.this.name
  addon_name        = "coredns"
  addon_version     = var.addon_coredns_version
  resolve_conflicts = "OVERWRITE"

  tags = var.common_tags
}

# EKS Addon: eks-pod-identity-agent
resource "aws_eks_addon" "eks_pod_identity_agent" {
  cluster_name      = aws_eks_cluster.this.name
  addon_name        = "eks-pod-identity-agent"
  addon_version     = var.addon_eks_pod_identity_agent_version
  resolve_conflicts = "OVERWRITE"

  tags = var.common_tags
}

# EKS Addon: kube-proxy
resource "aws_eks_addon" "kube_proxy" {
  cluster_name      = aws_eks_cluster.this.name
  addon_name        = "kube-proxy"
  addon_version     = var.addon_kube_proxy_version
  resolve_conflicts = "OVERWRITE"

  tags = var.common_tags
}

# EKS Addon: vpc-cni
resource "aws_eks_addon" "vpc_cni" {
  cluster_name      = aws_eks_cluster.this.name
  addon_name        = "vpc-cni"
  addon_version     = var.addon_vpc_cni_version
  resolve_conflicts = "OVERWRITE"

  tags = var.common_tags
}

