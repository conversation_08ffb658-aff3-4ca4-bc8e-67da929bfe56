# EC2 Instance Module

This module creates an AWS EC2 instance with configurable parameters.

## Usage

```hcl
module "ec2_instance" {
  source = "./modules/compute/ec2"
  
  instance_name       = "my-instance"
  ami_id             = "ami-12345678"
  instance_type      = "t3.medium"
  security_group_ids = ["sg-12345678"]
  subnet_id          = "subnet-12345678"
  availability_zone  = "us-west-2a"
  
  common_tags = {
    Environment = "dev"
    Project     = "infovault"
  }
}
```

## Inputs

See variables.tf for all available inputs.

## Outputs

See outputs.tf for all available outputs.
