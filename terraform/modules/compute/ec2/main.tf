terraform {
  required_version = ">= 1.3.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

resource "aws_instance" "this" {
  ami                    = var.ami_id
  instance_type          = var.instance_type
  key_name               = var.key_name != "N/A" && var.key_name != "" ? var.key_name : null
  vpc_security_group_ids = var.security_group_ids
  subnet_id              = var.subnet_id
  iam_instance_profile   = var.iam_instance_profile_name

  availability_zone = var.availability_zone
  monitoring        = var.monitoring_enabled
  ebs_optimized     = var.ebs_optimized
  source_dest_check = var.source_dest_check

  private_ip = var.private_ip_address != "N/A" && var.private_ip_address != "" ? var.private_ip_address : null
  user_data  = var.user_data

  dynamic "root_block_device" {
    for_each = var.root_block_device != null ? [var.root_block_device] : []
    content {
      volume_type           = root_block_device.value.volume_type
      volume_size           = root_block_device.value.volume_size
      delete_on_termination = root_block_device.value.delete_on_termination
      encrypted             = root_block_device.value.encrypted
    }
  }

  dynamic "ebs_block_device" {
    for_each = var.ebs_block_devices
    content {
      device_name           = ebs_block_device.value.device_name
      volume_type           = ebs_block_device.value.volume_type
      volume_size           = ebs_block_device.value.volume_size
      delete_on_termination = ebs_block_device.value.delete_on_termination
      encrypted             = ebs_block_device.value.encrypted
    }
  }

  tags = merge(var.common_tags, {
    Name = var.instance_name
  })

  lifecycle {
    create_before_destroy = false
  }
}
