# Variables for S3 Module
# InfoVault S3 Bucket Configuration

variable "bucket_name" {
  description = "Name for the S3 bucket (auto-generated if empty)"
  type        = string
  default     = ""
}

variable "name_prefix" {
  description = "Prefix for resource naming"
  type        = string
}

variable "bucket_purpose" {
  description = "Purpose/description of the bucket"
  type        = string
  default     = "General purpose S3 bucket"
}

variable "force_destroy" {
  description = "Allow force destroy of S3 bucket (for dev/testing environments)"
  type        = bool
  default     = false
}

# Versioning
variable "versioning_enabled" {
  description = "Enable versioning on S3 bucket"
  type        = bool
  default     = true
}

# Encryption
variable "encryption_algorithm" {
  description = "Server-side encryption algorithm (AES256 or aws:kms)"
  type        = string
  default     = "AES256"
  validation {
    condition     = contains(["AES256", "aws:kms"], var.encryption_algorithm)
    error_message = "Encryption algorithm must be either 'AES256' or 'aws:kms'."
  }
}

variable "kms_key_id" {
  description = "KMS key ID for encryption (required if encryption_algorithm is aws:kms)"
  type        = string
  default     = null
}

variable "bucket_key_enabled" {
  description = "Enable S3 bucket key for KMS encryption"
  type        = bool
  default     = true
}

# Public Access Block
variable "block_public_acls" {
  description = "Block public ACLs"
  type        = bool
  default     = true
}

variable "block_public_policy" {
  description = "Block public bucket policies"
  type        = bool
  default     = true
}

variable "ignore_public_acls" {
  description = "Ignore public ACLs"
  type        = bool
  default     = true
}

variable "restrict_public_buckets" {
  description = "Restrict public bucket policies"
  type        = bool
  default     = true
}

# Lifecycle Management
variable "lifecycle_enabled" {
  description = "Enable lifecycle management"
  type        = bool
  default     = true
}

variable "transition_to_ia_days" {
  description = "Number of days after which to transition objects to Standard-IA"
  type        = number
  default     = 30
}

variable "transition_to_glacier_days" {
  description = "Number of days after which to transition objects to Glacier"
  type        = number
  default     = 90
}

variable "expiration_days" {
  description = "Number of days after which to expire objects (0 = disabled)"
  type        = number
  default     = 365
}

variable "multipart_upload_days" {
  description = "Number of days after which to abort incomplete multipart uploads"
  type        = number
  default     = 7
}

# Logging
variable "logging_enabled" {
  description = "Enable S3 access logging"
  type        = bool
  default     = false
}

variable "logging_target_bucket" {
  description = "Target bucket for access logs"
  type        = string
  default     = ""
}

variable "logging_target_prefix" {
  description = "Prefix for access log objects"
  type        = string
  default     = "access-logs/"
}

# Notifications
variable "notification_enabled" {
  description = "Enable S3 bucket notifications"
  type        = bool
  default     = false
}

# Common Tags
variable "common_tags" {
  description = "Common tags to apply to all resources"
  type        = map(string)
  default     = {}
}
