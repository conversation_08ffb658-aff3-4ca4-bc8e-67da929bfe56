# S3 Module - InfoVault Application Data Storage
# Creates and configures S3 bucket with security, lifecycle, and monitoring features

# Generate random suffix for bucket name if not provided
resource "random_string" "bucket_suffix" {
  count   = var.bucket_name == "" ? 1 : 0
  length  = 8
  special = false
  upper   = false
}

# Local values for bucket configuration
locals {
  bucket_name = var.bucket_name != "" ? var.bucket_name : "${var.name_prefix}-${random_string.bucket_suffix[0].result}"

  # Merge common tags with module-specific tags
  tags = merge(var.common_tags, {
    Name        = local.bucket_name
    Purpose     = var.bucket_purpose
    Module      = "s3"
    Service     = "storage"
    Versioning  = var.versioning_enabled
    Encryption  = var.encryption_algorithm
    Lifecycle   = var.lifecycle_enabled
    Environment = "dev"
  })
}

# S3 Bucket
resource "aws_s3_bucket" "this" {
  bucket        = local.bucket_name
  force_destroy = var.force_destroy

  tags = local.tags
}

# S3 Bucket Versioning
resource "aws_s3_bucket_versioning" "this" {
  bucket = aws_s3_bucket.this.id
  versioning_configuration {
    status = var.versioning_enabled ? "Enabled" : "Suspended"
  }
}

# S3 Bucket Server-side Encryption
resource "aws_s3_bucket_server_side_encryption_configuration" "this" {
  bucket = aws_s3_bucket.this.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm     = var.encryption_algorithm
      kms_master_key_id = var.encryption_algorithm == "aws:kms" ? var.kms_key_id : null
    }
    bucket_key_enabled = var.bucket_key_enabled
  }
}

# S3 Bucket Public Access Block
resource "aws_s3_bucket_public_access_block" "this" {
  bucket = aws_s3_bucket.this.id

  block_public_acls       = var.block_public_acls
  block_public_policy     = var.block_public_policy
  ignore_public_acls      = var.ignore_public_acls
  restrict_public_buckets = var.restrict_public_buckets
}

# S3 Bucket Lifecycle Configuration
resource "aws_s3_bucket_lifecycle_configuration" "this" {
  count  = var.lifecycle_enabled ? 1 : 0
  bucket = aws_s3_bucket.this.id

  rule {
    id     = "lifecycle_rule"
    status = "Enabled"

    # Apply to all objects
    filter {}

    # Transition to Standard-IA
    dynamic "transition" {
      for_each = var.transition_to_ia_days > 0 ? [1] : []
      content {
        days          = var.transition_to_ia_days
        storage_class = "STANDARD_IA"
      }
    }

    # Transition to Glacier
    dynamic "transition" {
      for_each = var.transition_to_glacier_days > 0 ? [1] : []
      content {
        days          = var.transition_to_glacier_days
        storage_class = "GLACIER"
      }
    }

    # Expiration
    dynamic "expiration" {
      for_each = var.expiration_days > 0 ? [1] : []
      content {
        days = var.expiration_days
      }
    }

    # Abort incomplete multipart uploads
    abort_incomplete_multipart_upload {
      days_after_initiation = var.multipart_upload_days
    }
  }
}

# S3 Bucket Logging
resource "aws_s3_bucket_logging" "this" {
  count  = var.logging_enabled && var.logging_target_bucket != "" ? 1 : 0
  bucket = aws_s3_bucket.this.id

  target_bucket = var.logging_target_bucket
  target_prefix = var.logging_target_prefix
}

# S3 Bucket Notification (placeholder for future use)
resource "aws_s3_bucket_notification" "this" {
  count  = var.notification_enabled ? 1 : 0
  bucket = aws_s3_bucket.this.id

  # Add specific notification configurations as needed
  # This is a placeholder for future notification requirements
}
