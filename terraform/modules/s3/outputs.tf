# Outputs for S3 Module
# InfoVault S3 Bucket Information

# Basic Bucket Information
output "bucket_name" {
  description = "Name of the S3 bucket"
  value       = aws_s3_bucket.this.bucket
}

output "bucket_arn" {
  description = "ARN of the S3 bucket"
  value       = aws_s3_bucket.this.arn
}

output "bucket_id" {
  description = "ID of the S3 bucket"
  value       = aws_s3_bucket.this.id
}

output "bucket_domain_name" {
  description = "Domain name of the S3 bucket"
  value       = aws_s3_bucket.this.bucket_domain_name
}

output "bucket_regional_domain_name" {
  description = "Regional domain name of the S3 bucket"
  value       = aws_s3_bucket.this.bucket_regional_domain_name
}

output "bucket_region" {
  description = "Region where the S3 bucket is created"
  value       = aws_s3_bucket.this.region
}

output "bucket_hosted_zone_id" {
  description = "Route 53 Hosted Zone ID for the S3 bucket"
  value       = aws_s3_bucket.this.hosted_zone_id
}

# Configuration Information
output "versioning_enabled" {
  description = "Whether versioning is enabled on the bucket"
  value       = var.versioning_enabled
}

output "encryption_algorithm" {
  description = "Server-side encryption algorithm used"
  value       = var.encryption_algorithm
}

output "lifecycle_enabled" {
  description = "Whether lifecycle management is enabled"
  value       = var.lifecycle_enabled
}

output "public_access_blocked" {
  description = "Whether public access is blocked"
  value = {
    block_public_acls       = var.block_public_acls
    block_public_policy     = var.block_public_policy
    ignore_public_acls      = var.ignore_public_acls
    restrict_public_buckets = var.restrict_public_buckets
  }
}

# Lifecycle Configuration
output "lifecycle_configuration" {
  description = "Lifecycle configuration details"
  value = var.lifecycle_enabled ? {
    transition_to_ia_days      = var.transition_to_ia_days
    transition_to_glacier_days = var.transition_to_glacier_days
    expiration_days            = var.expiration_days
    multipart_upload_days      = var.multipart_upload_days
  } : null
}

# Logging Configuration
output "logging_configuration" {
  description = "Logging configuration details"
  value = var.logging_enabled ? {
    target_bucket = var.logging_target_bucket
    target_prefix = var.logging_target_prefix
  } : null
}

# Tags
output "tags" {
  description = "Tags applied to the S3 bucket"
  value       = aws_s3_bucket.this.tags
}
