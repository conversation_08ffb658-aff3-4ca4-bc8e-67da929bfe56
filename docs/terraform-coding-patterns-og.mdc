---
description:
globs:
alwaysApply: false
---
- **Project Structure and Modularity**
  - Organize Terraform code into:
    - `terraform/modules/` for reusable, generic components
    - `terraform/stacks/` for combinations of modules forming functional units
    - `terraform/environments/` for deployment configs per environment
  - Example structure:
    ```
    terraform/
      modules/
        networking/
          vpc/
      stacks/
        networking/
          vpc/
      environments/
        dev/
    ```

- **<PERSON><PERSON><PERSON> and Stack Patterns**
  - Modules should be generic and reusable, with clear input variables and outputs
  - Stacks combine modules and add environment- or use-case-specific resources
  - Example VPC module:
    - `main.tf`, `variables.tf`, `outputs.tf`, `versions.tf`
  - Example VPC stack:
    - Uses the VPC module, adds extra resources, and exposes outputs
  - Module Implementation Rules:
    - All configurable parameters must be exposed as variables
    - Never hardcode values that might need to change across environments
    - Use variable defaults only for non-critical settings
    - Document all variables with clear descriptions
  - Stack Implementation Rules:
    - Stacks should call modules and pass variables from the environment
    - Stacks should not hardcode values that should be passed from the environment
    - Stacks may have defaults for non-critical settings, but critical settings should come from the environment

- **Environment Configuration**
  - Each environment (e.g., `dev`, `staging`, `prod`) has its own directory under `environments/`
  - Use `main.tf` to call stacks, pass variables from `variables.tf` and `terraform.tfvars`
  - Configure remote state and backend in the environment root
  - Environment Implementation Rules:
    - **All modules and stacks MUST be called from the environment's main.tf file**
    - **Every infrastructure component must have a corresponding module call in main.tf**
    - Never call modules directly from the environment; always use stacks
    - All environment-specific values must be defined in terraform.tfvars
    - The environment's main.tf should only contain module calls and minimal logic
    - Each module call should receive all its configuration from variables defined in terraform.tfvars
    - Resource creation should never happen directly in the environment's main.tf
    - Module calls in main.tf should be organized in a logical order, with dependencies clearly defined

- **Variable and Output Management**
  - Define all variables in `variables.tf` with descriptions and types
  - Use `terraform.tfvars` for environment-specific values, never hardcode values in module or stack code
  - Expose important resource attributes via `outputs.tf`
  - All configurable parameters must be exposed as variables, even if they have defaults
  - Variable defaults should only be used for non-critical settings; critical settings should be explicitly set in terraform.tfvars
  - For resources with multiple instances of the same type (e.g., EC2 instances), define instance-specific parameters in terraform.tfvars

- **Consistency and Naming**
  - Follow consistent naming conventions for resources, modules, and variables
  - Use descriptive names for modules, stacks, and outputs
  - Tag all resources using a common tagging module or provider default_tags

- **Documentation**
  - Include a README.md in each module and stack directory
  - Document input variables, outputs, usage examples, and any caveats

- **Best Practices**
  - Use version pinning for providers and modules
  - Separate state files per environment
  - Use backend locking (e.g., DynamoDB for S3 backend)
  - Validate and lint code before committing (`terraform validate`, `terraform fmt`, `tflint`)
  - Avoid hardcoding sensitive values; use variables and secret managers
  - Create validation scripts for infrastructure components in the `terraform/script` directory
  - Document infrastructure components in the `4.Progress-Tracker` directory
  - Use `t3.medium` instance type for standard workloads instead of `m5.large` for cost optimization
  - Create security groups with proper naming (avoid prefixes like "sg-" which are reserved by AWS)

- **DO**
  - Break down infrastructure into reusable modules
  - Layer modules into stacks for functional groupings
  - Use environments for deployment-specific configuration
  - Keep documentation up to date
  - Ensure every module and stack is called from the environment's main.tf
  - Organize module calls in main.tf in a logical order (networking first, then compute, etc.)
  - Use depends_on to explicitly define dependencies between modules

- **DON'T**
  - Mix environment-specific logic in generic modules
  - Hardcode values that should be variable
  - Commit state files or secrets to version control
  - Hardcode resource configurations like instance types, sizes, or counts
  - Call modules directly from the environment's main.tf (use stacks instead)
  - Define resource configurations in multiple places (define them once in terraform.tfvars)
  - Create resources directly in the environment's main.tf
  - Use variable defaults for critical settings (use terraform.tfvars instead)
  - Duplicate code across different environments (use modules and stacks)
  - Use security group names that start with "sg-" (this prefix is reserved by AWS)
  - Define the same variable in multiple files within a module or stack
  - Create infrastructure components without calling them from the environment's main.tf
  - Leave modules or stacks uncalled in the environment's main.tf

- **References**
  - [terraform.mdc](mdc:infovault-automation/infovault-automation/infovault-automation/infovault-automation/infovault-automation/infovault-automation/.cursor/rules/terraform.mdc)
  - [Terraform Docs](mdc:infovault-automation/infovault-automation/infovault-automation/infovault-automation/infovault-automation/infovault-automation/https:/www.terraform.io/docs/index.html)
  - [HashiCorp Module Structure](mdc:infovault-automation/infovault-automation/infovault-automation/infovault-automation/infovault-automation/infovault-automation/https:/developer.hashicorp.com/terraform/language/modules/develop/structure)

- **Code Quality and Automation**
  - Run `terraform fmt`, `terraform validate`, and `tflint` before committing or merging changes
  - `tflint` is required for linting; `checkov` is optional and not required by this rule
  - Automate these checks with a Makefile or CI pipeline

- **Resource Configuration and Parameter Passing**
  - All configurable resource parameters must be exposed as variables
  - Critical resource configurations (instance types, sizes, counts) must be defined in terraform.tfvars
  - Parameter passing must follow this pattern:
    1. Define the parameter in the environment's terraform.tfvars
    2. Declare the variable in the environment's variables.tf
    3. Pass the variable to the stack in the environment's main.tf
    4. Declare the variable in the stack's variables.tf
    5. Pass the variable to the module in the stack's main.tf
    6. Declare the variable in the module's variables.tf
    7. Use the variable in the module's resources
  - Example:
    ```hcl
    # environments/dev/terraform.tfvars
    squid_instance_type = "t3.medium"

    # environments/dev/variables.tf
    variable "squid_instance_type" {
      description = "Instance type for the Squid Proxy EC2 instance"
      type        = string
    }

    # environments/dev/main.tf
    module "network_egress" {
      source = "../../stacks/networking/egress"
      squid_instance_type = var.squid_instance_type
      # other parameters...
    }

    # stacks/networking/egress/variables.tf
    variable "squid_instance_type" {
      description = "Instance type for the Squid Proxy EC2 instance"
      type        = string
    }

    # stacks/networking/egress/main.tf
    module "squid_proxy" {
      source = "../../../modules/compute/squid_proxy"
      instance_type = var.squid_instance_type
      # other parameters...
    }

    # modules/compute/squid_proxy/variables.tf
    variable "instance_type" {
      description = "EC2 instance type for the Squid Proxy"
      type        = string
    }

    # modules/compute/squid_proxy/main.tf
    resource "aws_instance" "squid" {
      instance_type = var.instance_type
      # other parameters...
    }
    ```

- **Tagging Consistency Across Modules**
  - All modules must accept a `common_tags` variable of type `map(string)`
  - Pass `common_tags` from the root module to all submodules
  - Apply `tags = var.common_tags` to all resources that support tags
  - DO:
    ```hcl
    # In module variables.tf
    variable "common_tags" {
      description = "Common tags for all resources"
      type        = map(string)
      default     = {}
    }
    # In resource
    resource "aws_iam_role" "this" {
      ...
      tags = var.common_tags
    }
    # In root module
    module "iam" {
      ...
      common_tags = var.common_tags
    }
    ```
  - DON'T:
    ```hcl
    # Passing common_tags to a module that doesn't declare it
    module "iam" {
      ...
      common_tags = var.common_tags # ❌ Will cause 'unsupported argument' error
    }
    ```

- **Variable Declaration and Usage**
  - All variables declared in a module must be used in the configuration
  - DO:
    ```hcl
    variable "foo" { type = string }
    resource "aws_s3_bucket" "example" {
      bucket = var.foo
    }
    ```
  - DON'T:
    ```hcl
    variable "bar" { type = string } # ❌ Declared but not used
    ```
  - If a variable is not used, comment it out or remove it to avoid linter warnings

- **Terraform Block Requirements**
  - Always include a required_version attribute in the terraform block
  - DO:
    ```hcl
    terraform {
      required_version = ">= 1.3.0"
      required_providers { ... }
    }
    ```
  - DON'T:
    ```hcl
    terraform {
      required_providers { ... }
    } # ❌ Missing required_version
    ```

- **All module calls in main.tf must use variables for input values, not hardcoded or inline values**
  - Define all module input values as variables in the corresponding `variables.tf` file
  - Set values for these variables in the appropriate `*.tfvars` file (e.g., `dev.tfvars`, `prod.tfvars`)
  - Reference variables in module blocks using `var.<variable_name>`
  - **DO NOT** hardcode values directly in the module block in `main.tf`

- **Validation Scripts and Documentation**
  - Create validation scripts for all infrastructure components in the `terraform/script` directory
  - Organize validation scripts in subdirectories matching the component names (e.g., `terraform/script/Patching-compartment/`)
  - Make validation scripts executable (`chmod +x`)
  - Document all infrastructure components in the `4.Progress-Tracker` directory
  - Documentation should include:
    - Overview and purpose of the component
    - Architecture details (VPC, subnets, route tables, security groups, etc.)
    - Implementation details (Terraform structure, validation scripts)
    - Network connectivity details
    - Resource tags
    - Future enhancements

**DO:**
```hcl
module "network_egress" {
  source = "../../stacks/networking/egress"
  environment            = var.environment
  vpc_id                 = var.vpc_id
  public_subnet_id       = var.public_subnet_id
  firewall_subnet_id     = var.firewall_subnet_id
  squid_sg_id            = var.squid_sg_id
  internet_gateway_id    = var.internet_gateway_id
  private_route_table_id = var.private_route_table_id
  squid_instance_type    = var.squid_instance_type
  tags                   = var.common_tags
}
```

**tfvars example:**
```hcl
environment         = "dev"
vpc_id             = "vpc-123456"
public_subnet_id   = "subnet-abcde"
firewall_subnet_id = "subnet-xyz"
squid_sg_id        = "sg-12345"
internet_gateway_id = "igw-12345"
private_route_table_id = "rtb-12345"
squid_instance_type = "m5.large"
common_tags = {
  Project = "InfoVault"
  Environment = "dev"
}
```

**DON'T:**
```hcl
module "network_egress" {
  source = "../../stacks/networking/egress"
  environment            = "dev" # ❌ hardcoded
  vpc_id                 = module.network_vpc.vpc_id # ❌ direct reference
  public_subnet_id       = "subnet-abcde" # ❌ hardcoded
  firewall_subnet_id     = "subnet-xyz" # ❌ hardcoded
  squid_sg_id            = "sg-12345" # ❌ hardcoded
  internet_gateway_id    = "igw-12345" # ❌ hardcoded
  private_route_table_id = "rtb-12345" # ❌ hardcoded
  squid_instance_type    = "m5.large" # ❌ hardcoded
  tags                   = { Project = "InfoVault" } # ❌ inline map
}
```

- **Main Points in Bold**
  - All module input values in main.tf must be passed as variables (var.*)
  - All variable values must be set in tfvars files, not inline or hardcoded
  - This ensures environment-specific configuration and code reusability
  - Reference: [main.tf example](mdc:terraform/environments/dev/main.tf)
  - Update this rule if new patterns emerge or exceptions are justified
