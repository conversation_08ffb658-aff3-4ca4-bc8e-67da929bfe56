{"cluster_overview": {"name": "infovault-dev-eks-cluster-v130", "arn": "arn:aws:eks:ap-southeast-1:046276255144:cluster/infovault-dev-eks-cluster-v130", "version": "1.32", "platform_version": "eks.10", "status": "ACTIVE", "created_at": "2025-05-21T15:24:31.970000+08:00", "endpoint": "https://97047C2789102ACEAC249097F3DA8883.gr7.ap-southeast-1.eks.amazonaws.com", "region": "ap-southeast-1", "oidc_issuer": "https://oidc.eks.ap-southeast-1.amazonaws.com/id/97047C2789102ACEAC249097F3DA8883"}, "network_configuration": {"vpc": {"vpc_id": "vpc-05e0e8104b3321c40", "name": "infovault-dev-gen-facing-compartment", "primary_cidr": "*********/26", "secondary_cidr": "**********/22", "state": "available"}, "subnets": [{"subnet_id": "subnet-023cafbedbb31d13e", "name": "app-subnet-az1", "cidr_block": "**********/24", "availability_zone": "ap-southeast-1a", "type": "Private", "available_ip_count": 181, "purpose": "EKS-Nodes"}, {"subnet_id": "subnet-015e4b2acb87091d9", "name": "app-subnet-az2", "cidr_block": "**********/24", "availability_zone": "ap-southeast-1b", "type": "Private", "available_ip_count": 184, "purpose": "EKS-Nodes"}], "security_groups": {"cluster_security_group_id": "sg-0950397297ecb203f", "additional_security_groups": ["sg-0353836b1b373e27d"], "description": "EKS created security group applied to ENI that is attached to EKS Control Plane master nodes, as well as any managed workloads."}, "access_config": {"endpoint_public_access": true, "endpoint_private_access": true, "public_access_cidrs": ["0.0.0.0/0"], "authentication_mode": "API_AND_CONFIG_MAP"}, "kubernetes_network": {"service_ipv4_cidr": "**********/16", "ip_family": "ipv4", "elastic_load_balancing_enabled": false}}, "iam_configuration": {"cluster_role_arn": "arn:aws:iam::046276255144:role/infovault-dev-eks-cluster-v130-role", "node_group_role_arn": "arn:aws:iam::046276255144:role/infovault-dev-eks-cluster-v130-v130-node-group-role-2"}, "node_groups": [{"name": "v130-node-group-3", "arn": "arn:aws:eks:ap-southeast-1:046276255144:nodegroup/infovault-dev-eks-cluster-v130/v130-node-group-3/1acb792e-ed89-78db-10d6-40290074af8e", "status": "ACTIVE", "version": "1.30", "release_version": "ami-024bcbfc9447224c4", "created_at": "2025-05-21T18:22:42.619000+08:00", "modified_at": "2025-06-10T17:53:16.277000+08:00", "configuration": {"ami_type": "CUSTOM", "capacity_type": "ON_DEMAND", "launch_template": {"name": "infovault-eks-node-v130-3", "version": "1", "id": "lt-027dd80e653b0294b"}}, "scaling_config": {"desired_size": 2, "min_size": 1, "max_size": 4, "max_unavailable": 1}, "labels": {"environment": "dev", "role": "application"}, "health_status": "HEALTHY", "current_nodes": [{"name": "ip-100-67-1-179.ap-southeast-1.compute.internal", "status": "Ready", "age": "19d", "version": "v1.30.11-eks-473151a", "internal_ip": "************", "os_image": "Amazon Linux 2"}, {"name": "ip-100-67-2-110.ap-southeast-1.compute.internal", "status": "Ready", "age": "19d", "version": "v1.30.11-eks-473151a", "internal_ip": "************", "os_image": "Amazon Linux 2"}]}], "addons": {"all_active": [{"name": "amazon-cloudwatch-observability", "version": "v4.1.0-eksbuild.1", "status": "ACTIVE", "health": "No issues"}, {"name": "aws-ebs-csi-driver", "version": "v1.43.0-eksbuild.1", "status": "ACTIVE", "health": "No issues"}, {"name": "aws-guardduty-agent", "version": "v1.10.0-eksbuild.2", "status": "ACTIVE", "health": "No issues"}, {"name": "coredns", "version": "v1.11.4-eksbuild.2", "status": "ACTIVE", "health": "No issues"}, {"name": "eks-pod-identity-agent", "version": "v1.3.4-eksbuild.1", "status": "ACTIVE", "health": "No issues"}, {"name": "kube-proxy", "version": "v1.32.0-eksbuild.2", "status": "ACTIVE", "health": "No issues"}, {"name": "vpc-cni", "version": "v1.19.2-eksbuild.1", "status": "ACTIVE", "health": "No issues"}]}, "logging": {"enabled_types": ["api", "audit", "authenticator", "controllerManager", "scheduler"]}, "current_state": {"nodes": {"current_count": 2, "expected_count": 2, "status": "All nodes healthy and ready"}, "running_applications": {"dev_namespace": [{"name": "automationejb-5d9948696d-bsmzz", "status": "Running", "type": "EJB Backend"}, {"name": "automationweb-7cc6df4778-bgv2r", "status": "Running", "type": "Web Frontend"}, {"name": "bamejb-77b5f54c78-vq2ds", "status": "Running", "type": "BAM EJB Backend"}, {"name": "bamweb-69fc94cd68-xwpsg", "status": "Running", "type": "BAM Web Frontend"}, {"name": "rmsejb-77b9999d79-nhsrq", "status": "Running", "type": "RMS EJB Backend"}, {"name": "rmsweb-666f6c49d9-jq8gr", "status": "Running", "type": "RMS Web Frontend"}, {"name": "rmsservices-5c9fb854dc-qx9q9", "status": "Running", "type": "RMS Services"}, {"name": "workspaceejb-765456f49f-trtrs", "status": "Running", "type": "Workspace EJB Backend"}, {"name": "workspaceweb-b98956497-zzmkf", "status": "Running", "type": "Workspace Web Frontend"}, {"name": "omnidocstem-d658ffdc5-t9zxs", "status": "Running", "type": "Document Management"}, {"name": "omnidocswebsvc-577f8f4fb8-9pp67", "status": "Running", "type": "Document Web Services"}], "demo_namespace": [{"name": "rmsweb-6d578f485b-58mwz", "status": "Running", "type": "Demo RMS Web"}]}, "services": [{"name": "kubernetes", "namespace": "default", "type": "ClusterIP", "cluster_ip": "**********", "ports": ["443/TCP"]}, {"name": "kube-dns", "namespace": "kube-system", "type": "ClusterIP", "cluster_ip": "**********0", "ports": ["53/UDP", "53/TCP", "9153/TCP"]}, {"name": "eks-extension-metrics-api", "namespace": "kube-system", "type": "ClusterIP", "cluster_ip": "**************", "ports": ["443/TCP"]}, {"name": "amazon-cloudwatch-observability-webhook-service", "namespace": "amazon-cloudwatch", "type": "ClusterIP", "cluster_ip": "*************", "ports": ["443/TCP"]}]}, "load_balancers": {"application_load_balancers": [{"name": "k8s-dev-albingre-741d5ef5b1", "dns_name": "internal-k8s-dev-albingre-741d5ef5b1-1765425104.ap-southeast-1.elb.amazonaws.com", "scheme": "internal", "status": "active", "type": "application"}, {"name": "k8s-dev-albingre-ec7cdc5798", "dns_name": "internal-k8s-dev-albingre-ec7cdc5798-271926709.ap-southeast-1.elb.amazonaws.com", "scheme": "internal", "status": "active", "type": "application"}, {"name": "gen-alb-internet-facing", "dns_name": "gen-alb-internet-facing-1740686524.ap-southeast-1.elb.amazonaws.com", "scheme": "internet-facing", "status": "active", "type": "application"}], "network_load_balancers": [{"name": "infovault-nlb", "dns_name": "infovault-nlb-aa06b8f0bdcc828a.elb.ap-southeast-1.amazonaws.com", "scheme": "internal", "status": "active", "type": "network"}, {"name": "app-nlb-internal", "dns_name": "app-nlb-internal-e4f71be0ff87ebcc.elb.ap-southeast-1.amazonaws.com", "scheme": "internal", "status": "active", "type": "network"}]}, "ingress_controllers": [{"name": "aws-load-balancer-controller", "status": "Running", "namespace": "kube-system", "pod": "aws-load-balancer-controller-6648f577d6-7m2f9"}], "certificates": [{"manager": "cert-manager", "status": "Running", "namespace": "cert-manager"}], "health_assessment": {"overall_status": "HEALTHY", "cluster_functional": true, "applications_running": true, "minor_observations": [{"severity": "LOW", "category": "Version Mismatch", "issue": "Node group running Kubernetes v1.30 while cluster is v1.32", "impact": "Minimal - cluster remains functional", "recommendation": "Update node group to match cluster version"}, {"severity": "LOW", "category": "AMI Type", "issue": "Using custom AMI instead of EKS-optimized AMI", "impact": "None if properly maintained", "recommendation": "Ensure custom AMI is regularly updated"}]}, "terraform_configuration": {"module_path": "terraform/modules/compute/eks", "stack_path": "terraform/stacks/compute/eks", "environment_path": "terraform/environments/dev", "cluster_name_var": "infovault-dev-eks-cluster-v130", "cluster_version_var": "1.32", "subnet_ids_var": ["subnet-087a114ef79afa85f", "subnet-00a5a802525196bba"], "node_group_name_var": "v130-node-group-3", "instance_types_var": ["t3.medium"]}, "tags": {"Project": "InfoVault", "Environment": "dev", "Owner": "<PERSON><PERSON>", "AutoShutdown": "true", "ManagedBy": "Terraform", "Confidentiality": "internal", "CostCenter": "12345", "OwnerTeam": "DevOps"}}