# InfoVault Infrastructure Inventory - Table Format for Excel

## EC2 Instances Master Table

| Instance ID | Instance Name | Instance Type | vCPU | Memory (GB) | Private IP | VPC ID | VPC Name | Subnet ID | Subnet Name | Subnet CIDR | AZ | Operating System | Purpose/Role |
|-------------|---------------|---------------|------|-------------|------------|--------|----------|-----------|-------------|-------------|----|-----------------|--------------| 
| i-063edc7bb33841b7c | mssql-primary | t3.xlarge | 4 | 16 | *********** | vpc-05e0e8104b3321c40 | Gen Facing VPC | subnet-0d4f42cf30b20c42d | DB Subnet AZ1 | **********/26 | ap-southeast-1a | Windows Server 2022 Datacenter | SQL Server Primary (Always On) |
| i-060584b2a431698f9 | mssql-secondary | t3.xlarge | 4 | 16 | *********** | vpc-05e0e8104b3321c40 | Gen Facing VPC | subnet-0c775a4f57dfa0e61 | DB Subnet AZ2 | ***********/26 | ap-southeast-1b | Windows Server 2022 Datacenter | SQL Server Secondary (Always On) |
| i-0617cabf61d1e4692 | ec2-sms-01 | t3.xlarge | 4 | 16 | TBD | vpc-05e0e8104b3321c40 | Gen Facing VPC | subnet-0d4f42cf30b20c42d | DB Subnet AZ1 | **********/26 | ap-southeast-1a | Windows Server 2022 | Storage Management System |
| i-04d29bace8fdea782 | dev-management-server-avm150 | c6a.xlarge | 4 | 16 | 100.15.0.5 | vpc-05f1b20a6634c6a38 | Management VPC | subnet-07ab4b059d69e4044 | Management Subnet AZ1 | 100.15.0.0/28 | ap-southeast-1a | Amazon Linux 2 | DAM Management Server (AVM150) |
| i-0a813028ac67e68dc | dev-dra-admin-server | m5.xlarge | 4 | 16 | 100.15.0.4 | vpc-05f1b20a6634c6a38 | Management VPC | subnet-07ab4b059d69e4044 | Management Subnet AZ1 | 100.15.0.0/28 | ap-southeast-1a | Amazon Linux 2 | DRA Admin Server |
| i-01e446e0b4800dad8 | dev-dra-analytics-server | m5.xlarge | 4 | 16 | 100.15.0.10 | vpc-05f1b20a6634c6a38 | Management VPC | subnet-07ab4b059d69e4044 | Management Subnet AZ1 | 100.15.0.0/28 | ap-southeast-1a | Amazon Linux 2 | DRA Analytics Server |
| i-0e86c058cfaad7a29 | DAM Agent Gateway | - | - | - | 100.66.3.41 | vpc-007a2ea62f155deeb | App-DB Compartment | - | DB subnet | 100.66.0.0/22 | - | - | DAM Agent Gateway (Primary) |
| i-0456a3c17005e61ed | DRA Agent Gateway (Failover) | - | - | - | 100.66.3.17 | vpc-007a2ea62f155deeb | App-DB Compartment | - | DB subnet | 100.66.0.0/22 | - | - | DRA Agent Gateway (Failover) |
| i-0fd3c2460891779a9 | infovault-dev-gitlab-runner | t3.large | 2 | 8 | 100.15.0.9 | vpc-05f1b20a6634c6a38 | Management VPC | subnet-07ab4b059d69e4044 | Management Subnet AZ1 | 100.15.0.0/28 | ap-southeast-1a | Amazon Linux 2 | GitLab Server |
| - | mgmt-newgenadm-01 | t3.xlarge | 4 | 16 | 100.15.0.7 | vpc-05f1b20a6634c6a38 | Management VPC | subnet-07ab4b059d69e4044 | Management Subnet AZ1 | 100.15.0.0/28 | ap-southeast-1a | Windows Server 2022 | Windows Tooling Server |
| i-0ad0e568c515c3ff9 | ec2-redis-cache-01 | r5.xlarge | 4 | 32 | 100.67.3.29 | vpc-05e0e8104b3321c40 | Gen Facing VPC | subnet-0d4f42cf30b20c42d | DB Subnet AZ1 | **********/26 | ap-southeast-1a | Amazon Linux 2 | Redis Cache Server |
| i-0b10ba32f14d2ae28 | newgen-nfs-server | t3.large | 2 | 8 | - | vpc-05e0e8104b3321c40 | Gen Facing VPC | subnet-023cafbedbb31d13e | App Subnet AZ1 | 100.67.1.0/24 | ap-southeast-1a | RHEL 9.x | NFS Server |
| i-06f3f2b6a31516b31 | - | - | - | - | - | - | - | - | - | - | ap-southeast-1 | - | Self-Managed Active Directory |

## Software Installation Details

| Instance ID | Instance Name | Software Installed | Version/Details |
|-------------|---------------|-------------------|-----------------|
| i-063edc7bb33841b7c | mssql-primary | SQL Server 2022 Standard Edition | v16.0.4185.3, Mixed Mode, Always On Primary |
| i-060584b2a431698f9 | mssql-secondary | SQL Server 2022 Standard Edition | v16.0.4185.3, Mixed Mode, Always On Secondary |
| i-0617cabf61d1e4692 | ec2-sms-01 | Storage Management System, SSM Agent, Chocolatey utilities | Windows Server 2022 |
| i-04d29bace8fdea782 | dev-management-server-avm150 | AWS CLI, SSM Agent, Java 11 (Amazon Corretto), jq, unzip | Requires: Imperva SecureSphere ********** (BYOL) |
| i-0a813028ac67e68dc | dev-dra-admin-server | AWS CLI, SSM Agent, Java 11 (Amazon Corretto), jq, unzip | Amazon Linux 2 |
| i-01e446e0b4800dad8 | dev-dra-analytics-server | AWS CLI, SSM Agent, Java 11 (Amazon Corretto), jq, unzip | Amazon Linux 2 |
| i-0fd3c2460891779a9 | infovault-dev-gitlab-runner | Docker, GitLab CE (containerized), AWS CLI, SSM Agent | Container ID: 39a0cdd3932c99832f2c7e940b65beaec09493fffe02bff1ff59cf13e7b2fe70 |
| - | mgmt-newgenadm-01 | Administrative tools, SSM Agent, Chocolatey utilities | Windows Server 2022 |
| i-0ad0e568c515c3ff9 | ec2-redis-cache-01 | Redis 7.x, AWS CLI, SSM Agent, CloudWatch Agent | 12GB memory allocation, AOF and RDB persistence |
| i-0b10ba32f14d2ae28 | newgen-nfs-server | NFS Server, AWS CLI, SSM Agent | RHEL 9.x, Exports: /opt/nfs/config, /opt/nfs/logs |

## Network Ports and Services

| Instance ID | Instance Name | Service | Port | Protocol | Purpose |
|-------------|---------------|---------|------|----------|---------|
| i-063edc7bb33841b7c | mssql-primary | SQL Server | 1433 | TCP | Database Access |
| i-063edc7bb33841b7c | mssql-primary | Mirroring Endpoint | 5022 | TCP | Always On Replication |
| i-060584b2a431698f9 | mssql-secondary | SQL Server | 1433 | TCP | Database Access |
| i-060584b2a431698f9 | mssql-secondary | Mirroring Endpoint | 5022 | TCP | Always On Replication |
| i-0617cabf61d1e4692 | ec2-sms-01 | SMS Service | 1111 | TCP | Storage Management |
| i-0617cabf61d1e4692 | ec2-sms-01 | SMS Service | 10000 | TCP | Storage Management |
| i-0617cabf61d1e4692 | ec2-sms-01 | HTTP | 8080 | TCP | Web Interface |
| i-0617cabf61d1e4692 | ec2-sms-01 | HTTPS | 8443 | TCP | Secure Web Interface |
| i-0617cabf61d1e4692 | ec2-sms-01 | RDP | 3389 | TCP | Remote Desktop |
| i-04d29bace8fdea782 | dev-management-server-avm150 | HTTPS | 443 | TCP | Web Console |
| i-04d29bace8fdea782 | dev-management-server-avm150 | Syslog | 514 | UDP | Log Collection |
| i-04d29bace8fdea782 | dev-management-server-avm150 | Monit | 2812 | TCP | Monitoring |
| i-04d29bace8fdea782 | dev-management-server-avm150 | Management | 8081,8083,8084,8085 | TCP | Gateway Communication |
| i-0a813028ac67e68dc | dev-dra-admin-server | HTTPS | 443 | TCP | Admin Interface |
| i-01e446e0b4800dad8 | dev-dra-analytics-server | HTTPS | 443 | TCP | Analytics Interface |
| i-0e86c058cfaad7a29 | DAM Agent Gateway | Management | 8083 | TCP | Gateway Interface |
| i-0456a3c17005e61ed | DRA Agent Gateway (Failover) | Management | 8083 | TCP | Gateway Interface |
| i-0fd3c2460891779a9 | infovault-dev-gitlab-runner | HTTP | 80 | TCP | GitLab Web |
| i-0fd3c2460891779a9 | infovault-dev-gitlab-runner | HTTPS | 443 | TCP | GitLab Secure Web |
| i-0fd3c2460891779a9 | infovault-dev-gitlab-runner | SSH | 2222 | TCP | Git SSH Access |
| i-0ad0e568c515c3ff9 | ec2-redis-cache-01 | Redis | 6379 | TCP | Cache Service |
| i-0b10ba32f14d2ae28 | newgen-nfs-server | NFS | 2049 | TCP | File Sharing |
| i-0b10ba32f14d2ae28 | newgen-nfs-server | RPC | 111 | TCP | Remote Procedure Call |

## Storage Configuration

| Instance ID | Instance Name | Root Volume | Data Volume | Additional Volumes | Encryption |
|-------------|---------------|-------------|-------------|-------------------|------------|
| i-063edc7bb33841b7c | mssql-primary | - | - | - | Yes |
| i-060584b2a431698f9 | mssql-secondary | - | - | - | Yes |
| i-0617cabf61d1e4692 | ec2-sms-01 | 125GB | 200GB | GovTech: 200GB, MOF: 200GB, Judiciary: 100GB | Yes |
| i-04d29bace8fdea782 | dev-management-server-avm150 | 160GB | - | - | Yes |
| i-0a813028ac67e68dc | dev-dra-admin-server | 125GB | - | - | Yes |
| i-01e446e0b4800dad8 | dev-dra-analytics-server | 125GB | - | - | Yes |
| i-0fd3c2460891779a9 | infovault-dev-gitlab-runner | - | - | /srv/gitlab/ (persistent) | - |
| - | mgmt-newgenadm-01 | 125GB | - | - | Yes |
| i-0ad0e568c515c3ff9 | ec2-redis-cache-01 | 125GB | 200GB | /opt/redis/data | Yes |
| i-0b10ba32f14d2ae28 | newgen-nfs-server | 125GB | - | - | Yes |

## VPC and Subnet Details

| VPC ID | VPC Name | CIDR | Subnet ID | Subnet Name | Subnet CIDR | AZ | Purpose |
|--------|----------|------|-----------|-------------|-------------|----|---------| 
| vpc-05e0e8104b3321c40 | Gen Facing VPC | 10.66.0.0/26, 100.67.0.0/22 | subnet-0f1b3c3a4d7c6a444 | App Subnet AZ1 | 100.67.1.0/24 | ap-southeast-1a | EKS Worker Nodes |
| vpc-05e0e8104b3321c40 | Gen Facing VPC | 10.66.0.0/26, 100.67.0.0/22 | subnet-099ce3891f26901ef | App Subnet AZ2 | 100.67.2.0/24 | ap-southeast-1b | EKS Worker Nodes |
| vpc-05e0e8104b3321c40 | Gen Facing VPC | 10.66.0.0/26, 100.67.0.0/22 | subnet-0d4f42cf30b20c42d | DB Subnet AZ1 | **********/26 | ap-southeast-1a | Database Servers |
| vpc-05e0e8104b3321c40 | Gen Facing VPC | 10.66.0.0/26, 100.67.0.0/22 | subnet-0c775a4f57dfa0e61 | DB Subnet AZ2 | ***********/26 | ap-southeast-1b | Database Servers |
| vpc-05e0e8104b3321c40 | Gen Facing VPC | 10.66.0.0/26, 100.67.0.0/22 | subnet-023cafbedbb31d13e | App Subnet AZ1 | 100.67.1.0/24 | ap-southeast-1a | NFS Server |
| vpc-05f1b20a6634c6a38 | Management VPC | 100.15.0.0/28 | subnet-07ab4b059d69e4044 | Management Subnet AZ1 | 100.15.0.0/28 | ap-southeast-1a | Management Servers |
| vpc-05f1b20a6634c6a38 | Management VPC | 100.15.0.0/28 | subnet-0ebbc016c2fc6b3db | MS AD Subnet | TBD | ap-southeast-1a | AWS Managed Microsoft AD |
| vpc-007a2ea62f155deeb | App-DB Compartment | 100.66.0.0/22 | - | DB subnet | 100.66.0.0/22 | - | DAM/DRA Agent Gateways |

## Security Groups

| Security Group ID | Name | VPC | Purpose | Inbound Rules |
|-------------------|------|-----|---------|---------------|
| sg-0353836b1b373e27d | infovault-eks-cluster-sg | vpc-05e0e8104b3321c40 | EKS cluster communication | EKS specific rules |
| sg-0950397297ecb203f | - | vpc-05e0e8104b3321c40 | Database servers, Redis, SMS | 1433 (SQL), 6379 (Redis), 1111/10000 (SMS), 2049/111 (NFS) |
| sg-095117b1a793c2ce0 | infovault-mgmt-servers-sg | vpc-05f1b20a6634c6a38 | Management and tooling servers | Management specific rules |

## EKS Cluster Configuration

| Property | Value |
|----------|-------|
| Cluster Name | infovault-dev-eks-cluster-v130 |
| Kubernetes Version | 1.32 |
| VPC ID | vpc-05e0e8104b3321c40 |
| VPC Name | Gen Facing VPC |
| Primary CIDR | 10.66.0.0/26 |
| Secondary CIDR | 100.67.0.0/22 |
| Endpoint Access | Private and Public |
| Security Group | sg-0353836b1b373e27d |
| Node Group Name | v130-node-group-3 |
| Node Instance Type | m5.4xlarge |
| Node Volume Size | 250GB gp3 encrypted |
| Auto Scaling | Enabled with cluster autoscaler |

## EKS Addons

| Addon Name |
|------------|
| CoreDNS |
| kube-proxy |
| Amazon VPC CNI |
| Amazon EKS Pod Identity Agent |
| Amazon EBS CSI Driver |

## SQL Server Always On Configuration

| Property | Primary Node | Secondary Node |
|----------|--------------|----------------|
| Instance ID | i-063edc7bb33841b7c | i-060584b2a431698f9 |
| Hostname | EC2AMAZ-73MN22L | EC2AMAZ-6PR1DTT |
| Private IP | *********** | *********** |
| Role | Primary Replica | Secondary Replica |
| Availability Group | InfoVaultAG | InfoVaultAG |
| Database | InfoVaultDB | InfoVaultDB |
| Failover Mode | Automatic | Automatic |
| Availability Mode | Synchronous Commit | Synchronous Commit |
| Cluster Name | SQLAG-Cluster | SQLAG-Cluster |
| Cluster IP | ************ | ************ |
| Quorum Mode | Node Majority | Node Majority |

## Application Endpoints

| Type | Application | Endpoint | Port | Purpose |
|------|-------------|----------|------|---------|
| Public | RMS | https://rms.infovaults.net/rms/dist/#/web | 443 | Records Management System |
| Public | Workspace Studio | https://newgenoneworkspacestudio.infovaults.net/workspacestudio/ | 443 | Workspace Management |
| Internal | automationweb | - | 30409 | Automation Web Interface |
| Internal | bamweb | - | 31481 | BAM Web Interface |
| Internal | omnidocswebsvc | - | 31549 | OmniDocs Web Service |
| Internal | rmsservices | - | 30165, 32154, 32281 | RMS Services |
| Internal | rmsweb | - | 31502 | RMS Web Interface |
| Internal | workspaceweb | - | 32409 | Workspace Web Interface |
| Internal | automationejb | - | 30466 (RMI), 30177 (EJB) | Automation EJB Services |
| Internal | bamejb | - | 30320 (RMI), 30159 (EJB) | BAM EJB Services |
| Internal | rmsejb | - | 30571 (RMI), 32484 (EJB) | RMS EJB Services |
| Internal | workspaceejb | - | 32195 (RMI), 30858 (EJB) | Workspace EJB Services |

## Active Directory Configuration

| Type | Property | Value |
|------|----------|-------|
| AWS Managed AD | Domain | infovault.local |
| AWS Managed AD | NetBIOS | INFOVAULT |
| AWS Managed AD | Subnet | subnet-0ebbc016c2fc6b3db |
| AWS Managed AD | VPC | vpc-05f1b20a6634c6a38 |
| Self-Managed AD | Instance ID | i-06f3f2b6a31516b31 |
| Self-Managed AD | Domain | infovault.local |
| Self-Managed AD | NetBIOS | INFOVAULT |
| Self-Managed AD | SQL Service Account | sql-service |
| Self-Managed AD | Region | ap-southeast-1 |

## Load Balancers

| Type | Scheme | VPC | Subnets | Purpose |
|------|--------|-----|---------|---------|
| Application Load Balancer | Internal | vpc-05e0e8104b3321c40 | Ingress subnets | Routes traffic to EKS services |
| Network Load Balancer | Internal | vpc-05e0e8104b3321c40 | App subnets | Routes traffic from Gen-facing ALB to internal ALB |

## Access Methods

| Method | Description | Applicable To |
|--------|-------------|---------------|
| AWS Systems Manager Session Manager | Primary access method | All instances |
| RDP | Remote Desktop Protocol | Windows instances (port 3389) |
| SSH | Secure Shell via Session Manager | Linux instances |
| Web Interfaces | Application-specific consoles | Various services |

## Backup and Monitoring Strategy

| Component | Backup Method | Monitoring Method |
|-----------|---------------|-------------------|
| EBS Volumes | Automated snapshots | AWS CloudWatch |
| SQL Server | Always On Availability Groups with automatic failover | Always On dashboard and health checks |
| Redis | RDB snapshots every 15 minutes + AOF for point-in-time recovery | Built-in monitoring tools and CloudWatch integration |
| GitLab | Persistent volumes in /srv/gitlab/ | Container monitoring |
| EKS | - | Cluster autoscaler and pod monitoring |

## Contact Information

| Property | Value |
|----------|-------|
| Primary Contact | Rupesh Panwar |
| AWS Profile | infovault |
| Region | ap-southeast-1 |
| Environment | Development |
| Documentation Location | /infovault-automation/terraform/scripts/ |
| AWS Account | ************ |
| Last Updated | 2025-01-27 |

---

**Note**: This document is formatted for easy copy-paste into Excel spreadsheets. Each table can be copied as a separate worksheet or combined as needed.
