# InfoVault EKS Cluster Overview - AP-Southeast-1

## Executive Summary

This document provides a comprehensive overview of the InfoVault development EKS cluster infrastructure in ap-southeast-1, including configuration details, current status, and running applications.

## Cluster Information

### Basic Details
- **Cluster Name**: `infovault-dev-eks-cluster-v130`
- **Kubernetes Version**: `1.32`
- **Platform Version**: `eks.10`
- **Region**: `ap-southeast-1`
- **Status**: `ACTIVE` ✅
- **Created**: `2025-05-21T15:24:31.970000+08:00`

### ARNs and Identifiers
- **Cluster ARN**: `arn:aws:eks:ap-southeast-1:046276255144:cluster/infovault-dev-eks-cluster-v130`
- **Cluster Endpoint**: `https://97047C2789102ACEAC249097F3DA8883.gr7.ap-southeast-1.eks.amazonaws.com`
- **OIDC Issuer**: `https://oidc.eks.ap-southeast-1.amazonaws.com/id/97047C2789102ACEAC249097F3DA8883`

## Network Configuration

### VPC Details
- **VPC ID**: `vpc-05e0e8104b3321c40`
- **VPC Name**: `infovault-dev-gen-facing-compartment`
- **Primary CIDR**: `*********/26`
- **Secondary CIDR**: `**********/22`

### Subnets
The EKS cluster is deployed across two private subnets:

#### Subnet 1 (AZ: ap-southeast-1a)
- **Subnet ID**: `subnet-023cafbedbb31d13e`
- **Name**: `app-subnet-az1`
- **CIDR**: `**********/24`
- **Available IPs**: 181
- **Type**: Private
- **Purpose**: EKS-Nodes

#### Subnet 2 (AZ: ap-southeast-1b)
- **Subnet ID**: `subnet-015e4b2acb87091d9`
- **Name**: `app-subnet-az2`
- **CIDR**: `**********/24`
- **Available IPs**: 184
- **Type**: Private
- **Purpose**: EKS-Nodes

### Security Groups
- **Cluster Security Group ID**: `sg-0950397297ecb203f`
- **Additional Security Groups**: `["sg-0353836b1b373e27d"]`
- **Description**: EKS created security group applied to ENI that is attached to EKS Control Plane master nodes, as well as any managed workloads.

### Access Configuration
- **Endpoint Public Access**: `true`
- **Endpoint Private Access**: `true`
- **Public Access CIDRs**: `["0.0.0.0/0"]`
- **Authentication Mode**: `API_AND_CONFIG_MAP`

### Kubernetes Network Configuration
- **Service IPv4 CIDR**: `**********/16`
- **IP Family**: `ipv4`
- **Elastic Load Balancing**: `disabled`

## IAM Configuration

### Cluster Role
- **Role ARN**: `arn:aws:iam::046276255144:role/infovault-dev-eks-cluster-v130-role`

### Node Group Role
- **Role ARN**: `arn:aws:iam::046276255144:role/infovault-dev-eks-cluster-v130-v130-node-group-role-2`

## Node Groups

### Node Group: v130-node-group-3
- **Status**: `ACTIVE` ✅
- **Node Group ARN**: `arn:aws:eks:ap-southeast-1:046276255144:nodegroup/infovault-dev-eks-cluster-v130/v130-node-group-3/1acb792e-ed89-78db-10d6-40290074af8e`
- **Version**: `1.30`
- **Release Version**: `ami-024bcbfc9447224c4`
- **Created**: `2025-05-21T18:22:42.619000+08:00`
- **Modified**: `2025-06-10T17:53:16.277000+08:00`

#### Configuration
- **AMI Type**: `CUSTOM`
- **Capacity Type**: `ON_DEMAND`
- **Launch Template**: `infovault-eks-node-v130-3` (version 1)
- **Launch Template ID**: `lt-027dd80e653b0294b`

#### Scaling Configuration
- **Desired Size**: `2`
- **Min Size**: `1`
- **Max Size**: `4`
- **Max Unavailable**: `1`

#### Labels
- `environment`: `dev`
- `role`: `application`

#### Health Status ✅
- **Status**: Healthy
- **Issues**: None

#### Current Nodes
1. **Node 1**: `ip-100-67-1-179.ap-southeast-1.compute.internal`
   - **Status**: Ready
   - **Age**: 19 days
   - **Version**: v1.30.11-eks-473151a
   - **Internal IP**: ************
   - **OS**: Amazon Linux 2

2. **Node 2**: `ip-100-67-2-110.ap-southeast-1.compute.internal`
   - **Status**: Ready
   - **Age**: 19 days
   - **Version**: v1.30.11-eks-473151a
   - **Internal IP**: ************
   - **OS**: Amazon Linux 2

## EKS Addons

### All Addons Active ✅

1. **amazon-cloudwatch-observability**
   - Version: `v4.1.0-eksbuild.1`
   - Status: `ACTIVE`
   - Health: No issues

2. **aws-ebs-csi-driver**
   - Version: `v1.43.0-eksbuild.1`
   - Status: `ACTIVE`
   - Health: No issues

3. **aws-guardduty-agent**
   - Version: `v1.10.0-eksbuild.2`
   - Status: `ACTIVE`
   - Health: No issues

4. **coredns**
   - Version: `v1.11.4-eksbuild.2`
   - Status: `ACTIVE`
   - Health: No issues

5. **eks-pod-identity-agent**
   - Version: `v1.3.4-eksbuild.1`
   - Status: `ACTIVE`
   - Health: No issues

6. **kube-proxy**
   - Version: `v1.32.0-eksbuild.2`
   - Status: `ACTIVE`
   - Health: No issues

7. **vpc-cni**
   - Version: `v1.19.2-eksbuild.1`
   - Status: `ACTIVE`
   - Health: No issues

## Logging Configuration

### Enabled Log Types
- `api`
- `audit`
- `authenticator`
- `controllerManager`
- `scheduler`

## Current Cluster State

### Nodes
- **Current Node Count**: `2` ✅
- **Expected Node Count**: `2`
- **Status**: All nodes healthy and ready

### Running Applications

#### Development Environment (dev namespace)
1. **Automation Services**
   - `automationejb-5d9948696d-bsmzz` - Running (EJB backend)
   - `automationweb-7cc6df4778-bgv2r` - Running (Web frontend)

2. **BAM (Business Activity Monitoring)**
   - `bamejb-77b5f54c78-vq2ds` - Running (EJB backend)
   - `bamweb-69fc94cd68-xwpsg` - Running (Web frontend)

3. **RMS (Records Management System)**
   - `rmsejb-77b9999d79-nhsrq` - Running (EJB backend)
   - `rmsweb-666f6c49d9-jq8gr` - Running (Web frontend)
   - `rmsservices-5c9fb854dc-qx9q9` - Running (Services layer)

4. **Workspace Management**
   - `workspaceejb-765456f49f-trtrs` - Running (EJB backend)
   - `workspaceweb-b98956497-zzmkf` - Running (Web frontend)

5. **OmniDocs**
   - `omnidocstem-d658ffdc5-t9zxs` - Running (Document management)
   - `omnidocswebsvc-577f8f4fb8-9pp67` - Running (Web services)

#### Demo Environment (demo namespace)
- `rmsweb-6d578f485b-58mwz` - Running (Demo RMS web application)

#### System Services

##### cert-manager namespace
- `cert-manager-7c48cc479c-qfksc` - Running (Certificate management)
- `cert-manager-cainjector-7d7fd75896-kzmsm` - Running (CA injector)
- `cert-manager-webhook-56bbd9dcbd-qb8zz` - Running (Webhook)

##### amazon-cloudwatch namespace
- `amazon-cloudwatch-observability-controller-manager` - Running
- `cloudwatch-agent` (DaemonSet) - Running on all nodes
- `fluent-bit` (DaemonSet) - Running on all nodes

##### amazon-guardduty namespace
- `aws-guardduty-agent` (DaemonSet) - Running on all nodes

##### kube-system namespace
- `aws-load-balancer-controller-6648f577d6-7m2f9` - Running
- `coredns` (2 replicas) - Running
- `ebs-csi-controller` (2 replicas) - Running
- `ebs-csi-node` (DaemonSet) - Running on all nodes
- `eks-pod-identity-agent` (DaemonSet) - Running on all nodes
- `kube-proxy` (DaemonSet) - Running on all nodes
- `aws-node` (VPC CNI DaemonSet) - Running on all nodes

## Load Balancers and Ingress

### Application Load Balancers (ALB) ✅

#### 1. Main Application Ingress ALB
- **Name**: `k8s-dev-albingre-741d5ef5b1`
- **DNS**: `internal-k8s-dev-albingre-741d5ef5b1-1765425104.ap-southeast-1.elb.amazonaws.com`
- **Type**: Application Load Balancer
- **Scheme**: Internal
- **Status**: Active
- **Hosts Served**:
  - `newgenoneautomationstudio.infovaults.net`
  - `newgenoneworkspacestudio.infovaults.net`
  - `newgenonebam.infovaults.net`
  - Additional hosts configured

#### 2. RMS Services ALB
- **Name**: `k8s-dev-albingre-ec7cdc5798`
- **DNS**: `internal-k8s-dev-albingre-ec7cdc5798-271926709.ap-southeast-1.elb.amazonaws.com`
- **Type**: Application Load Balancer
- **Scheme**: Internal
- **Status**: Active
- **Host**: `apachemanifold.infovaults.net`

#### 3. Internet-Facing ALB
- **Name**: `gen-alb-internet-facing`
- **DNS**: `gen-alb-internet-facing-1740686524.ap-southeast-1.elb.amazonaws.com`
- **Type**: Application Load Balancer
- **Scheme**: Internet-facing
- **Status**: Active

### Network Load Balancers (NLB) ✅

#### 1. InfoVault NLB
- **Name**: `infovault-nlb`
- **DNS**: `infovault-nlb-aa06b8f0bdcc828a.elb.ap-southeast-1.amazonaws.com`
- **Type**: Network Load Balancer
- **Scheme**: Internal
- **Status**: Active

#### 2. Application NLB Internal
- **Name**: `app-nlb-internal`
- **DNS**: `app-nlb-internal-e4f71be0ff87ebcc.elb.ap-southeast-1.amazonaws.com`
- **Type**: Network Load Balancer
- **Scheme**: Internal
- **Status**: Active

### Ingress Controllers ✅

#### AWS Load Balancer Controller
- **Status**: Running
- **Pod**: `aws-load-balancer-controller-6648f577d6-7m2f9`
- **Namespace**: `kube-system`
- **Version**: Latest
- **Function**: Manages ALB/NLB creation for Kubernetes ingress resources

### Ingress Resources

#### 1. Main Application Ingress
- **Name**: `alb-ingress`
- **Namespace**: `dev`
- **Class**: Default
- **Hosts**: Multiple InfoVault application domains
- **Backend Services**: Automation, Workspace, BAM applications

#### 2. RMS Services Ingress
- **Name**: `alb-ingress-rmsservices`
- **Namespace**: `dev`
- **Class**: Default
- **Host**: `apachemanifold.infovaults.net`
- **Backend Service**: RMS Services

## Cluster Health Assessment ✅

### Overall Status: HEALTHY
The InfoVault EKS cluster in ap-southeast-1 is fully operational and serving production workloads successfully.

### Key Strengths
1. **Node Health**: All 2 nodes are healthy and ready
2. **Addon Status**: All 7 EKS addons are active and healthy
3. **Application Availability**: Multiple business applications running successfully
4. **Load Balancing**: Comprehensive ALB/NLB setup with proper ingress configuration
5. **Monitoring**: CloudWatch observability and GuardDuty security monitoring active
6. **Certificate Management**: cert-manager deployed for TLS certificate automation

### Minor Observations
1. **Node Version Mismatch**: Nodes running Kubernetes v1.30 while cluster is v1.32
   - **Impact**: Low - cluster remains functional
   - **Recommendation**: Update node group to match cluster version

2. **Custom AMI Usage**: Node group uses custom AMI instead of standard EKS-optimized AMI
   - **Impact**: None if properly maintained
   - **Recommendation**: Ensure custom AMI is regularly updated with security patches

## Recommendations

### Short-term Optimizations
1. **Node Group Version Alignment**
   - Update node group to Kubernetes v1.32 to match cluster version
   - Plan maintenance window for rolling update

2. **Security Review**
   - Review and potentially restrict public access CIDRs from `0.0.0.0/0`
   - Implement pod security standards if not already in place

3. **Backup Strategy**
   - Implement regular ETCD backups
   - Document disaster recovery procedures

### Long-term Improvements
1. **High Availability**
   - Consider adding nodes in additional availability zones
   - Implement multi-AZ deployment for critical applications

2. **Cost Optimization**
   - Review instance types and consider spot instances for non-critical workloads
   - Implement cluster autoscaler for dynamic scaling

3. **Enhanced Monitoring**
   - Set up custom CloudWatch dashboards for application metrics
   - Implement alerting for application-specific SLAs

4. **GitOps Implementation**
   - Consider implementing ArgoCD or Flux for application deployment
   - Establish CI/CD pipelines for automated deployments

## Terraform Configuration

### Module Structure
- **EKS Module**: `terraform/modules/compute/eks/main.tf`
- **EKS Stack**: `terraform/stacks/compute/eks/main.tf`
- **Environment Config**: `terraform/environments/dev/main.tf`
- **Variables**: `terraform/environments/dev/terraform.tfvars`

### Key Configuration Values (Actual Deployment)
```hcl
cluster_name         = "infovault-dev-eks-cluster-v130"
cluster_version      = "1.32"
region              = "ap-southeast-1"
vpc_id              = "vpc-05e0e8104b3321c40"
subnet_ids          = ["subnet-023cafbedbb31d13e", "subnet-015e4b2acb87091d9"]
node_group_name     = "v130-node-group-3"
node_version        = "1.30"
desired_size        = 2
min_size           = 1
max_size           = 4
```

### Deployed Addon Versions
- `amazon-cloudwatch-observability`: `v4.1.0-eksbuild.1`
- `aws-ebs-csi-driver`: `v1.43.0-eksbuild.1`
- `aws-guardduty-agent`: `v1.10.0-eksbuild.2`
- `coredns`: `v1.11.4-eksbuild.2`
- `eks-pod-identity-agent`: `v1.3.4-eksbuild.1`
- `kube-proxy`: `v1.32.0-eksbuild.2`
- `vpc-cni`: `v1.19.2-eksbuild.1`

## Tags Applied
All resources are tagged with:
- Project: InfoVault / infovault
- Environment: dev
- Owner: Temus
- AutoShutdown: true
- ManagedBy: Terraform
- Confidentiality: internal
- CostCenter: 12345
- OwnerTeam: DevOps

## Application Services Summary

### Business Applications (11 services)
- **Automation Studio**: Web + EJB services
- **BAM (Business Activity Monitoring)**: Web + EJB services
- **RMS (Records Management)**: Web + EJB + Services layer
- **Workspace Studio**: Web + EJB services
- **OmniDocs**: Document management + Web services
- **Demo RMS**: Demonstration environment

### Infrastructure Services (15+ services)
- **AWS Load Balancer Controller**: ALB/NLB management
- **cert-manager**: TLS certificate automation
- **CloudWatch Agent**: Monitoring and logging
- **Fluent Bit**: Log forwarding
- **GuardDuty Agent**: Security monitoring
- **Core Kubernetes**: DNS, CNI, CSI, Pod Identity

---

*Document generated on: 2025-06-10*
*Last updated: 2025-06-10*
*Status: ✅ Cluster is healthy and fully operational*
*Region: ap-southeast-1*
