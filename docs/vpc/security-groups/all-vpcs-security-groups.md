# Complete VPC and Security Groups Documentation

**Region:** `ap-southeast-1`  
**Account:** `************`  
**Generated:** June 9, 2025

## Executive Summary

This document provides a comprehensive overview of all VPCs and their associated security groups in the InfoVault AWS environment. The infrastructure consists of **6 VPCs** with **80+ security groups** across different functional compartments.

### VPC Overview

| VPC ID | Name | CIDR Block | Type | Security Groups |
|---|---|---|---|---|
| `vpc-05f1b20a6634c6a38` | infovalut-dev-Intranet-management-compartment | **********/26 | Management | 8 |
| `vpc-0e75036a2ad9e5b4f` | infovault-dev-ablrhf-compartment | *********/27 | ABLRHF | 10 |
| `vpc-05e0e8104b3321c40` | infovault-dev-gen-facing-compartment | *********/26, **********/22 | General/App | 25+ |
| `vpc-037f619602fa293a1` | infovault-dev-Internet-facing-compartment | **********/27 | Internet-Facing | 9 |
| `vpc-02282d8d4c9c7764c` | aws-controltower-VPC | **********/16 | Control Tower | 1 |
| `vpc-0ca0480cce82946b3` | infovault-dev-Patching-Compartment | **********/27 | Patching | 7 |

---

## 1. Management VPC (`vpc-05f1b20a6634c6a38`)

**Name:** `infovalut-dev-Intranet-management-compartment`  
**CIDR:** `**********/26`  
**Purpose:** Management and administrative functions

### Key Security Groups:

#### Management Servers Security Group
- **ID:** `sg-095117b1a793c2ce0`
- **Name:** `infovault-mgmt-servers-sg`
- **Purpose:** Management servers access control
- **Key Rules:**
  - SSH from App-DB VPC (100.66.0.0/22)
  - RDP from App-DB VPC (100.66.0.0/22)
  - WinRM (5985-5986)

#### GitLab Runner Security Group
- **ID:** `sg-00df94fe66c7464f5`
- **Name:** `infovault-mgmt-gitlab-sg`
- **Key Rules:**
  - HTTP/HTTPS restricted to management subnets
  - SSH from management subnet only
  - Proxy access to internet-facing VPC

#### VPC Endpoints Security Group
- **ID:** `sg-0d28b5ada1bb60c9a`
- **Name:** `infovault-mgmt-vpce-sg`
- **Key Rules:**
  - HTTPS (443) from management and AD client subnets

---

## 2. ABLRHF Compartment VPC (`vpc-0e75036a2ad9e5b4f`)

**Name:** `infovault-dev-ablrhf-compartment`  
**CIDR:** `*********/27`  
**Purpose:** ABLRHF specific workloads

### Key Security Groups:

#### ABLRHF General Security Group
- **ID:** `sg-0a6d2925236dcbd18`
- **Name:** `ablrhf-general`
- **Key Rules:**
  - All internal VPC traffic (*********/27)

#### ABLRHF VPC Endpoints
- **ID:** `sg-082d3fc2b26b1004e`
- **Name:** `ablrhf-vpce`
- **Key Rules:**
  - HTTPS from VPC CIDR (*********/27)

---

## 3. General/App-Facing VPC (`vpc-05e0e8104b3321c40`)

**Name:** `infovault-dev-gen-facing-compartment`  
**CIDR:** `*********/26`, `**********/22`  
**Purpose:** Main application workloads and EKS cluster

### Key Security Groups:

#### EKS Cluster Security Group
- **ID:** `sg-0353836b1b373e27d`
- **Name:** `infovault-eks-cluster-sg`
- **Key Rules:**
  - Complex EKS-specific rules
  - NodePort ranges (30000-33000)
  - Database access (1433, 6379, 10000)
  - ⚠️ SSH open to internet (0.0.0.0/0)

#### Application Load Balancer Groups
- **ID:** `sg-0e921258047172c67`
- **Name:** `infovault-dev-alb-sg`
- **Key Rules:**
  - HTTP/HTTPS from anywhere (0.0.0.0/0)
  - Management VPC access

#### Application Tier Security Group
- **ID:** `sg-095c31fa3b94c41ae`
- **Name:** `infovault-app-tier-sg`
- **Key Rules:**
  - SSH from specific office IPs
  - NFS access (2049)
  - RPC Portmapper (111)
  - ⚠️ HTTPS open to internet

#### Database Security Groups
- **ID:** `sg-0d8a5fa845dca93c9`
- **Name:** `Database-SQL-infovault-gen-facing-compartment`
- **Key Rules:**
  - SQL Server (1433) from EKS and management
  - Redis (6379), Custom ports (10000, 1111)
  - Cross-VPC peering connections

---

## 4. Internet-Facing VPC (`vpc-037f619602fa293a1`)

**Name:** `infovault-dev-Internet-facing-compartment`  
**CIDR:** `**********/27`  
**Purpose:** Public-facing services and load balancers

### Key Security Groups:

#### SharePoint 2016 Security Group
- **ID:** `sg-08b7a467b37acf0e7`
- **Name:** `InfoVault-SPS2016-SG`
- **⚠️ CRITICAL SECURITY ISSUES:**
  - RDP (3389) open to internet (0.0.0.0/0)
  - HTTP/HTTPS open to internet
  - SQL Server (1433) from internal networks

#### ALB Security Groups
- **ID:** `sg-0c2c1a0a80463a69b`
- **Name:** `infovault-dev-alb-sg`
- **Key Rules:**
  - HTTP/HTTPS from anywhere
  - Standard ALB configuration

#### Network Firewall Endpoints
- **ID:** `sg-0e2bbdad5eddfe99c`
- **Name:** `infovault-dev-anfw-endpoint-sg`
- **Key Rules:**
  - All traffic from firewall subnet (**********/27)

#### POC Host
- **ID:** `sg-054a1946b939e0eca`
- **Name:** `poc-host-naga`
- **⚠️ CRITICAL SECURITY ISSUE:**
  - SSH (22) open to internet (0.0.0.0/0)

---

## 5. Control Tower VPC (`vpc-02282d8d4c9c7764c`)

**Name:** `aws-controltower-VPC`  
**CIDR:** `**********/16`  
**Purpose:** AWS Control Tower management
**Managed By:** AWS Control Tower (CloudFormation)

### Security Groups:
- Only default VPC security group
- Managed by AWS Control Tower

---

## 6. Patching Compartment VPC (`vpc-0ca0480cce82946b3`)

**Name:** `infovault-dev-Patching-Compartment`  
**CIDR:** `**********/27`  
**Purpose:** Patch management and updates

### Key Security Groups:

#### WSUS Server Security Group
- **ID:** `sg-04080d467ff2ca6c4`
- **Name:** `patching-wsus-sg`
- **Key Rules:**
  - HTTP/HTTPS from VPC (**********/27)

#### RHEL Repository Security Group
- **ID:** `sg-01f0c371bd11aa6f1`
- **Name:** `patching-rhel-repo-sg`
- **Key Rules:**
  - HTTP/HTTPS from VPC (**********/27)

#### VPC Endpoints Security Group
- **ID:** `sg-0870c466d0db940c4`
- **Name:** `patching-vpce-sg`
- **Key Rules:**
  - HTTPS from VPC for VPC endpoints

---

## Security Analysis by Category

### 🔥 Critical Security Issues

1. **Public SSH Access**
   - `poc-host-naga` (sg-054a1946b939e0eca): SSH from 0.0.0.0/0
   - `infovault-eks-cluster-sg` (sg-0353836b1b373e27d): SSH from 0.0.0.0/0
   - Multiple application servers with SSH from 0.0.0.0/0

2. **Public RDP Access**
   - `InfoVault-SPS2016-SG` (sg-08b7a467b37acf0e7): RDP from 0.0.0.0/0

3. **Incomplete Database Security**
   - Multiple RDS security groups with no inbound rules
   - Database access not properly restricted

### ⚠️ Security Concerns

1. **Overly Permissive Outbound Rules**
   - Most security groups allow all outbound traffic
   - Should implement least privilege outbound rules

2. **Wide Network Access**
   - Some rules use large CIDR blocks (0.0.0.0/0, 10.0.0.0/8)
   - Could be more restrictive

3. **Missing Security Group Descriptions**
   - Some security groups lack proper documentation

### ✅ Good Security Practices

1. **Terraform Management**
   - Most resources managed by Infrastructure as Code
   - Consistent tagging strategy

2. **Network Segmentation**
   - Proper VPC separation by function
   - Squid proxy for controlled internet access

3. **VPC Peering Controls**
   - Cross-VPC access properly configured
   - Security group references across VPCs

4. **AWS Managed Services**
   - GuardDuty security groups properly configured
   - Control Tower integration

---

## Network Architecture Summary

### CIDR Block Allocation
- **Management VPC**: **********/26 (64 IPs)
- **ABLRHF VPC**: *********/27 (32 IPs)
- **Gen-Facing VPC**: *********/26 + **********/22 (64 + 1024 IPs)
- **Internet-Facing VPC**: **********/27 (32 IPs)
- **Patching VPC**: **********/27 (32 IPs)
- **Control Tower VPC**: **********/16 (65536 IPs)

### Common Service Ports
- **HTTP/HTTPS**: 80, 443
- **SSH**: 22
- **RDP**: 3389
- **SQL Server**: 1433
- **Redis**: 6379
- **Squid Proxy**: 3128
- **WinRM**: 5985-5986
- **EKS NodePorts**: 30000-33000
- **Custom Applications**: 8080-8085, 10000, 1111

### Cross-VPC Connectivity
- **VPC Peering**: Management ↔ Gen-Facing (pcx-00306eabadaf4a547)
- **Network Firewall**: Centralized in Internet-Facing VPC
- **Squid Proxy**: Centralized internet access control

---

## Immediate Action Items

### 🚨 High Priority (Fix Immediately)
1. **Remove public SSH access** from:
   - poc-host-naga (sg-054a1946b939e0eca)
   - infovault-eks-cluster-sg (sg-0353836b1b373e27d)

2. **Remove public RDP access** from:
   - InfoVault-SPS2016-SG (sg-08b7a467b37acf0e7)

3. **Configure RDS security groups** with proper inbound rules

### ⚠️ Medium Priority (Fix Within 30 Days)
1. **Implement least privilege outbound rules**
2. **Review and restrict wide CIDR blocks**
3. **Add proper security group descriptions**
4. **Implement bastion host or Systems Manager Session Manager**

### ℹ️ Low Priority (Ongoing Improvements)
1. **Regular security group audits**
2. **Automated compliance checking**
3. **Security group usage monitoring**
4. **Documentation updates**

---

## Compliance and Governance

### Tagging Strategy
- **Environment**: dev
- **Project**: infovault
- **ManagedBy**: Terraform
- **OwnerTeam**: DevOps
- **CostCenter**: 12345
- **Confidentiality**: internal
- **AutoShutdown**: true

### AWS Managed Security Groups
- **GuardDuty**: 4 managed security groups across VPCs
- **EKS**: 1 cluster security group
- **AWS Managed AD**: 1 directory controllers security group

---

*This documentation was automatically generated from AWS security group and VPC configurations. Last updated: June 9, 2025*

