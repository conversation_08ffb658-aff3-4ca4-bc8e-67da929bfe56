# IAM Roles and Policies Analysis - Naga EKS Cluster

This document provides a comprehensive overview of all IAM roles, policies, and related resources defined in the naga-eks-cluster Terraform codebase.

## Overview

The EKS cluster infrastructure defines multiple IAM roles and policies to support:
- EKS cluster operations
- Worker node management
- Container networking (VPC CNI)
- Storage management (EBS CSI, EFS CSI)
- Service account authentication (OIDC)

---

## IAM Roles

### 1. EKS Cluster Service Role
**Resource Name:** `bc-eks-cluster`  
**File:** `modules/eks/main.tf` (lines 51-66)  
**Purpose:** Allows EKS service to manage cluster resources

```terraform
resource "aws_iam_role" "bc-eks-cluster" {
  name = "${var.cluster_name}-eks-cluster-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action    = "sts:AssumeRole"
        Effect    = "Allow"
        Principal = {
          Service = "eks.amazonaws.com"
        }
      },
    ]
  })
}
```

**Attached Policies:**
- `arn:aws:iam::aws:policy/AmazonEKSClusterPolicy`
- `arn:aws:iam::aws:policy/AmazonEKSServicePolicy`

### 2. EKS Node Group Role
**Resource Name:** `bc-eks-cluster-node-role`  
**File:** `modules/eks/main.tf` (lines 103-118)  
**Purpose:** IAM role for EC2 instances in the node group

```terraform
resource "aws_iam_role" "bc-eks-cluster-node-role" {
  name = "${var.cluster_name}-node-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "ec2.amazonaws.com"
        }
      },
    ]
  })
}
```

### 3. EKS Worker Node Role
**Resource Name:** `eks_worker`  
**File:** `modules/eks/main.tf` (lines 158-173)  
**Purpose:** IAM role for EKS worker nodes

```terraform
resource "aws_iam_role" "eks_worker" {
  name = "${var.cluster_name}-worker-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action    = "sts:AssumeRole"
        Effect    = "Allow"
        Principal = {
          Service = "ec2.amazonaws.com"
        }
      },
    ]
  })
}
```

**Attached Policies:**
- `arn:aws:iam::aws:policy/AmazonEKSWorkerNodePolicy`
- `arn:aws:iam::aws:policy/AmazonEKS_CNI_Policy`
- `arn:aws:iam::aws:policy/AmazonEC2ContainerRegistryReadOnly`

### 4. VPC CNI Service Account Role
**Resource Name:** `eks_vpc_cni_role`  
**File:** `eks.tf` (lines 30-33)  
**Purpose:** IRSA role for VPC CNI addon

```terraform
resource "aws_iam_role" "eks_vpc_cni_role" {
  name = "${module.eks.eks_cluster}-vpc-cni-role"
  assume_role_policy = data.aws_iam_policy_document.eks_vpc_cni_assume_role_policy.json
}
```

**Trust Policy:** Allows `system:serviceaccount:kube-system:aws-node` to assume role via OIDC

### 5. EBS CSI Service Account Role
**Resource Name:** `eks_vpc_ebs_csi_role`  
**File:** `eks.tf` (lines 130-133)  
**Purpose:** IRSA role for EBS CSI driver

```terraform
resource "aws_iam_role" "eks_vpc_ebs_csi_role" {
  name = "${module.eks.eks_cluster}-ebs-csi-role"
  assume_role_policy = data.aws_iam_policy_document.eks_vpc_ebs_csi_policy.json
}
```

**Trust Policy:** Allows `system:serviceaccount:kube-system:ebs-csi-controller-sa` to assume role via OIDC

### 6. EFS CSI Service Account Role
**Resource Name:** `eks_vpc_efs_csi_role`  
**File:** `eks.tf` (lines 319-322)  
**Purpose:** IRSA role for EFS CSI driver

```terraform
resource "aws_iam_role" "eks_vpc_efs_csi_role" {
  name = "${module.eks.eks_cluster}-efs-csi-role"
  assume_role_policy = data.aws_iam_policy_document.eks_vpc_efs_csi_policy.json
}
```

**Trust Policy:** Allows `system:serviceaccount:kube-system:efs-csi-*` to assume role via OIDC

---

## IAM Policies

### 1. VPC CNI Custom Policy
**Resource Name:** `eks_vpc_cni_policy`  
**File:** `eks.tf` (lines 61-98)  
**Policy Name:** `${module.eks.eks_cluster}-vpc-cni-policy`

**Permissions:**
- EC2 network interface management
- Private IP address assignment/unassignment
- Network interface attachment/detachment
- Instance and subnet description
- ENI tagging

**Key Actions:**
```json
[
  "ec2:AssignPrivateIpAddresses",
  "ec2:AttachNetworkInterface",
  "ec2:CreateNetworkInterface",
  "ec2:DeleteNetworkInterface",
  "ec2:DescribeInstances",
  "ec2:DescribeTags",
  "ec2:DescribeNetworkInterfaces",
  "ec2:DescribeInstanceTypes",
  "ec2:DescribeSubnets",
  "ec2:DetachNetworkInterface",
  "ec2:ModifyNetworkInterfaceAttribute",
  "ec2:UnassignPrivateIpAddresses",
  "ec2:CreateTags"
]
```

### 2. EBS CSI Custom Policy
**Resource Name:** `eks_vpc_ebs_csi_policy`  
**File:** `eks.tf` (lines 159-296)  
**Policy Name:** `${module.eks.eks_cluster}-ebs_csi-policy`

**Permissions:**
- EBS volume lifecycle management
- Snapshot operations
- Volume tagging and deletion
- Conditional access based on cluster tags

**Key Actions:**
```json
[
  "ec2:CreateSnapshot",
  "ec2:AttachVolume",
  "ec2:DetachVolume",
  "ec2:ModifyVolume",
  "ec2:DescribeAvailabilityZones",
  "ec2:DescribeInstances",
  "ec2:DescribeSnapshots",
  "ec2:DescribeTags",
  "ec2:DescribeVolumes",
  "ec2:DescribeVolumesModifications",
  "ec2:CreateVolume",
  "ec2:DeleteVolume",
  "ec2:DeleteSnapshot",
  "ec2:CreateTags",
  "ec2:DeleteTags"
]
```

### 3. EFS CSI Custom Policy
**Resource Name:** `eks_vpc_efs_csi_policy`  
**File:** `eks.tf` (lines 348-414)  
**Policy Name:** `${module.eks.eks_cluster}-efs_csi-policy`

**Permissions:**
- EFS access point management
- File system description
- Access point tagging and deletion

**Key Actions:**
```json
[
  "elasticfilesystem:DescribeAccessPoints",
  "elasticfilesystem:DescribeFileSystems",
  "elasticfilesystem:DescribeMountTargets",
  "ec2:DescribeAvailabilityZones",
  "elasticfilesystem:CreateAccessPoint",
  "elasticfilesystem:TagResource",
  "elasticfilesystem:DeleteAccessPoint"
]
```

---

## IAM Instance Profile

### EKS Node Instance Profile
**Resource Name:** `bc-eks-cluster-instance-profile`  
**File:** `modules/eks/main.tf` (lines 120-123)

```terraform
resource "aws_iam_instance_profile" "bc-eks-cluster-instance-profile" {
  name = "${var.cluster_name}-instance-profile"
  role = aws_iam_role.bc-eks-cluster-node-role.name
}
```

---

## OIDC Identity Provider

### EKS OIDC Provider
**Resource Name:** `eks_openod_connect_provider`  
**File:** `modules/eks/main.tf` (lines 193-197)

```terraform
resource "aws_iam_openid_connect_provider" "eks_openod_connect_provider" {
  client_id_list  = ["sts.amazonaws.com"]
  thumbprint_list = [data.tls_certificate.eks_tls_certificate.certificates[0].sha1_fingerprint]
  url             = aws_eks_cluster.bc-eks-cluster.identity[0].oidc[0].issuer
}
```

**Purpose:** Enables IAM Roles for Service Accounts (IRSA) functionality

---

## EKS Access Management

### Access Entry
**Resource Name:** `example`  
**File:** `modules/eks/main.tf` (lines 201-206)

```terraform
resource "aws_eks_access_entry" "example" {
  cluster_name      = aws_eks_cluster.bc-eks-cluster.name
  principal_arn     = "arn:aws:iam::************:user/<EMAIL>"
  type              = "STANDARD"
}
```

### Policy Association
**Resource Name:** `access-policy`  
**File:** `modules/eks/main.tf` (lines 208-216)

```terraform
resource "aws_eks_access_policy_association" "access-policy" {
  cluster_name  = aws_eks_cluster.bc-eks-cluster.name
  policy_arn    = "arn:aws:eks::aws:cluster-access-policy/AmazonEKSClusterAdminPolicy"
  principal_arn = "arn:aws:iam::************:user/<EMAIL>"

  access_scope {
    type       = "cluster"
  }
}
```

**Grants:** Cluster admin access to user `<EMAIL>`

---

## EKS Addons Configuration

The following EKS addons are configured with their respective service account roles:

1. **CoreDNS** - No custom IAM role required
2. **Kube-proxy** - No custom IAM role required  
3. **VPC CNI** - Uses `eks_vpc_cni_role`
4. **EBS CSI Driver** - Uses `eks_vpc_ebs_csi_role`
5. **EFS CSI Driver** - Uses `eks_vpc_efs_csi_role`

---

## Summary

**Total IAM Resources:**
- **6 IAM Roles** (3 for nodes, 3 for service accounts)
- **3 Custom IAM Policies** (VPC CNI, EBS CSI, EFS CSI)
- **6 Policy Attachments** (3 AWS managed + 3 custom)
- **1 Instance Profile**
- **1 OIDC Provider**
- **1 EKS Access Entry**
- **1 EKS Policy Association**

**AWS Account ID:** `************`  
**Cluster Name Variable:** `${var.cluster_name}` (default: "poc-ekscluster")

This IAM configuration follows AWS best practices for EKS security, using least-privilege access and service-specific roles for different components.
