#!/bin/bash

# Persistent GitLab Tunnel with Auto-Restart
# This script maintains a persistent connection to GitLab with automatic restart on failure

set -e

INSTANCE_ID="i-0fd3c2460891779a9"
AWS_REGION="ap-southeast-1"
AWS_PROFILE="infovault"
LOCAL_PORT="8080"
REMOTE_PORT="80"
RESTART_DELAY=5

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

info() {
    echo -e "${BLUE}[INFO] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
}

# Check prerequisites
check_prerequisites() {
    # Add user bin directory to PATH
    export PATH="$HOME/.local/bin:/usr/local/bin:/opt/homebrew/bin:$PATH"

    log "Checking prerequisites..."

    # Check AWS CLI
    if ! command -v aws &> /dev/null; then
        error "AWS CLI is not installed. Please install it first."
        exit 1
    fi

    # Check if AWS CLI supports SSM
    if ! aws ssm help &> /dev/null; then
        error "AWS CLI does not support SSM commands."
        error "Please update your AWS CLI to the latest version."
        exit 1
    fi

    # Check AWS session validity
    info "Checking AWS session validity..."
    if ! aws sts get-caller-identity --profile "$AWS_PROFILE" &> /dev/null; then
        error "AWS CLI is not configured properly for profile: $AWS_PROFILE"
        error "Your session may have expired. Please run: ./bootstrap/user-mfa-session.sh"
        exit 1
    fi

    # Verify we can access the target instance
    info "Verifying access to GitLab instance..."
    if ! aws ssm describe-instance-information --instance-information-filter-list key=InstanceIds,valueSet="$INSTANCE_ID" --profile "$AWS_PROFILE" --region "$AWS_REGION" &> /dev/null; then
        error "Cannot access instance $INSTANCE_ID via SSM"
        error "Please check your permissions and instance status"
        exit 1
    fi

    log "Prerequisites check passed ✅"
}

# Kill existing port forwarding sessions
cleanup_existing_sessions() {
    log "Cleaning up existing port forwarding sessions..."
    
    # Kill any existing sessions on the same port
    lsof -ti:$LOCAL_PORT | xargs kill -9 2>/dev/null || true
    
    # Wait a moment for cleanup
    sleep 2
    
    log "Cleanup completed ✅"
}

# Start port forwarding session
start_port_forwarding() {
    log "Starting persistent port forwarding session..."
    info "Local port: $LOCAL_PORT"
    info "Remote port: $REMOTE_PORT"
    info "Instance: $INSTANCE_ID"
    
    # Start the port forwarding session
    aws ssm start-session \
        --target "$INSTANCE_ID" \
        --profile "$AWS_PROFILE" \
        --region "$AWS_REGION" \
        --document-name AWS-StartPortForwardingSession \
        --parameters "{\"portNumber\":[\"$REMOTE_PORT\"],\"localPortNumber\":[\"$LOCAL_PORT\"]}"
}

# Test connection
test_connection() {
    local max_attempts=15
    local attempt=1

    log "Testing connection to localhost:$LOCAL_PORT..."

    while [ $attempt -le $max_attempts ]; do
        # Test if port is open
        if nc -z localhost $LOCAL_PORT 2>/dev/null; then
            info "Port $LOCAL_PORT is open, testing HTTP response..."

            # Test HTTP response
            local http_code=$(curl -s -o /dev/null -w "%{http_code}" --connect-timeout 5 http://localhost:$LOCAL_PORT 2>/dev/null || echo "000")

            if [[ "$http_code" =~ ^(200|302|301)$ ]]; then
                log "✅ Connection test successful! (HTTP $http_code)"
                return 0
            elif [[ "$http_code" != "000" ]]; then
                info "Got HTTP $http_code, GitLab might be starting up..."
            fi
        else
            info "Port $LOCAL_PORT not yet available..."
        fi

        warn "Attempt $attempt/$max_attempts failed, retrying in 3 seconds..."
        sleep 3
        ((attempt++))
    done

    error "Connection test failed after $max_attempts attempts"

    # Additional diagnostics
    info "Running diagnostics..."
    if lsof -i :$LOCAL_PORT 2>/dev/null; then
        info "Process is listening on port $LOCAL_PORT"
    else
        warn "No process listening on port $LOCAL_PORT"
    fi

    return 1
}

# Main loop with auto-restart
main_loop() {
    local restart_count=0
    local consecutive_failures=0
    local max_consecutive_failures=3

    while true; do
        log "=== Starting GitLab Tunnel (Restart #$restart_count) ==="

        cleanup_existing_sessions

        # Start port forwarding in background and capture PID
        info "Starting SSM port forwarding session..."
        start_port_forwarding &
        local tunnel_pid=$!

        # Store PID for cleanup
        echo $tunnel_pid > /tmp/gitlab_tunnel.pid

        # Wait for tunnel to establish
        info "Waiting for tunnel to establish..."
        sleep 15

        # Test connection
        if test_connection; then
            log "🎉 GitLab tunnel is active and working!"
            consecutive_failures=0  # Reset failure counter
            echo ""
            info "🌐 GitLab URL: http://localhost:$LOCAL_PORT"
            info "🔐 Username: root"
            info "📝 Use your reset password to login"
            info "🔧 GitLab Manager: ./bootstrap/gitlab-manager.sh"
            echo ""
            log "Monitoring tunnel... (Press Ctrl+C to stop)"

            # Wait for the tunnel process to finish (or fail)
            wait $tunnel_pid
            local exit_code=$?

            if [ $exit_code -ne 0 ]; then
                warn "Tunnel process exited with code $exit_code"
            fi
        else
            warn "Connection test failed, killing tunnel process"
            kill $tunnel_pid 2>/dev/null || true
            ((consecutive_failures++))

            if [ $consecutive_failures -ge $max_consecutive_failures ]; then
                error "Too many consecutive failures ($consecutive_failures). Checking prerequisites..."
                check_prerequisites
                consecutive_failures=0  # Reset after checking prerequisites
            fi
        fi

        # Clean up PID file
        rm -f /tmp/gitlab_tunnel.pid

        ((restart_count++))
        warn "Tunnel disconnected. Restarting in $RESTART_DELAY seconds..."
        sleep $RESTART_DELAY
    done
}

# Handle script interruption
cleanup_and_exit() {
    echo ""
    log "Stopping GitLab tunnel..."

    # Kill tunnel process if PID file exists
    if [ -f /tmp/gitlab_tunnel.pid ]; then
        local tunnel_pid=$(cat /tmp/gitlab_tunnel.pid)
        if kill -0 $tunnel_pid 2>/dev/null; then
            info "Stopping tunnel process (PID: $tunnel_pid)..."
            kill $tunnel_pid 2>/dev/null || true
        fi
        rm -f /tmp/gitlab_tunnel.pid
    fi

    cleanup_existing_sessions
    log "GitLab tunnel stopped."
    exit 0
}

# Set up signal handlers
trap cleanup_and_exit INT TERM

# Main execution
main() {
    log "=== Persistent GitLab Tunnel ==="
    echo ""
    
    check_prerequisites
    
    echo ""
    info "This script will maintain a persistent connection to GitLab"
    info "It will automatically restart if the connection drops"
    info "Keep this terminal open to maintain access"
    echo ""
    
    main_loop
}

# Run main function
main "$@"
