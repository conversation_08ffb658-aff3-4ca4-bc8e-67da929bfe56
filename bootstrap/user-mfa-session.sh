#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Define account information


infovault_PROFILE="infovault"
infovault_MFA_ARN="arn:aws:iam::************:mfa/ShriyaPhone"
infovault_SESSION_FILE="$HOME/.aws/infovault_session_info"
infovault_DESCRIPTION="Infovault"

# Function to display usage
show_usage() {
    echo -e "${RED}Usage: $0${NC}"
    echo -e "The script will prompt you to select an account and enter your MFA token."
    exit 1
}

# Function to prompt for account selection
select_account() {
    echo -e "\n${YELLOW}╔════════════════════════════════════════╗${NC}"
    echo -e "${YELLOW}║${NC}       ${BLUE}AWS ACCOUNT SELECTION${NC}             ${YELLOW}║${NC}"
    echo -e "${YELLOW}╠════════════════════════════════════════╣${NC}"
    echo -e "${YELLOW}║${NC}  ${GREEN}1) Infovault${NC}                          ${YELLOW}║${NC}"
    echo -e "${YELLOW}╚════════════════════════════════════════╝${NC}\n"

    read -p "Enter your choice (1): " choice

    case $choice in
        1)
            ACCOUNT_TYPE="infovault"
            ;;
        *)
            echo -e "${RED}Invalid choice. Please select 1.${NC}"
            exit 1
            ;;
    esac
}

# Function to prompt for region selection
select_region() {
    echo -e "\n${YELLOW}╔════════════════════════════════════════╗${NC}"
    echo -e "${YELLOW}║${NC}       ${BLUE}AWS REGION SELECTION${NC}               ${YELLOW}║${NC}"
    echo -e "${YELLOW}╠════════════════════════════════════════╣${NC}"
    echo -e "${YELLOW}║${NC}  ${GREEN}1) us-east-1 (N. Virginia)${NC}            ${YELLOW}║${NC}"
    echo -e "${YELLOW}║${NC}  ${GREEN}2) us-west-2 (Oregon)${NC}                 ${YELLOW}║${NC}"
    echo -e "${YELLOW}║${NC}  ${GREEN}3) ap-southeast-1 (Singapore)${NC}         ${YELLOW}║${NC}"
    echo -e "${YELLOW}║${NC}  ${GREEN}4) ap-southeast-2 (Sydney)${NC}            ${YELLOW}║${NC}"
    echo -e "${YELLOW}║${NC}  ${GREEN}5) eu-west-1 (Ireland)${NC}                ${YELLOW}║${NC}"
    echo -e "${YELLOW}║${NC}  ${GREEN}6) Other (custom region)${NC}              ${YELLOW}║${NC}"
    echo -e "${YELLOW}╚════════════════════════════════════════╝${NC}\n"

    read -p "Enter your choice (1-6): " region_choice

    case $region_choice in
        1)
            AWS_REGION="us-east-1"
            ;;
        2)
            AWS_REGION="us-west-2"
            ;;
        3)
            AWS_REGION="ap-southeast-1"
            ;;
        4)
            AWS_REGION="ap-southeast-2"
            ;;
        5)
            AWS_REGION="eu-west-1"
            ;;
        6)
            read -p "Enter custom region (e.g., us-east-2): " AWS_REGION
            if [ -z "$AWS_REGION" ]; then
                echo -e "${RED}Error: Region cannot be empty${NC}"
                exit 1
            fi
            ;;
        *)
            echo -e "${RED}Invalid choice. Please select 1-6.${NC}"
            exit 1
            ;;
    esac

    echo -e "${GREEN}Selected region: $AWS_REGION${NC}"
}

# Function to validate token code
validate_token() {
    if ! [[ $TOKEN_CODE =~ ^[0-9]{6}$ ]]; then
        echo -e "${RED}Error: Token code must be 6 digits${NC}"
        exit 1
    fi
}

# Function to get AWS session token
get_session_token() {
    local profile=$1
    local mfa_arn=$2

    echo -e "${BLUE}Using AWS profile: $profile${NC}"
    echo -e "${BLUE}Using AWS region: $AWS_REGION${NC}"
    echo -e "${BLUE}Logging in to AWS with token code: $TOKEN_CODE${NC}"

    STS_RESPONSE=$(aws sts get-session-token \
        --serial-number $mfa_arn \
        --token-code "$TOKEN_CODE" \
        --profile $profile \
        --region $AWS_REGION \
        --output json)

    if [ $? -ne 0 ]; then
        echo -e "${RED}Error: Failed to get AWS session token${NC}"
        exit 1
    fi

    # Extract credentials
    AWS_ACCESS_KEY_ID=$(echo "$STS_RESPONSE" | jq -r '.Credentials.AccessKeyId')
    AWS_SECRET_ACCESS_KEY=$(echo "$STS_RESPONSE" | jq -r '.Credentials.SecretAccessKey')
    AWS_SESSION_TOKEN=$(echo "$STS_RESPONSE" | jq -r '.Credentials.SessionToken')
    EXPIRATION=$(echo "$STS_RESPONSE" | jq -r '.Credentials.Expiration')

    # Export credentials to current shell
    export AWS_ACCESS_KEY_ID
    export AWS_SECRET_ACCESS_KEY
    export AWS_SESSION_TOKEN
    export AWS_DEFAULT_REGION=$AWS_REGION
}

# Function to display session information
display_session_info() {
    echo -e "${GREEN}Successfully obtained temporary credentials!${NC}"
    echo -e "${YELLOW}Session will expire at: $EXPIRATION${NC}"

    # Calculate session duration
    EXPIRATION_EPOCH=$(date -j -f "%Y-%m-%dT%H:%M:%SZ" "$EXPIRATION" +%s 2>/dev/null)
    NOW_EPOCH=$(date +%s)

    if [ -n "$EXPIRATION_EPOCH" ]; then
        DURATION_SECONDS=$((EXPIRATION_EPOCH - NOW_EPOCH))
        DURATION_HOURS=$((DURATION_SECONDS / 3600))
        DURATION_MINUTES=$(((DURATION_SECONDS % 3600) / 60))

        echo -e "${YELLOW}Session duration: ${DURATION_HOURS}h ${DURATION_MINUTES}m (${DURATION_SECONDS}s)${NC}"
    fi
}

# Function to test AWS connection
test_connection() {
    local profile=$1

    echo -e "${BLUE}Testing AWS connection in $AWS_REGION...${NC}"
    BUCKET_COUNT=$(aws s3 ls --profile $profile --region $AWS_REGION | wc -l)
    echo -e "${GREEN}Found $BUCKET_COUNT S3 buckets in $AWS_REGION${NC}"
}

# Function to save session information
save_session_info() {
    local session_file=$1
    local description=$2
    local profile=$3

    mkdir -p "$HOME/.aws"

    cat > "$session_file" << EOF
# $description AWS Session Information
# Generated on $(date)
# Using profile: $profile
# Using region: $AWS_REGION
export AWS_ACCESS_KEY_ID=$AWS_ACCESS_KEY_ID
export AWS_SECRET_ACCESS_KEY=$AWS_SECRET_ACCESS_KEY
export AWS_SESSION_TOKEN=$AWS_SESSION_TOKEN
export AWS_DEFAULT_REGION=$AWS_REGION
# Session expires at: $EXPIRATION
EOF

    echo -e "${BLUE}Session information saved to $session_file${NC}"
    echo -e "${YELLOW}To restore this session in a new terminal, run:${NC}"
    echo -e "${GREEN}source $session_file${NC}"

    # Source the session file in the current shell
    # Note: This will only affect the subshell, not the parent shell
    source $session_file

    # Set AWS CLI environment variables directly
    # This will be available in the current subshell
    export AWS_PROFILE=$profile
}

# Function to check if a valid session exists for the account
check_existing_session() {
    local session_file=$1
    if [ -f "$session_file" ]; then
        # Source the session file to get EXPIRATION
        source "$session_file"
        # Extract expiration from the file (last line)
        EXPIRATION_LINE=$(grep 'Session expires at:' "$session_file" | tail -1)
        EXPIRATION=$(echo "$EXPIRATION_LINE" | awk -F': ' '{print $2}')
        if [ -n "$EXPIRATION" ]; then
            EXPIRATION_EPOCH=$(date -j -f "%Y-%m-%dT%H:%M:%SZ" "$EXPIRATION" +%s 2>/dev/null)
            NOW_EPOCH=$(date +%s)
            if [ -n "$EXPIRATION_EPOCH" ] && [ "$EXPIRATION_EPOCH" -gt "$NOW_EPOCH" ]; then
                echo -e "${GREEN}A valid AWS session already exists for this account!${NC}"
                echo -e "${YELLOW}Session will expire at: $EXPIRATION${NC}"
                echo -e "${YELLOW}To use this session, run:${NC}"
                echo -e "${GREEN}[ -f $session_file ] && source $session_file${NC}"
                exit 0
            fi
        fi
    fi
}

# Check if any arguments were provided (show usage if so)
if [ $# -ne 0 ]; then
    show_usage
fi

# First, prompt for account selection
select_account

# Then, prompt for region selection
select_region

# After account and region selection, check for existing session
if [ "$ACCOUNT_TYPE" = "infovault" ]; then
    check_existing_session $infovault_SESSION_FILE
fi

# Then, prompt for MFA token
echo -e "\n${YELLOW}╔════════════════════════════════════════╗${NC}"
echo -e "${YELLOW}║${NC}       ${BLUE}INFOVAULT MFA AUTHENTICATION${NC}       ${YELLOW}║${NC}"
echo -e "${YELLOW}║${NC}       ${BLUE}Region: $AWS_REGION${NC}$(printf "%*s" $((18 - ${#AWS_REGION})) "")${YELLOW}║${NC}"
echo -e "${YELLOW}╚════════════════════════════════════════╝${NC}\n"
read -p "Enter your 6-digit MFA token: " TOKEN_CODE

# Validate token code
validate_token

# Process based on account type
if [ "$ACCOUNT_TYPE" = "infovault" ]; then
    get_session_token $infovault_PROFILE $infovault_MFA_ARN
    display_session_info
    test_connection $infovault_PROFILE
    save_session_info $infovault_SESSION_FILE "$infovault_DESCRIPTION" $infovault_PROFILE
fi

# After successful authentication and saving session info
# Set session_file variable for the selected account
if [ "$ACCOUNT_TYPE" = "infovault" ]; then
    session_file=$infovault_SESSION_FILE
fi

echo -e "${GREEN}Authentication completed successfully!${NC}"
echo -e "\n${YELLOW}IMPORTANT:${NC} To use these credentials in your current shell, run:"
echo -e "${GREEN}[ -f $session_file ] && source $session_file${NC}"
echo -e "This will activate your AWS session in the current terminal."
echo -e "Or add this line to your ~/.bashrc or ~/.zshrc file to automatically load the session:"
echo -e "${GREEN}[ -f $session_file ] && source $session_file${NC}\n"
