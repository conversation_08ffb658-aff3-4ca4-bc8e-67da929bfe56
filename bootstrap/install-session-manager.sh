#!/bin/bash

# AWS Session Manager Plugin Installer for macOS
# This script installs the AWS Session Manager plugin required for SSM tunneling

set -e

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

info() {
    echo -e "${BLUE}[INFO] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
}

# Check if running on macOS
check_os() {
    if [[ "$OSTYPE" != "darwin"* ]]; then
        error "This script is designed for macOS only"
        exit 1
    fi
    log "✅ Running on macOS"
}

# Check if session manager plugin is already installed
check_existing_installation() {
    if command -v session-manager-plugin &> /dev/null; then
        log "✅ Session Manager plugin is already installed"
        session-manager-plugin --version
        return 0
    fi
    
    # Check in common locations
    local common_paths=(
        "/usr/local/bin/session-manager-plugin"
        "$HOME/.local/bin/session-manager-plugin"
        "/opt/homebrew/bin/session-manager-plugin"
    )
    
    for path in "${common_paths[@]}"; do
        if [ -f "$path" ]; then
            log "✅ Found session manager plugin at: $path"
            export PATH="$HOME/.local/bin:/usr/local/bin:/opt/homebrew/bin:$PATH"
            return 0
        fi
    done
    
    return 1
}

# Install session manager plugin
install_session_manager() {
    log "Installing AWS Session Manager plugin..."
    
    # Create local bin directory
    mkdir -p "$HOME/.local/bin"
    
    # Download the plugin
    info "Downloading Session Manager plugin..."
    curl -o /tmp/sessionmanager-bundle.zip \
        "https://s3.amazonaws.com/session-manager-downloads/plugin/latest/mac/sessionmanager-bundle.zip"
    
    # Extract and install
    info "Extracting and installing..."
    cd /tmp
    unzip -o sessionmanager-bundle.zip
    
    # Install to user's local bin
    sudo ./sessionmanager-bundle/install -i /usr/local/sessionmanagerplugin -b /usr/local/bin/session-manager-plugin
    
    # Also create a symlink in user's local bin for backup
    ln -sf /usr/local/bin/session-manager-plugin "$HOME/.local/bin/session-manager-plugin"
    
    # Clean up
    rm -rf /tmp/sessionmanager-bundle.zip /tmp/sessionmanager-bundle
    
    log "✅ Session Manager plugin installed successfully"
}

# Verify installation
verify_installation() {
    # Update PATH to include common locations
    export PATH="$HOME/.local/bin:/usr/local/bin:/opt/homebrew/bin:$PATH"
    
    if command -v session-manager-plugin &> /dev/null; then
        log "✅ Installation verified successfully"
        info "Version: $(session-manager-plugin --version)"
        return 0
    else
        error "❌ Installation verification failed"
        return 1
    fi
}

# Main execution
main() {
    log "=== AWS Session Manager Plugin Installer ==="
    echo ""
    
    check_os
    
    if check_existing_installation; then
        info "Session Manager plugin is already available"
        exit 0
    fi
    
    info "Session Manager plugin not found, installing..."
    install_session_manager
    
    if verify_installation; then
        echo ""
        log "🎉 Installation completed successfully!"
        echo ""
        info "You can now use AWS SSM port forwarding with:"
        info "aws ssm start-session --target <instance-id> --document-name AWS-StartPortForwardingSession"
        echo ""
    else
        error "Installation failed. Please check the logs and try again."
        exit 1
    fi
}

# Run main function
main "$@"
