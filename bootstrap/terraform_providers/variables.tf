# Variables for InfoVault DEV Environment
# Use Case 4: Setup / Manage AWS Infrastructure (DEV)

variable "environment" {
  description = "Environment name"
  type        = string
  default     = "dev"
}

variable "aws_region" {
  description = "AWS region for resources"
  type        = string
  default     = "ap-southeast-1"
}

variable "project_name" {
  description = "Project name for resource naming"
  type        = string
  default     = "infovault"
}

# S3 Configuration
variable "s3_bucket_name" {
  description = "Name for the InfoVault application S3 bucket (auto-generated if empty)"
  type        = string
  default     = ""
}

variable "s3_force_destroy" {
  description = "Allow force destroy of S3 bucket (for dev/testing environments)"
  type        = bool
  default     = true
}

# S3 Versioning
variable "enable_s3_versioning" {
  description = "Enable versioning on S3 bucket"
  type        = bool
  default     = true
}

# S3 Encryption
variable "s3_encryption_algorithm" {
  description = "Server-side encryption algorithm (AES256 or aws:kms)"
  type        = string
  default     = "AES256"
}

variable "s3_kms_key_id" {
  description = "KMS key ID for encryption (required if encryption_algorithm is aws:kms)"
  type        = string
  default     = null
}

variable "s3_bucket_key_enabled" {
  description = "Enable S3 bucket key for KMS encryption"
  type        = bool
  default     = true
}

# S3 Public Access Block
variable "s3_block_public_acls" {
  description = "Block public ACLs"
  type        = bool
  default     = true
}

variable "s3_block_public_policy" {
  description = "Block public bucket policies"
  type        = bool
  default     = true
}

variable "s3_ignore_public_acls" {
  description = "Ignore public ACLs"
  type        = bool
  default     = true
}

variable "s3_restrict_public_buckets" {
  description = "Restrict public bucket policies"
  type        = bool
  default     = true
}

# S3 Lifecycle Management
variable "s3_lifecycle_enabled" {
  description = "Enable lifecycle management"
  type        = bool
  default     = true
}

variable "s3_transition_to_ia_days" {
  description = "Number of days after which to transition objects to Standard-IA"
  type        = number
  default     = 30
}

variable "s3_transition_to_glacier_days" {
  description = "Number of days after which to transition objects to Glacier"
  type        = number
  default     = 90
}

variable "s3_expiration_days" {
  description = "Number of days after which to expire objects (0 = disabled)"
  type        = number
  default     = 365
}

variable "s3_multipart_upload_days" {
  description = "Number of days after which to abort incomplete multipart uploads"
  type        = number
  default     = 7
}

# S3 Logging
variable "s3_logging_enabled" {
  description = "Enable S3 access logging"
  type        = bool
  default     = false
}

variable "s3_logging_target_bucket" {
  description = "Target bucket for access logs"
  type        = string
  default     = ""
}

variable "s3_logging_target_prefix" {
  description = "Prefix for access log objects"
  type        = string
  default     = "access-logs/"
}

# S3 Notifications
variable "s3_notification_enabled" {
  description = "Enable S3 bucket notifications"
  type        = bool
  default     = false
}

# Common Tags
variable "common_tags" {
  description = "Common tags to apply to all resources"
  type        = map(string)
  default = {
    Project     = "InfoVault"
    Environment = "dev"
    ManagedBy   = "Terraform"
    UseCase     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
  }
}
