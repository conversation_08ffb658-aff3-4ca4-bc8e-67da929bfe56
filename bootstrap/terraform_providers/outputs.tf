# Outputs for InfoVault DEV Environment
# Use Case 4: Setup / Manage AWS Infrastructure (DEV)

# S3 Bucket Outputs
output "s3_bucket_name" {
  description = "Name of the InfoVault application data S3 bucket"
  value       = module.app_data_bucket.bucket_name
}

output "s3_bucket_arn" {
  description = "ARN of the InfoVault application data S3 bucket"
  value       = module.app_data_bucket.bucket_arn
}

output "s3_bucket_domain_name" {
  description = "Domain name of the S3 bucket"
  value       = module.app_data_bucket.bucket_domain_name
}

output "s3_bucket_regional_domain_name" {
  description = "Regional domain name of the S3 bucket"
  value       = module.app_data_bucket.bucket_regional_domain_name
}

output "s3_bucket_region" {
  description = "Region where the S3 bucket is created"
  value       = module.app_data_bucket.bucket_region
}

output "s3_bucket_hosted_zone_id" {
  description = "Route 53 Hosted Zone ID for the S3 bucket"
  value       = module.app_data_bucket.bucket_hosted_zone_id
}

# Environment Information
output "environment" {
  description = "Environment name"
  value       = var.environment
}

output "aws_region" {
  description = "AWS region where resources are deployed"
  value       = var.aws_region
}

# Resource Summary
output "infrastructure_summary" {
  description = "Summary of deployed infrastructure"
  value = {
    environment       = var.environment
    region            = var.aws_region
    s3_bucket         = module.app_data_bucket.bucket_name
    s3_versioning     = module.app_data_bucket.versioning_enabled
    s3_encryption     = module.app_data_bucket.encryption_algorithm
    s3_lifecycle      = module.app_data_bucket.lifecycle_enabled
    s3_public_blocked = module.app_data_bucket.public_access_blocked
    deployed_at       = timestamp()
  }
}
