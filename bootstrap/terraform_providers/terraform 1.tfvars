# InfoVault DEV Environment Configuration
# Use Case 4: Setup / Manage AWS Infrastructure (DEV)

# Environment Configuration
environment  = "dev"
aws_region   = "ap-southeast-1"
project_name = "infovault"

# S3 Bucket Configuration
s3_bucket_name   = ""   # Auto-generated: infovault-dev-app-data-{random}
s3_force_destroy = true # Allow destruction in DEV environment

# S3 Versioning
enable_s3_versioning = true

# S3 Encryption
s3_encryption_algorithm = "AES256"
s3_kms_key_id           = null
s3_bucket_key_enabled   = true

# S3 Public Access Block (Security Best Practices)
s3_block_public_acls       = true
s3_block_public_policy     = true
s3_ignore_public_acls      = true
s3_restrict_public_buckets = true

# S3 Lifecycle Management
s3_lifecycle_enabled          = true
s3_transition_to_ia_days      = 30  # Move to Standard-IA after 30 days
s3_transition_to_glacier_days = 90  # Move to Glacier after 90 days
s3_expiration_days            = 365 # Delete after 1 year
s3_multipart_upload_days      = 7   # Clean up incomplete uploads after 7 days

# S3 Logging (Disabled for DEV)
s3_logging_enabled       = false
s3_logging_target_bucket = ""
s3_logging_target_prefix = "access-logs/"

# S3 Notifications (Disabled for DEV)
s3_notification_enabled = false

# Common Tags
common_tags = {
  Project     = "InfoVault"
  Environment = "dev"
  ManagedBy   = "Terraform"
  UseCase     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
  Owner       = "InfoVault-IAC-Team"
  CostCenter  = "InfoVault-Development"
  Backup      = "Required"
  Monitoring  = "Enabled"
}
