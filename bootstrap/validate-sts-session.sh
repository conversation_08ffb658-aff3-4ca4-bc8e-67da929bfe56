#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# InfoVault session configuration
INFOVAULT_SESSION_FILE="$HOME/.aws/infovault_session_info"
INFOVAULT_PROFILE="infovault"

# Function to display script header
show_header() {
    echo -e "\n${CYAN}╔════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${CYAN}║${NC}              ${BLUE}InfoVault AWS STS Session Validator${NC}              ${CYAN}║${NC}"
    echo -e "${CYAN}╚════════════════════════════════════════════════════════════╝${NC}\n"
}

# Function to check if session file exists
check_session_file() {
    if [ ! -f "$INFOVAULT_SESSION_FILE" ]; then
        echo -e "${RED}❌ No InfoVault session file found at: $INFOVAULT_SESSION_FILE${NC}"
        echo -e "${YELLOW}💡 Run the MFA session script first: ./bootstrap/user-mfa-session.sh${NC}"
        return 1
    fi
    echo -e "${GREEN}✅ Session file found: $INFOVAULT_SESSION_FILE${NC}"
    return 0
}

# Function to load session credentials
load_session() {
    echo -e "${BLUE}🔄 Loading session credentials...${NC}"
    source "$INFOVAULT_SESSION_FILE"
    
    if [ -z "$AWS_ACCESS_KEY_ID" ] || [ -z "$AWS_SECRET_ACCESS_KEY" ] || [ -z "$AWS_SESSION_TOKEN" ]; then
        echo -e "${RED}❌ Invalid session file - missing required credentials${NC}"
        return 1
    fi
    
    echo -e "${GREEN}✅ Session credentials loaded${NC}"
    return 0
}

# Function to check session expiration
check_expiration() {
    echo -e "${BLUE}🕐 Checking session expiration...${NC}"
    
    # Extract expiration from session file
    EXPIRATION_LINE=$(grep 'Session expires at:' "$INFOVAULT_SESSION_FILE" | tail -1)
    EXPIRATION=$(echo "$EXPIRATION_LINE" | awk -F': ' '{print $2}')
    
    if [ -z "$EXPIRATION" ]; then
        echo -e "${YELLOW}⚠️  Could not determine session expiration time${NC}"
        return 1
    fi
    
    # Convert expiration to epoch time
    EXPIRATION_EPOCH=$(date -j -f "%Y-%m-%dT%H:%M:%S+00:00" "$EXPIRATION" +%s 2>/dev/null)
    NOW_EPOCH=$(date +%s)
    
    if [ -z "$EXPIRATION_EPOCH" ]; then
        echo -e "${YELLOW}⚠️  Could not parse expiration time: $EXPIRATION${NC}"
        return 1
    fi
    
    # Calculate time remaining
    TIME_REMAINING=$((EXPIRATION_EPOCH - NOW_EPOCH))
    
    if [ "$TIME_REMAINING" -le 0 ]; then
        echo -e "${RED}❌ Session has expired at: $EXPIRATION${NC}"
        return 1
    fi
    
    # Convert to human readable format
    HOURS=$((TIME_REMAINING / 3600))
    MINUTES=$(((TIME_REMAINING % 3600) / 60))
    SECONDS=$((TIME_REMAINING % 60))
    
    echo -e "${GREEN}✅ Session is valid until: $EXPIRATION${NC}"
    echo -e "${CYAN}⏰ Time remaining: ${HOURS}h ${MINUTES}m ${SECONDS}s${NC}"
    return 0
}

# Function to validate STS credentials
validate_sts() {
    echo -e "${BLUE}🔍 Validating STS credentials...${NC}"
    
    # Get caller identity
    CALLER_IDENTITY=$(aws sts get-caller-identity --output json 2>/dev/null)
    
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ Failed to validate STS credentials${NC}"
        echo -e "${YELLOW}💡 Your session may have expired or credentials are invalid${NC}"
        return 1
    fi
    
    # Parse identity information
    USER_ID=$(echo "$CALLER_IDENTITY" | jq -r '.UserId' 2>/dev/null)
    ACCOUNT=$(echo "$CALLER_IDENTITY" | jq -r '.Account' 2>/dev/null)
    ARN=$(echo "$CALLER_IDENTITY" | jq -r '.Arn' 2>/dev/null)
    
    echo -e "${GREEN}✅ STS credentials are valid${NC}"
    echo -e "${CYAN}👤 User ID: $USER_ID${NC}"
    echo -e "${CYAN}🏢 Account: $ACCOUNT${NC}"
    echo -e "${CYAN}🔗 ARN: $ARN${NC}"
    
    return 0
}

# Function to test AWS services access
test_services() {
    echo -e "${BLUE}🧪 Testing AWS services access...${NC}"
    
    # Test S3 access
    echo -e "${BLUE}  📦 Testing S3 access...${NC}"
    S3_RESULT=$(aws s3 ls 2>/dev/null)
    if [ $? -eq 0 ]; then
        BUCKET_COUNT=$(echo "$S3_RESULT" | wc -l | tr -d ' ')
        echo -e "${GREEN}  ✅ S3 access: Found $BUCKET_COUNT buckets${NC}"
    else
        echo -e "${RED}  ❌ S3 access: Failed${NC}"
    fi
    
    # Test EC2 access
    echo -e "${BLUE}  🖥️  Testing EC2 access...${NC}"
    EC2_RESULT=$(aws ec2 describe-regions --region ap-southeast-1 2>/dev/null)
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}  ✅ EC2 access: Working${NC}"
    else
        echo -e "${RED}  ❌ EC2 access: Failed${NC}"
    fi
    
    # Test IAM access (read-only)
    echo -e "${BLUE}  👥 Testing IAM access...${NC}"
    IAM_RESULT=$(aws iam get-user 2>/dev/null)
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}  ✅ IAM access: Working${NC}"
    else
        echo -e "${YELLOW}  ⚠️  IAM access: Limited or failed${NC}"
    fi
}

# Function to display session summary
show_summary() {
    echo -e "\n${CYAN}╔════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${CYAN}║${NC}                        ${BLUE}Session Summary${NC}                        ${CYAN}║${NC}"
    echo -e "${CYAN}╠════════════════════════════════════════════════════════════╣${NC}"
    echo -e "${CYAN}║${NC} Profile: $INFOVAULT_PROFILE                                      ${CYAN}║${NC}"
    echo -e "${CYAN}║${NC} Session File: ~/.aws/infovault_session_info                ${CYAN}║${NC}"
    echo -e "${CYAN}║${NC} Region: ap-southeast-1                                     ${CYAN}║${NC}"
    echo -e "${CYAN}╚════════════════════════════════════════════════════════════╝${NC}"
    
    echo -e "\n${YELLOW}💡 To use this session in a new terminal:${NC}"
    echo -e "${GREEN}source ~/.aws/infovault_session_info${NC}"
    
    echo -e "\n${YELLOW}💡 To refresh your session when it expires:${NC}"
    echo -e "${GREEN}./bootstrap/user-mfa-session.sh${NC}"
}

# Main execution
main() {
    show_header
    
    # Check if session file exists
    if ! check_session_file; then
        exit 1
    fi
    
    # Load session credentials
    if ! load_session; then
        exit 1
    fi
    
    # Check expiration
    if ! check_expiration; then
        echo -e "\n${YELLOW}💡 Your session has expired. Please run the MFA script to get a new session:${NC}"
        echo -e "${GREEN}./bootstrap/user-mfa-session.sh${NC}"
        exit 1
    fi
    
    # Validate STS credentials
    if ! validate_sts; then
        exit 1
    fi
    
    # Test services
    test_services
    
    # Show summary
    show_summary
    
    echo -e "\n${GREEN}🎉 Session validation completed successfully!${NC}\n"
}

# Run main function
main "$@"
