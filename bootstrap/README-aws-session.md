# InfoVault AWS Session Management

This directory contains scripts to manage AWS MFA sessions for the InfoVault account.

## Scripts

### 1. `user-mfa-session.sh` - Create MFA Session
Creates a new AWS STS session using MFA authentication.

**Usage:**
```bash
./bootstrap/user-mfa-session.sh
```

**What it does:**
- Prompts you to select InfoVault account (option 3)
- Asks for your 6-digit MFA token
- Creates temporary AWS credentials (valid for ~4 hours)
- Saves session to `~/.aws/infovault_session_info`
- Tests S3 access to verify the session works

### 2. `validate-sts-session.sh` - Validate Current Session
Checks if your current AWS session is still valid and provides detailed information.

**Usage:**
```bash
./bootstrap/validate-sts-session.sh
```

**What it does:**
- Checks if session file exists
- Validates session hasn't expired
- Tests STS credentials with AWS
- Tests access to S3, EC2, and IAM services
- Shows remaining session time
- Displays account and user information

## Configuration

### AWS Profile: `infovault`
- **Account ID:** ************
- **Region:** ap-southeast-1
- **MFA Device:** arn:aws:iam::************:mfa/ShriyaPhone

### Files
- **Credentials:** `~/.aws/credentials` (contains base access keys)
- **Config:** `~/.aws/config` (contains profile settings)
- **Session:** `~/.aws/infovault_session_info` (contains temporary MFA session)
- **Environment:** `bootstrap/.env` (contains formatted AWS variables)

## Quick Commands

### Start a new MFA session:
```bash
cd /Users/<USER>/Documents/Project/InfoVault
./bootstrap/user-mfa-session.sh
```

### Check current session status:
```bash
cd /Users/<USER>/Documents/Project/InfoVault
./bootstrap/validate-sts-session.sh
```

### Use session in a new terminal:
```bash
source ~/.aws/infovault_session_info
```

### Verify AWS CLI is working:
```bash
aws sts get-caller-identity
aws s3 ls
```

## Troubleshooting

### Session Expired
If you get authentication errors, your session may have expired. Run:
```bash
./bootstrap/validate-sts-session.sh
```

If expired, create a new session:
```bash
./bootstrap/user-mfa-session.sh
```

### Permission Denied
If you get permission denied errors, make scripts executable:
```bash
chmod +x bootstrap/user-mfa-session.sh
chmod +x bootstrap/validate-sts-session.sh
```

### AWS CLI Not Found
Install AWS CLI if not present:
```bash
brew install awscli
```

## Session Duration
- **Default:** 4 hours (14,400 seconds)
- **Maximum:** 12 hours (with appropriate IAM policies)
- **Automatic expiry:** Sessions automatically expire and cannot be renewed

## Security Notes
- Never commit AWS credentials to version control
- Session files contain temporary credentials that expire automatically
- MFA tokens are single-use and expire after 30 seconds
- Base credentials in `~/.aws/credentials` should be kept secure
