#!/usr/bin/env ruby

# GitLab User Creation Script
# This script creates a new user 'shriya' and makes her a maintainer

puts "🚀 Creating GitLab user 'shriya'..."

# Create the user
user = User.new(
  email: '<EMAIL>',
  name: '<PERSON><PERSON>',
  username: 'shriya',
  password: 'TempPassword123!',
  password_confirmation: 'TempPassword123!',
  confirmed_at: Time.current,
  admin: false
)

if user.save
  puts "✅ User 'shriya' created successfully!"
  puts "   Email: #{user.email}"
  puts "   Username: #{user.username}"
  puts "   ID: #{user.id}"
else
  puts "❌ Failed to create user:"
  user.errors.full_messages.each { |msg| puts "   - #{msg}" }
  exit 1
end

# Find or create a project (assuming there's a project or we'll create one)
project_name = "infovault-project"
namespace = Namespace.find_by(path: 'root') || user.namespace

project = Project.find_by(name: project_name) || Project.create!(
  name: project_name,
  path: project_name.downcase.gsub(/[^a-z0-9\-_]/, '-'),
  namespace: namespace,
  creator: User.find_by(username: 'root'),
  visibility_level: Gitlab::VisibilityLevel::PRIVATE
)

if project.persisted?
  puts "✅ Project '#{project_name}' ready!"
  puts "   Project ID: #{project.id}"
  puts "   Path: #{project.full_path}"
else
  puts "❌ Failed to create/find project:"
  project.errors.full_messages.each { |msg| puts "   - #{msg}" }
end

# Add user as maintainer to the project
member = project.add_maintainer(user)

if member.persisted?
  puts "✅ User 'shriya' added as maintainer to '#{project_name}'!"
  puts "   Access Level: #{member.human_access}"
else
  puts "❌ Failed to add user as maintainer:"
  member.errors.full_messages.each { |msg| puts "   - #{msg}" }
end

# Make user admin (optional - comment out if you don't want admin privileges)
user.update!(admin: true)
puts "✅ User 'shriya' granted admin privileges!"

puts ""
puts "🎉 Setup completed successfully!"
puts ""
puts "📋 User Details:"
puts "   Username: shriya"
puts "   Email: <EMAIL>"
puts "   Password: TempPassword123!"
puts "   Role: Admin & Project Maintainer"
puts ""
puts "🔐 Next Steps:"
puts "   1. Login to GitLab at http://localhost:8080"
puts "   2. Use username 'shriya' and password 'TempPassword123!'"
puts "   3. Change password on first login"
puts "   4. Start using GitLab!"
puts ""
